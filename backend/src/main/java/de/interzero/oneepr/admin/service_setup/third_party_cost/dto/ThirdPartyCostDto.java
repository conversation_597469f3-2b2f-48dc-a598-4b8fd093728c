package de.interzero.oneepr.admin.service_setup.third_party_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;

/**
 * Data Transfer Object for third party cost responses.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ThirdPartyCostDto {

    @JsonProperty("id")
    @Schema(description = "Unique identifier of the third party cost")
    private Integer id;

    @JsonProperty("name")
    @Schema(
            description = "Name of the third party cost",
            example = "Registration Fee"
    )
    private String name;

    @JsonProperty("price")
    @Schema(
            description = "Price of the third party cost in cents",
            example = "5000"
    )
    private Integer price;

    @JsonProperty("country_id")
    @Schema(description = "ID of the associated country")
    private Integer countryId;

    @JsonProperty("created_at")
    @Schema(description = "Timestamp when the cost was created")
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(description = "Timestamp when the cost was last updated")
    private Instant updatedAt;
}
