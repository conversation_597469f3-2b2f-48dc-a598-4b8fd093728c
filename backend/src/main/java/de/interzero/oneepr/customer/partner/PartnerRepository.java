package de.interzero.oneepr.customer.partner;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for managing {@link Partner} entities.
 */
@Repository
public interface PartnerRepository extends JpaRepository<Partner, Integer> {

    /**
     * Finds the first partner by email address, ignoring case, and not marked as deleted.
     *
     * @param email the email of the partner
     * @return an {@link Optional} containing the partner if found, or empty if not
     */
    Optional<Partner> findFirstByEmailIgnoreCaseAndDeletedAtIsNull(String email);

    /**
     * Finds the first partner by associated user ID, excluding soft-deleted records.
     *
     * @param userId the ID of the associated user
     * @return an {@link Optional} containing the partner if found, or empty if not
     */

    Optional<Partner> findFirstByUserIdAndDeletedAtIsNull(Integer userId);

    /**
     * Retrieves a partner by ID along with all related entities.
     * <p>
     * This method eagerly fetches the following associations:
     * <ul>
     *   <li>partnerBanking</li>
     *   <li>partnerContract (and its files and changes)</li>
     *   <li>companies (and their addresses and contacts)</li>
     *   <li>coupons (and their uses)</li>
     *   <li>marketingMaterialPartners (and their marketingMaterials)</li>
     * </ul>
     *
     * @param id the ID of the partner
     * @return an {@link Optional} containing the partner with all associations, or empty if not found
     */
    @Query(
            """
                        SELECT p FROM Partner p
                        LEFT JOIN FETCH p.partnerBanking
                        LEFT JOIN FETCH p.partnerContract pc
                        LEFT JOIN FETCH pc.files
                        LEFT JOIN FETCH pc.changes
                        LEFT JOIN FETCH p.companies c
                        LEFT JOIN FETCH c.address
                        LEFT JOIN FETCH c.contacts
                        LEFT JOIN FETCH p.coupons cp
                        LEFT JOIN FETCH cp.coupon coupon
                        LEFT JOIN FETCH coupon.couponUses
                        LEFT JOIN FETCH p.marketingMaterialPartners mmp
                        LEFT JOIN FETCH mmp.marketingMaterial
                        WHERE p.id = :id AND p.deletedAt IS NULL
                    """
    )
    Optional<Partner> findByIdAndDeletedAtIsNullWithAllRelations(@Param("id") Integer id);

    /**
     * <p>Finds a partner by ID where the partner has not been soft-deleted.</p>
     *
     * @param id the ID of the partner
     * @return an Optional containing the {@link Partner} if found and not soft-deleted, or empty otherwise
     */
    Optional<Partner> findByIdAndDeletedAtIsNull(Integer id);
}
