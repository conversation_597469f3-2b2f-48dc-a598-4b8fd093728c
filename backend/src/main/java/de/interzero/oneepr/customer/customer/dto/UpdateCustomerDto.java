package de.interzero.oneepr.customer.customer.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Fields required to update a user.
 * Inherits all fields from CreateCustomerDto. All fields are optional.
 */
@Schema(description = "Fields required to update a customer")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateCustomerDto extends CreateCustomerDto {

    @Schema(
            description = "The VAT ID of the customer",
            nullable = true
    )
    @JsonProperty("vat")
    private String vat;

    @Schema(
            description = "The TIN (Tax ID) of the customer",
            nullable = true
    )
    @JsonProperty("tin")
    private String tin;

    @Schema(
            description = "The address of the customer",
            nullable = true
    )
    @JsonProperty("address")
    private AddressDto address;
}
