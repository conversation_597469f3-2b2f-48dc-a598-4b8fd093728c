import { ApiProperty } from "@nestjs/swagger";

class ServiceRevenueMonthPerformanceOutputDto {
  @ApiProperty()
  month: number;
  @ApiProperty()
  result: number;
}

export class ServiceRevenueOvertimeDto {
  @ApiProperty()
  total: number;

  @ApiProperty()
  setup_packaging_service_id: number;

  @ApiProperty({ isArray: true, type: ServiceRevenueMonthPerformanceOutputDto })
  month_perfomances: ServiceRevenueMonthPerformanceOutputDto[];

  @ApiProperty()
  label: string;
}

export class ServicesRevenueOvertimeOutputDto {
  data: ServiceRevenueOvertimeDto[];
}
