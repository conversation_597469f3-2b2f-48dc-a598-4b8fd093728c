import { Module } from "@nestjs/common";
import { PartnerService } from "./partner.service";
import { PartnerController } from "./partner.controller";
import { HttpApiModule } from "@/http/http.module";
import { FileService } from "@/file/file.service";
import { MarketingMaterialsService } from "@/marketing-materials/marketing-materials.service";

@Module({
  imports: [HttpApiModule],
  controllers: [PartnerController],
  providers: [PartnerService, FileService, MarketingMaterialsService],
})
export class PartnerModule {}
