{"compilerOptions": {"target": "ES2021", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "ts-node": {"compilerOptions": {"module": "ESNext", "moduleResolution": "Node"}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/components/modules/dashboard/sections/services-revenue-overtime", "src/components/modules/partner-hub/partner-overview/convert-partners-table-CSV.ts"], "exclude": ["node_modules", "cypress.config.ts"]}