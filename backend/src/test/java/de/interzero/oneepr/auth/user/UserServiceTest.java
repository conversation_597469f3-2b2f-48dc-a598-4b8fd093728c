package de.interzero.oneepr.auth.user;

import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.role.RoleRepository;
import de.interzero.oneepr.auth.user.dto.ResetPasswordDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
@Transactional
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordResetRequestRepository passwordResetRequestRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @BeforeEach
    void setUp() {
        passwordResetRequestRepository.deleteAll();
        userRepository.deleteAll();
    }

    /**
     * Verifies that a valid PASSWORD_RESET token leads to a successful password update in the database.
     * <p>
     * Steps:
     * - Create a test user and a valid PasswordResetRequest
     * - Call {@link UserService#resetPassword(ResetPasswordDto)} with the new password
     * - Assert that the password is stored hashed in the database
     */
    @Test
    void resetPassword_shouldStoreHashedPasswordInDatabase_withPasswordResetToken() {
        Role role = roleRepository.findByName("CUSTOMER").orElseThrow();

        User user = new User();
        user.setEmail("<EMAIL>");
        user.setName("ResetTest");
        user.setPassword(passwordEncoder.encode("oldPassword"));
        user.setRole(role);
        user.setIsActive(true);
        user.setStatus(User.Status.COMPLETE);
        user = userRepository.save(user);

        String rawToken = "test-token";
        String newPassword = "newSecurePassword";

        PasswordResetRequest request = new PasswordResetRequest();
        request.setUser(user);
        request.setToken(rawToken);
        request.setEmail(user.getEmail());
        request.setCallbackUrl("https://example.com/reset");
        request.setStatus("PENDING");
        request.setExpiresAt(Instant.now().plus(1, ChronoUnit.HOURS));
        passwordResetRequestRepository.save(request);

        ResetPasswordDto dto = new ResetPasswordDto();
        dto.setToken(rawToken);
        dto.setPassword(newPassword);
        dto.setType("PASSWORD_RESET");

        userService.resetPassword(dto);

        User updatedUser = userRepository.findById(user.getId()).orElseThrow();
        assertNotNull(updatedUser.getPassword(), "Password should not be null in DB");
        assertTrue(
                passwordEncoder.matches(newPassword, updatedUser.getPassword()),
                "Password should be hashed and valid");
    }
}

