import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FractionInput } from "@/components/ui/fraction-input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowRight } from "lucide-react";
import { useEffect, useState } from "react";
import { Controller, useForm, useFormContext, useWatch } from "react-hook-form";
import { z } from "zod";
import { PriceListFormData } from "../price-list-form-provider";
import { formatCurrency } from "@/utils/format-currency";

interface AddThresholdDialogProps {
  onAdd: (data: AddThresholdFormData) => void;
}

const addThresholdFormSchema = z.object({
  id: z.number().optional(),
  title: z.string({ message: "Threshold title is required" }).min(1, { message: "Threshold title is required" }),
  value: z.number({ message: "Threshold value is required" }).min(1, { message: "Threshold value must be at least 1" }),
  helper_text: z.string().nullable().default(""),
});

export type AddThresholdFormData = z.infer<typeof addThresholdFormSchema>;

export function AddThresholdDialog({ onAdd }: AddThresholdDialogProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    control,
    setError,
  } = useForm<AddThresholdFormData>({
    resolver: zodResolver(addThresholdFormSchema),
  });

  useEffect(() => {
    reset();

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDialogOpen]);

  const priceListForm = useFormContext<PriceListFormData>();

  const thresholds = useWatch({ control: priceListForm.control, name: "thresholds" });

  function handleFormSubmit(data: AddThresholdFormData) {
    if (!thresholds) return;

    const lastThreshold = thresholds[thresholds.length - 1];

    if (!lastThreshold) return;

    if (data.value <= lastThreshold.value) {
      setError("value", {
        message: `Threshold value must be greater than the previous threshold (${formatCurrency(lastThreshold.value)})`,
      });
      return;
    }

    onAdd({ ...data, helper_text: data.helper_text || null });
    setIsDialogOpen(false);
  }

  async function handleDialogOpenChange(open: boolean) {
    if (!open) {
      reset({
        id: undefined,
        title: undefined,
        value: undefined,
        helper_text: undefined,
      });
    }

    setIsDialogOpen(open);
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="iconSmall"
          leadingIcon={<Add className="text-support-blue" />}
        />
      </DialogTrigger>
      <DialogContent className="px-8 max-w-[600px]">
        <DialogHeader>
          <DialogTitle>New threshold</DialogTitle>
          <DialogDescription>Create a new threshold for the pricing list.</DialogDescription>
        </DialogHeader>
        <div id="add-new-information-form" className="w-full space-y-10">
          <div className="w-full space-y-6">
            <div className="w-full">
              <Input
                label="Title *"
                placeholder="Title"
                {...register("title")}
                variant={errors.title ? "error" : "default"}
                errorMessage={errors.title?.message}
              />
            </div>
            <div className="w-full">
              <Controller
                name="value"
                control={control}
                render={({ field }) => (
                  <FractionInput {...field} label="Threshhold (€) *" type="currency" error={errors.value?.message} />
                )}
              />
            </div>
            <div className="w-full">
              <Textarea
                label="Helper text"
                placeholder="Description"
                {...register("helper_text")}
                errorMessage={errors.helper_text?.message}
                rows={3}
              />
            </div>
          </div>
          <div className="flex flex-col mt-8">
            <div className="flex items-center justify-end">
              <Button
                form="add-new-information-form"
                type="submit"
                variant="filled"
                color="yellow"
                size="medium"
                trailingIcon={<ArrowRight />}
                onClick={() => handleSubmit(handleFormSubmit)()}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
