"use client";

import Image from "next/image";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Dialog, DialogContent, DialogFooter } from "@/components/ui/dialog";
import { useRouter } from "next/navigation";

interface SuccessDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function SuccessDialog({ open, setOpen }: SuccessDialogProps) {
  const router = useRouter();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl">
        <div className="mb-10 flex items-center gap-2">
          <Image src="/assets/images/leaf-seal.svg" alt="Leaf seal" width={36} height={36} className="size-9" />
          <h3 className="font-bold text-[1.75rem] text-primary">Changes saved</h3>
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="filled"
            color="yellow"
            size="medium"
            onClick={() => router.push("/discount-codes")}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
