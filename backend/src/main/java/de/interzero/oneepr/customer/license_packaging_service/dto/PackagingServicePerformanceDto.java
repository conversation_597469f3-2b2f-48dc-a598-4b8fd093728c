package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object representing the performance of a packaging service.
 */
@Data
public class PackagingServicePerformanceDto {

    @JsonProperty("setup_packaging_service_id")
    @Schema(
            description = "The setup packaging service ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupPackagingServiceId;

    @JsonProperty("name")
    @Schema(
            description = "The name of the packaging service",
            example = "Standard Packaging",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @JsonProperty("customers_total")
    @Schema(
            description = "The total number of customers",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int customersTotal;

    @JsonProperty("revenue_total")
    @Schema(
            description = "The total revenue",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int revenueTotal;

    @JsonProperty("handling_total")
    @Schema(
            description = "The total handling fee",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int handlingTotal;

    @JsonProperty("third_party_total")
    @Schema(
            description = "The total third party fee",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private int thirdPartyTotal;

}
