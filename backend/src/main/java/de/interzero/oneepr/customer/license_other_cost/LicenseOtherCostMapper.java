package de.interzero.oneepr.customer.license_other_cost;

import de.interzero.oneepr.customer.license_other_cost.dto.UpdateLicenseOtherCostDto;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * Mapper for handling updates to the LicenseOtherCost entity.
 */
@Mapper(componentModel = "spring")
public interface LicenseOtherCostMapper {

    /**
     * Applies partial updates from a DTO to an existing entity.
     * Null fields in the DTO are ignored.
     *
     * @param dto    The source DTO with update data.
     * @param entity The target entity to be updated.
     * @ts-legacy The nullValuePropertyMappingStrategy.IGNORE is crucial for replicating
     * the behavior of a partial update in NestJS, where only provided fields are changed.
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(@MappingTarget LicenseOtherCost entity,
                       UpdateLicenseOtherCostDto dto);
}