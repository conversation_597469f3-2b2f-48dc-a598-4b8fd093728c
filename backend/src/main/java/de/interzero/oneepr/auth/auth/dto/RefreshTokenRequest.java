package de.interzero.oneepr.auth.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.NonNull;

public record RefreshTokenRequest(@NonNull @JsonProperty("refresh_token") String refreshToken,
                                  @NonNull @JsonProperty("access_token") String accessToken) {  // access token isn't actually used, but included to satisfy the original request structure

}
