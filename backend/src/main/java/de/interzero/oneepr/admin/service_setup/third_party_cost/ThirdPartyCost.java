package de.interzero.oneepr.admin.service_setup.third_party_cost;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "other_cost",
        schema = "public"
)
public class ThirdPartyCost {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "price",
            nullable = false
    )
    @JsonProperty("price")
    private Integer price;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "country_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("country")
    private Country country;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
            )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    /**
     * Exposes the ID of the related Country in the JSON output.
     *
     * @return The ID of the country.
     */
    @Transient
    @JsonProperty("country_id")
    public Integer getCountryId() {
        return country != null ? country.getId() : null;
    }

    /**
     * Sets the creation and update timestamps before the entity is first persisted.
     * This is a JPA lifecycle callback that translates Prisma's `@default(now())` and `@updatedAt`.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = updatedAt = Instant.now();
    }

    /**
     * Updates the 'updatedAt' timestamp before an existing entity is updated.
     * This is a JPA lifecycle callback that translates Prisma's `@updatedAt`.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }
}
