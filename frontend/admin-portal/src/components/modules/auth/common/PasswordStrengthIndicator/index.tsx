import { CONTAINS_LETTER_REGEX, CONTAINS_NON_ALPHANUMERIC_REGEX, CONTAINS_NUMBER_REGEX } from "@/utils/regex";
import { cn } from "@/utils/cn";
import { memo, useMemo } from "react";

interface PasswordStrengthIndicatorProps {
  password?: string;
  errMsg?: string;
}
export const PasswordStrengthIndicator = memo(function PasswordStrengthIndicator({
  password = "",
  errMsg = "",
}: PasswordStrengthIndicatorProps) {
  const { strengthText, strengthWidth, textColor, bgColor } = useMemo(() => {
    let score = 1;
    if (!password || password.length < 8) {
      score = 0;
    } else {
      if (CONTAINS_LETTER_REGEX.test(password)) score++;
      if (CONTAINS_NUMBER_REGEX.test(password)) score++;
      if (CONTAINS_NON_ALPHANUMERIC_REGEX.test(password)) score++;
    }

    let strengthText = "";
    let strengthColorClasses = "";
    let strengthWidth = "0%";

    if (score <= 2) {
      strengthText = "Weak";
      strengthColorClasses = "text-tonal-red-40 bg-tonal-red-40";
      strengthWidth = "33%";
    } else if (score === 3) {
      strengthText = "Medium";
      strengthColorClasses = "text-tonal-yellow-50 bg-tonal-yellow-50";
      strengthWidth = "66%";
    } else if (score >= 4) {
      strengthText = "Strong";
      strengthColorClasses = "text-tonal-green-40 bg-tonal-green-40";
      strengthWidth = "100%";
    }

    const [textColor, bgColor] = strengthColorClasses.split(" ");
    return {
      strengthText,
      strengthWidth,
      textColor,
      bgColor,
    };
  }, [password]);

  return (
    <div className="w-full flex flex-col gap-2">
      <div className="flex justify-start items-center text-sm">
        <p className="font-normal">Password strength: </p>
        {strengthText && <span className={cn("font-normal ml-1", textColor)}>{strengthText}</span>}
      </div>
      <div className="w-full bg-tonal-cream-90 rounded-[10px] h-2">
        <div className={cn("h-2 rounded-[10px] transition-all", bgColor)} style={{ width: strengthWidth }}></div>
      </div>
      {errMsg && <span className={cn("text-sm font-normal", textColor)}>{errMsg}</span>}
    </div>
  );
});
