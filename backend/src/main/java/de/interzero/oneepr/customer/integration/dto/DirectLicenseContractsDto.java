package de.interzero.oneepr.customer.integration.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "Fields required to create a direct licensing contract")
public class DirectLicenseContractsDto extends BaseDto {

    @JsonProperty("contract_volume_value")
    @Schema(
            description = "Total contract volume value",
            example = "1500",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer contractVolumeValue;

    @JsonProperty("status")
    @Schema(
            description = "Status of the contract",
            example = "0",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String status;

    @JsonProperty("active_year")
    @Schema(
            description = "Year the contract is active",
            example = "2025",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String activeYear;

    @JsonProperty("service_type")
    @Schema(
            description = "Service type identifier",
            example = "2",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String serviceType;

    @JsonProperty("starting_year")
    @Schema(
            description = "Starting year of the contract",
            example = "2025",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String startingYear;

    @JsonProperty("ending_year")
    @Schema(
            description = "Ending year of the contract",
            example = "2026",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String endingYear;

    @JsonProperty("termination_date")
    @Schema(
            description = "Termination date in ISO format",
            example = "2026-01-01",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String terminationDate;

    @JsonProperty("customer_id")
    @Schema(
            description = "Customer ID",
            example = "123456",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String customerId;

    @JsonProperty("country")
    @Schema(
            description = "Country name",
            example = "Germany",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String country;
}
