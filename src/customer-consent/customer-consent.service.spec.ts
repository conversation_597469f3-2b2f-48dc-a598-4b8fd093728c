import { Test, TestingModule } from "@nestjs/testing";
import { CustomerConsentService } from "./customer-consent.service";
import { DatabaseService } from "@/database/database.service";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { CreateCustomerConsentDto } from "./dto/create-customer-consent";
import { UpdateCustomerConsentDto } from "./dto/update-customer-consent";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("CustomerConsentService", () => {
  let service: CustomerConsentService;
  let databaseService: DatabaseService;
  let customerIoService: CustomerIoService;

  const mockDate = new Date("2025-01-01T12:00:00Z");

  const mockCustomerConsent = {
    id: 1,
    customer_id: 100,
    consent_id: 200,
    given: true,
    givenAt: mockDate,
    revokedAt: null,
    created_at: mockDate,
    updated_at: mockDate,
    customer: {
      id: 100,
      name: "Test Customer",
    },
    consent: {
      id: 200,
      name: "Test Consent",
    },
  };

  const mockEmailConsent = {
    id: 2,
    customer_id: 101,
    consent_id: 201,
    given: true,
    givenAt: mockDate,
    revokedAt: null,
    created_at: mockDate,
    updated_at: mockDate,
    customer: {
      id: 101,
      name: "Email Customer",
    },
    consent: {
      id: 201,
      name: "Result by mail – we need you consent to send you e-mails*",
    },
  };

  const mockDatabaseService = {
    customerConsent: {
      create: jest.fn(),
      createMany: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    $transaction: jest.fn((callback) => callback(mockDatabaseService)),
  };

  const mockCustomerIoService = {
    updateAttributesByCustomerId: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    jest.spyOn(global, "Date").mockImplementation(() => mockDate as any);

    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomerConsentService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
        {
          provide: CustomerIoService,
          useValue: mockCustomerIoService,
        },
      ],
    }).compile();

    service = module.get<CustomerConsentService>(CustomerConsentService);
    databaseService = module.get<DatabaseService>(DatabaseService);
    customerIoService = module.get<CustomerIoService>(CustomerIoService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    const createDto: CreateCustomerConsentDto = {
      customer_id: 100,
      consent_id: 200,
      given: true,
    };

    it("should successfully create a customer consent", async () => {
      mockDatabaseService.customerConsent.create.mockResolvedValue(mockCustomerConsent);

      const result = await service.create(createDto);

      expect(result).toEqual(mockCustomerConsent);
      expect(mockDatabaseService.customerConsent.create).toHaveBeenCalledWith({
        data: {
          given: true,
          givenAt: mockDate,
          revokedAt: null,
          created_at: mockDate,
          updated_at: mockDate,
          customer: {
            connect: { id: 100 },
          },
          consent: {
            connect: { id: 200 },
          },
        },
        include: {
          customer: true,
          consent: true,
        },
      });
      expect(mockCustomerIoService.updateAttributesByCustomerId).not.toHaveBeenCalled();
    });

    it("should update CustomerIO when email consent is created", async () => {
      mockDatabaseService.customerConsent.create.mockResolvedValue(mockEmailConsent);

      await service.create({
        customer_id: 101,
        consent_id: 201,
        given: true,
      });

      expect(mockCustomerIoService.updateAttributesByCustomerId).toHaveBeenCalledWith(101, {
        cio_subscription_preferences: {
          topics: {
            topic_1: true,
            topic_3: true,
            topic_5: true,
            topic_6: true,
          },
        },
      });
    });

    it("should set revokedAt when consent is not given", async () => {
      const notGivenConsent = { ...mockCustomerConsent, given: false, givenAt: null, revokedAt: mockDate };
      mockDatabaseService.customerConsent.create.mockResolvedValue(notGivenConsent);

      const result = await service.create({
        ...createDto,
        given: false,
      });

      expect(result).toEqual(notGivenConsent);
      expect(mockDatabaseService.customerConsent.create).toHaveBeenCalledWith({
        data: {
          given: false,
          givenAt: null,
          revokedAt: mockDate,
          created_at: mockDate,
          updated_at: mockDate,
          customer: {
            connect: { id: 100 },
          },
          consent: {
            connect: { id: 200 },
          },
        },
        include: {
          customer: true,
          consent: true,
        },
      });
    });

    it("should throw NotFoundException when customer or consent not found", async () => {
      const error = new Prisma.PrismaClientKnownRequestError("", {
        code: "P2025",
        clientVersion: "",
      });
      mockDatabaseService.customerConsent.create.mockRejectedValue(error);

      await expect(service.create(createDto)).rejects.toThrow(NotFoundException);
      await expect(service.create(createDto)).rejects.toThrow("Customer or Consent not found");
    });

    it("should throw BadRequestException when consent combination already exists", async () => {
      const error = new Prisma.PrismaClientKnownRequestError("", {
        code: "P2002",
        clientVersion: "",
      });
      mockDatabaseService.customerConsent.create.mockRejectedValue(error);

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
      await expect(service.create(createDto)).rejects.toThrow("A CustomerConsent with this combination already exists");
    });

    it("should throw BadRequestException for other errors", async () => {
      mockDatabaseService.customerConsent.create.mockRejectedValue(new Error("Unknown error"));

      await expect(service.create(createDto)).rejects.toThrow(BadRequestException);
      await expect(service.create(createDto)).rejects.toThrow("Unable to create CustomerConsent");
    });
  });

  describe("createMany", () => {
    const createDtos: CreateCustomerConsentDto[] = [
      { customer_id: 100, consent_id: 200, given: true },
      { customer_id: 101, consent_id: 201, given: false },
    ];

    it("should create and update multiple consents", async () => {
      mockDatabaseService.customerConsent.findMany.mockResolvedValue([{ customer_id: 100, consent_id: 200 }]);

      mockDatabaseService.customerConsent.findMany.mockResolvedValueOnce([{ customer_id: 100, consent_id: 200 }]);

      mockDatabaseService.customerConsent.update.mockResolvedValueOnce({
        id: 1,
        customer_id: 100,
        consent_id: 200,
        given: true,
      });

      mockDatabaseService.customerConsent.findMany.mockResolvedValueOnce([
        {
          id: 1,
          customer_id: 100,
          consent_id: 200,
          given: true,
          customer: { id: 100 },
          consent: { id: 200, name: "Test Consent" },
        },
        {
          id: 2,
          customer_id: 101,
          consent_id: 201,
          given: false,
          customer: { id: 101 },
          consent: { id: 201, name: "Test Consent 2" },
        },
      ]);

      const result = await service.createMany(createDtos);

      expect(result.length).toBe(2);
      expect(mockDatabaseService.customerConsent.update).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.customerConsent.createMany).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.$transaction).toHaveBeenCalled();
    });

    it("should handle no creates needed", async () => {
      mockDatabaseService.customerConsent.findMany.mockResolvedValue([
        { customer_id: 100, consent_id: 200 },
        { customer_id: 101, consent_id: 201 },
      ]);

      mockDatabaseService.customerConsent.update.mockResolvedValueOnce({
        id: 1,
        customer_id: 100,
        consent_id: 200,
        given: true,
      });

      mockDatabaseService.customerConsent.update.mockResolvedValueOnce({
        id: 2,
        customer_id: 101,
        consent_id: 201,
        given: false,
      });

      mockDatabaseService.customerConsent.findMany.mockResolvedValueOnce([
        {
          id: 1,
          customer_id: 100,
          consent_id: 200,
          given: true,
          customer: { id: 100 },
          consent: { id: 200, name: "Test Consent" },
        },
        {
          id: 2,
          customer_id: 101,
          consent_id: 201,
          given: false,
          customer: { id: 101 },
          consent: { id: 201, name: "Test Consent 2" },
        },
      ]);

      const result = await service.createMany(createDtos);

      expect(result.length).toBe(2);
      expect(mockDatabaseService.customerConsent.update).toHaveBeenCalledTimes(2);
      expect(mockDatabaseService.customerConsent.createMany).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when customers or consents not found", async () => {
      const error = new Prisma.PrismaClientKnownRequestError("", {
        code: "P2025",
        clientVersion: "",
      });
      mockDatabaseService.customerConsent.findMany.mockRejectedValue(error);

      await expect(service.createMany(createDtos)).rejects.toThrow(NotFoundException);
      await expect(service.createMany(createDtos)).rejects.toThrow("One or more Customers or Consents not found");
    });

    it("should throw BadRequestException for other errors", async () => {
      mockDatabaseService.customerConsent.findMany.mockRejectedValue(new Error("Unknown error"));

      await expect(service.createMany(createDtos)).rejects.toThrow(BadRequestException);
      await expect(service.createMany(createDtos)).rejects.toThrow("Unable to create/update CustomerConsents");
    });
  });

  describe("findAll", () => {
    it("should return all customer consents", async () => {
      const mockConsents = [mockCustomerConsent, mockEmailConsent];
      mockDatabaseService.customerConsent.findMany.mockResolvedValue(mockConsents);

      const result = await service.findAll();

      expect(result).toEqual(mockConsents);
      expect(mockDatabaseService.customerConsent.findMany).toHaveBeenCalled();
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a single customer consent by ID", async () => {
      mockDatabaseService.customerConsent.findUnique.mockResolvedValue(mockCustomerConsent);

      const result = await service.findOne(1, user);

      expect(result).toEqual(mockCustomerConsent);
      expect(mockDatabaseService.customerConsent.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: {
          customer: true,
          consent: true,
        },
      });
    });

    it("should throw NotFoundException when consent not found", async () => {
      mockDatabaseService.customerConsent.findUnique.mockResolvedValue(null);

      await expect(service.findOne(999, user)).rejects.toThrow(NotFoundException);
      await expect(service.findOne(999, user)).rejects.toThrow("Customer consent not found");
    });
  });

  describe("findByCustomerId", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException for invalid customer ID", async () => {
      await expect(service.findByCustomerId(NaN, user)).rejects.toThrow(BadRequestException);
      await expect(service.findByCustomerId(NaN, user)).rejects.toThrow("Invalid CustomerConsent ID");
    });

    it("should throw NotFoundException when no consents found", async () => {
      mockDatabaseService.customerConsent.findMany.mockResolvedValue([]);

      await expect(service.findByCustomerId(999, user)).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    const updateDto: UpdateCustomerConsentDto = {
      customer_id: 100,
      consent_id: 200,
      given: false,
    };

    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException for invalid ID", async () => {
      await expect(service.update(NaN, updateDto, user)).rejects.toThrow(BadRequestException);
      await expect(service.update(NaN, updateDto, user)).rejects.toThrow("Invalid CustomerConsent ID");
    });

    it("should throw NotFoundException when consent not found", async () => {
      const error = new Prisma.PrismaClientKnownRequestError("", {
        code: "P2025",
        clientVersion: "",
      });
      mockDatabaseService.customerConsent.update.mockRejectedValue(error);

      await expect(service.update(999, updateDto, user)).rejects.toThrow("Customer consent not found");
    });

    it("should throw BadRequestException for other errors", async () => {
      mockDatabaseService.customerConsent.update.mockRejectedValue(new Error("Update error"));

      await expect(service.update(1, updateDto, user)).rejects.toThrow("Customer consent not found");
    });
  });

  describe("updateMany", () => {
    const updateDtos: UpdateCustomerConsentDto[] = [
      { id: 1, customer_id: 100, consent_id: 200, given: false },
      { id: 2, customer_id: 101, consent_id: 201, given: true },
    ];

    it("should update multiple consents successfully", async () => {
      mockDatabaseService.customerConsent.update.mockResolvedValueOnce({
        id: 1,
        customer_id: 100,
        consent_id: 200,
        given: false,
        givenAt: null,
        revokedAt: mockDate,
        updated_at: mockDate,
      });

      mockDatabaseService.customerConsent.update.mockResolvedValueOnce({
        id: 2,
        customer_id: 101,
        consent_id: 201,
        given: true,
        givenAt: mockDate,
        revokedAt: null,
        updated_at: mockDate,
      });

      const result = await service.updateMany(updateDtos);

      expect(result.length).toBe(2);
      expect(mockDatabaseService.customerConsent.update).toHaveBeenCalledTimes(2);
      expect(mockDatabaseService.customerConsent.update).toHaveBeenNthCalledWith(1, {
        where: { id: 1 },
        data: {
          given: false,
          updated_at: mockDate,
          givenAt: null,
          revokedAt: mockDate,
        },
      });
      expect(mockDatabaseService.customerConsent.update).toHaveBeenNthCalledWith(2, {
        where: { id: 2 },
        data: {
          given: true,
          updated_at: mockDate,
          givenAt: mockDate,
          revokedAt: null,
        },
      });
    });

    it("should throw NotFoundException when one or more consents not found", async () => {
      const error = new Prisma.PrismaClientKnownRequestError("", {
        code: "P2025",
        clientVersion: "",
      });
      mockDatabaseService.customerConsent.update.mockRejectedValue(error);

      await expect(service.updateMany(updateDtos)).rejects.toThrow(NotFoundException);
      await expect(service.updateMany(updateDtos)).rejects.toThrow("One or more CustomerConsents were not found");
    });

    it("should throw BadRequestException for other errors", async () => {
      mockDatabaseService.customerConsent.update.mockRejectedValue(new Error("Update error"));

      await expect(service.updateMany(updateDtos)).rejects.toThrow(BadRequestException);
      await expect(service.updateMany(updateDtos)).rejects.toThrow("Error updating CustomerConsents: Update error");
    });
  });
});
