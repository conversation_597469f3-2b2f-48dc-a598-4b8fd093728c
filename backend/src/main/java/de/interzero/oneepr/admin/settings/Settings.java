package de.interzero.oneepr.admin.settings;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.fraction_icon.Files;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "settings",
        schema = "public"
)
public class Settings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "key",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("key")
    private String key;

    @NotNull
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(
            name = "value",
            nullable = false,
            columnDefinition = "jsonb"
    )
    @JsonProperty("value")
    private Map<String, Object> value;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "term_or_condition_file_id")
    @JsonIgnore
    private Files file;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        this.createdAt = now;
        this.updatedAt = now;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    @Transient
    @JsonProperty("term_or_condition_file_id")
    public String getTermOrConditionFileId() {
        return (this.file != null) ? this.file.getId() : null;
    }
}
