package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing a phone number associated with a customer.
 * <p>
 * This DTO is a direct translation of the {@code CustomerPhone} model included in
 * the {@code phones} relation of the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CustomerPhoneDto {

    @Schema(description = "Unique identifier of the customer phone record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The phone number.")
    @JsonProperty("phone_number")
    private String phoneNumber;

    @Schema(description = "The type of phone number (e.g., PHONE, MOBILE).")
    @JsonProperty("phone_type")
    private String phoneType;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;
}