import { UploadedFile } from "../file/types";

export type RequiredInformationType = "TEXT" | "NUMBER" | "DOCUMENT" | "FILE" | "IMAGE";

export type RequiredInformationKind = "GENERATION_INFORMATION" | "LICENSE_INFORMATION";

export type RequiredInformationStatus = "OPEN" | "DONE" | "DECLINED" | "APPROVED";

export interface RequiredInformation {
  id: number;
  setup_required_information_id: number;
  license_id: number;
  setup_general_information_id: number;
  contract_id: number;
  kind: RequiredInformationKind;
  type: RequiredInformationType;
  status: RequiredInformationStatus;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  question: string | null;
  file_id: string | null;
  answer: string | null;
  answer_files: UploadedFile[];
}

export interface CreateRequiredInformationRequest {
  setup_required_information_id: number;
  license_id: number;
  type: RequiredInformationType;
  status: RequiredInformationStatus;
  name: string;
  description: string;
  question?: string;
  fileId?: string;
}

export interface CreateAdminRequiredInformationRequest {
  country_id: number;
  type: RequiredInformationType;
  name: string;
  description: string;
  question?: string;
  file_id?: string;
}
