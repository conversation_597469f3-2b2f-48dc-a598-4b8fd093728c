package de.interzero.oneepr.customer.customer_consent;

import de.interzero.oneepr.admin.mail.EmailDeliveryException;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.customer.consent.Consent;
import de.interzero.oneepr.customer.consent.ConsentRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_consent.dto.CreateCustomerConsentDto;
import de.interzero.oneepr.customer.customer_consent.dto.UpdateCustomerConsentDto;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceException;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.*;

/**
 * CustomerConsentService
 * Service for managing customer consents, including creation, updates, and retrieval.
 * This service interacts with Customer.io for certain consent-related attribute updates.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CustomerConsentService {

    private final EntityManager entityManager;

    private final CustomerConsentRepository customerConsentRepository;

    private final CustomerRepository customerRepository;

    private final ConsentRepository consentRepository;

    private final CustomerIoService customerIoService;

    private static final String RESULT_BY_MAIL_CONSENT_NAME = "Result by mail – we need you consent to send you e-mails*";

    private static final String CUSTOMER = "customer";

    private final EmailOutboxGateway emailOutboxGateway;

    private static final String CONSENT = "consent";

    /**
     * create
     * Creates a single customer consent record.
     * If the consent is for receiving results by mail, it updates attributes in Customer.io.
     *
     * @param dto The data transfer object containing information for the new consent.
     * @return The newly created and saved CustomerConsent entity.
     * @throws ResponseStatusException if referenced Customer or Consent is not found (404),
     *                                 if a consent with this combination already exists (400),
     *                                 or for other creation errors (400).
     * @ts-legacy the original code has a TODO: "Change id matching to enum for Result by mail – we need you consent to send you e-mails*"
     */
    @Transactional
    public CustomerConsent create(CreateCustomerConsentDto dto) {
        try {
            Instant date = Instant.now();
            Customer customer = customerRepository.findById(dto.getCustomerId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "Customer not found for ID: " + dto.getCustomerId()));
            Consent consent = consentRepository.findById(dto.getConsentId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "Consent not found for ID: " + dto.getConsentId()));
            boolean dtoGiven = Boolean.TRUE.equals(dto.getGiven());
            CustomerConsent customerConsent = new CustomerConsent();
            customerConsent.setGiven(dto.getGiven());
            customerConsent.setGivenAt(dtoGiven ? date : null);
            customerConsent.setRevokedAt(dtoGiven ? null : date);
            customerConsent.setCreatedAt(date);
            customerConsent.setUpdatedAt(date);
            customerConsent.setCustomer(customer);
            customerConsent.setConsent(consent);

            CustomerConsent savedCustomerConsent = customerConsentRepository.save(customerConsent);

            if (savedCustomerConsent.getConsent() != null && RESULT_BY_MAIL_CONSENT_NAME.equals(savedCustomerConsent.getConsent()
                                                                                                        .getName())) {

                Map<String, Boolean> topics = new HashMap<>();
                boolean getGiven = Boolean.TRUE.equals(savedCustomerConsent.getGiven());
                topics.put("topic_1", getGiven);
                topics.put("topic_3", getGiven);
                topics.put("topic_5", getGiven);
                topics.put("topic_6", getGiven);

                Map<String, Object> preferences = new HashMap<>();
                preferences.put("topics", topics);
                customerIoService.updateAttributesByCustomerId(savedCustomerConsent.getCustomer().getId(), preferences);
            }
            return savedCustomerConsent;
        } catch (DataIntegrityViolationException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "A CustomerConsent with this combination already exists.",
                                              e);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unable to create CustomerConsent.", e);
        }
    }

    /**
     * createMany
     * Creates or updates multiple customer consent records in bulk.
     * It first identifies existing consents to determine whether to create new ones or update existing ones.
     * All database operations are performed within a single transaction.
     *
     * @param dtos A list of DTOs, each containing information for a customer consent.
     * @return A list of the created or updated CustomerConsent entities, fetched after all operations.
     * @throws ResponseStatusException for various errors including data integrity violations or if underlying entities are not found during creation.
     * @ts-legacy The process of fetching all potentially existing consents and then iterating through input DTOs to check for existence (O(N*M) complexity) mimics the original TypeScript's .some() or similar pattern.
     * @ts-legacy The original TypeScript used Promise.all for concurrent database operations; this Java version executes updates and then creates sequentially within the transaction.
     * @ts-legacy Methods should not perform too many tasks (aka Brain method)
     * @ts-legacy Cognitive Complexity of methods should not be too high
     */
    @Transactional
    public List<CustomerConsent> createMany(List<CreateCustomerConsentDto> dtos) {
        try {
            Instant date = Instant.now();
            List<CustomerConsent> existingConsents = new ArrayList<>();
            if (dtos != null && !dtos.isEmpty()) {
                CriteriaBuilder cb = entityManager.getCriteriaBuilder();
                CriteriaQuery<CustomerConsent> cq = cb.createQuery(CustomerConsent.class);
                Root<CustomerConsent> customerConsentRoot = cq.from(CustomerConsent.class);
                customerConsentRoot.fetch(CUSTOMER, JoinType.LEFT);
                customerConsentRoot.fetch(CONSENT, JoinType.LEFT);

                List<Predicate> orPredicates = new ArrayList<>();
                for (CreateCustomerConsentDto dto : dtos) {
                    Predicate customerMatch = cb.equal(
                            customerConsentRoot.get(CUSTOMER).get("id"),
                            dto.getCustomerId());
                    Predicate consentMatch = cb.equal(customerConsentRoot.get(CONSENT).get("id"), dto.getConsentId());
                    orPredicates.add(cb.and(customerMatch, consentMatch));
                }
                cq.select(customerConsentRoot).where(cb.or(orPredicates.toArray(new Predicate[0])));
                TypedQuery<CustomerConsent> queryExisting = entityManager.createQuery(cq);
                existingConsents = queryExisting.getResultList();
            }
            List<CreateCustomerConsentDto> updates = new ArrayList<>();
            List<CreateCustomerConsentDto> creates = new ArrayList<>();
            if (dtos != null) {
                for (CreateCustomerConsentDto dto : dtos) {
                    boolean exists = false;
                    for (CustomerConsent existing : existingConsents) {
                        if (existing.getCustomer() != null && existing.getCustomer()
                                .getId()
                                .equals(dto.getCustomerId()) && existing.getConsent() != null && existing.getConsent()
                                .getId()
                                .equals(dto.getConsentId())) {
                            exists = true;
                            break;
                        }
                    }
                    if (exists) {
                        updates.add(dto);
                    } else {
                        creates.add(dto);
                    }
                }
            }
            for (CreateCustomerConsentDto dtoToUpdate : updates) {
                boolean dtoGiven = Boolean.TRUE.equals(dtoToUpdate.getGiven());
                customerConsentRepository.updateConsentAttributes(
                        dtoToUpdate.getCustomerId(),
                        dtoToUpdate.getConsentId(),
                        dtoToUpdate.getGiven(),
                        dtoGiven ? date : null,
                        dtoGiven ? null : date,
                        date);
            }
            entityManager.flush();
            entityManager.clear();
            List<CustomerConsent> newEntitiesToCreate = new ArrayList<>();
            if (!creates.isEmpty()) {
                for (CreateCustomerConsentDto dtoToCreate : creates) {
                    boolean getGiven = Boolean.TRUE.equals(dtoToCreate.getGiven());
                    CustomerConsent newConsent = new CustomerConsent();
                    Customer customer = customerRepository.findById(dtoToCreate.getCustomerId())
                            .orElseThrow(() -> new ResponseStatusException(
                                    HttpStatus.NOT_FOUND,
                                    "Consent with ID " + dtoToCreate.getCustomerId() + " not found"));
                    Consent consent = consentRepository.findById(dtoToCreate.getConsentId())
                            .orElseThrow(() -> new ResponseStatusException(
                                    HttpStatus.NOT_FOUND,
                                    "Consent with ID " + dtoToCreate.getCustomerId() + " not found"));
                    newConsent.setCustomer(customer);
                    newConsent.setConsent(consent);
                    newConsent.setGiven(dtoToCreate.getGiven());
                    newConsent.setGivenAt(getGiven ? date : null);
                    newConsent.setRevokedAt(getGiven ? null : date);
                    newConsent.setCreatedAt(date);
                    newConsent.setUpdatedAt(date);
                    newEntitiesToCreate.add(newConsent);
                }
                customerConsentRepository.saveAll(newEntitiesToCreate);
                entityManager.flush();
            }
            if (dtos == null || dtos.isEmpty()) {
                return new ArrayList<>();
            }

            CriteriaBuilder cb = entityManager.getCriteriaBuilder();
            CriteriaQuery<CustomerConsent> cq = cb.createQuery(CustomerConsent.class);
            Root<CustomerConsent> customerConsentRoot = cq.from(CustomerConsent.class);
            customerConsentRoot.fetch(CUSTOMER, JoinType.LEFT);
            customerConsentRoot.fetch(CONSENT, JoinType.LEFT);

            List<Predicate> orPredicates = new ArrayList<>();

            for (CreateCustomerConsentDto dto : dtos) {
                Predicate customerMatch = cb.equal(customerConsentRoot.get(CUSTOMER).get("id"), dto.getCustomerId());
                Predicate consentMatch = cb.equal(customerConsentRoot.get(CONSENT).get("id"), dto.getConsentId());
                orPredicates.add(cb.and(customerMatch, consentMatch));
            }
            cq.select(customerConsentRoot).where(cb.or(orPredicates.toArray(new Predicate[0]))).distinct(true);
            TypedQuery<CustomerConsent> queryResult = entityManager.createQuery(cq);
            return queryResult.getResultList();

        } catch (DataIntegrityViolationException dive) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                    "One or more CustomerConsents with this combination already exist or a data integrity rule was violated.",
                    dive);
        } catch (PersistenceException pe) {
            if (pe.getCause() instanceof org.hibernate.exception.ConstraintViolationException) {
                throw new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                        "One or more CustomerConsents with this combination already exist (Constraint Violation).",
                        pe);
            }
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Unable to create/update CustomerConsents due to a persistence issue.",
                                              pe);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Unable to create/update CustomerConsents.", e);
        }
    }

    /**
     * findAll
     * Retrieves all customer consents.
     *
     * @return A list of all CustomerConsent entities.
     */
    public List<CustomerConsent> findAll() {
        return customerConsentRepository.findAll();
    }

    /**
     * findOne
     * Retrieves a single customer consent by its ID after validating user permissions.
     * Includes related customer and consent entities in the fetched result.
     *
     * @param id   The ID of the CustomerConsent to retrieve.
     * @param user The authenticated user performing the request.
     * @return The CustomerConsent entity with related customer and consent data.
     * @throws ResponseStatusException if the consent is not found (404), if the user lacks permission, or for other retrieval errors.
     * @ts-legacy The sequence of validating permission (which involves a fetch) and then potentially re-fetching the entity with all its relations might mirror an original pattern that could involve redundant database calls.
     */
    @Transactional(readOnly = true)
    public CustomerConsent findOne(Integer id,
                                   AuthenticatedUser user) {
        try {
            validatingUserPermissionCustomerConsent(id, user);
            return customerConsentRepository.findByIdAndFetchAll(id)
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "CustomerConsent not found with id: " + id));
            // Note: validatingUserPermissionCustomerConsent already throws if not found. This is a redundant check
            // but harmless and aligns with Prisma's findUnique which would also fail if ID doesn't exist.
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                                              "Error retrieving CustomerConsent: " + e.getMessage(),
                                              e);
        }
    }

    /**
     * findByCustomerId
     * Retrieves all customer consents for a specific customer ID after validating user permissions.
     * Includes related customer and consent entities.
     *
     * @param customerId The ID of the Customer whose consents are to be retrieved.
     * @param user       The authenticated user performing the request.
     * @return A list of CustomerConsent entities associated with the given customer ID.
     * @throws ResponseStatusException if user permissions are insufficient, if no consents are found for the customer (404), or for other errors.
     * @ts-legacy This method throws a NotFoundException (404) if the list of consents is empty,
     * which is an unusual behavior for a "find all by X" type of query and replicates a specific behavior from the original TypeScript code. An Empty List/Array return will be preferred.
     */
    @Transactional(readOnly = true)
    public List<CustomerConsent> findByCustomerId(Integer customerId,
                                                  AuthenticatedUser user) {
        try {
            List<CustomerConsent> customerConsents = customerConsentRepository.findAllByCustomerId(customerId);
            if (customerConsents.isEmpty()) {
                throw new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                                                  "No CustomerConsents found for customer ID " + customerId);
            }
            return customerConsents;
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                                              "Error retrieving CustomerConsents: " + e.getMessage(),
                                              e);
        }
    }

    /**
     * update
     * Updates an existing customer consent record identified by its ID, after validating user permissions.
     * If the consent is for receiving results by mail and its status changes, it updates attributes in Customer.io.
     *
     * @param id   The ID of the CustomerConsent to update.
     * @param dto  The DTO containing the fields to update (primarily 'given' status).
     * @param user The authenticated user performing the update.
     * @return The updated CustomerConsent entity, including its related customer and consent data.
     * @throws ResponseStatusException if the consent is not found for update (404), if the user lacks permission, or for other update errors (400).
     */
    @Transactional
    public CustomerConsent update(Integer id,
                                  UpdateCustomerConsentDto dto,
                                  AuthenticatedUser user) {
        validatingUserPermissionCustomerConsent(id, user);
        boolean dtoGiven = Boolean.TRUE.equals(dto.getGiven());
        try {
            Instant date = Instant.now();

            CustomerConsent customerConsentToUpdate = customerConsentRepository.findByIdAndFetchAll(id)
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "CustomerConsent with ID " + id + " not found for update."));

            if (dto.getGiven() != null) {
                customerConsentToUpdate.setGiven(dtoGiven);
                customerConsentToUpdate.setGivenAt(dtoGiven ? date : null);
                customerConsentToUpdate.setRevokedAt(dtoGiven ? null : date);
            }
            customerConsentToUpdate.setUpdatedAt(date);
            CustomerConsent updatedConsent = customerConsentRepository.save(customerConsentToUpdate);

            if (updatedConsent.getConsent() != null && RESULT_BY_MAIL_CONSENT_NAME.equals(updatedConsent.getConsent()
                                                                                                  .getName())) {
                Map<String, Boolean> topics = new HashMap<>();
                boolean getGiven = Boolean.TRUE.equals(updatedConsent.getGiven());
                topics.put("topic_1", getGiven);
                topics.put("topic_3", getGiven);
                topics.put("topic_5", getGiven);
                topics.put("topic_6", getGiven);
                Map<String, Object> preferences = new HashMap<>();
                preferences.put("topics", topics);
                customerIoService.updateAttributesByCustomerId(updatedConsent.getCustomer().getId(), preferences);
            }
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("email", user.getEmail());
            EmailMessage emailMessage = new EmailMessage(
                    "24",
                                                         user.getEmail(),
                                                         "Lizenzero <<EMAIL>>",
                                                         "Consent status changed",
                                                         messageData);
            emailMessage.setRecipientName(
                    updatedConsent.getCustomer().getFirstName(),
                    updatedConsent.getCustomer().getLastName());

            try {
                emailOutboxGateway.sendEmail(emailMessage);
            } catch (EmailDeliveryException e) {
                log.error(
                        "A problem occurred while trying to send the 'Consent status changed' email to {}: {}",
                        user.getEmail(),
                        e.getMessage());
                // The original NestJS code logs the error and continues, so we do the same here.
            }
            return updatedConsent;
        } catch (ResponseStatusException e) {
            throw e;
        } catch (DataIntegrityViolationException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Data integrity violation during update.", e);
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Error updating CustomerConsent: " + e.getMessage(),
                                              e);
        }
    }

    /**
     * updateMany
     * Updates multiple customer consent records. Each DTO in the list must contain the ID of the consent to update.
     * Operations are performed by fetching and saving each consent individually within a transaction.
     *
     * @param dtos A list of DTOs, each specifying an ID and the 'given' status to update for a CustomerConsent.
     * @return A list of the updated CustomerConsent entities.
     * @throws ResponseStatusException if any CustomerConsent ID is missing or not found (404), or for other update errors (400).
     * @ts-legacy This method replicates a pattern of iterating through DTOs and performing individual updates,
     * rather than a single bulk database update operation. The original TypeScript likely followed a similar iterative update logic for a list.
     */
    @Transactional
    public List<CustomerConsent> updateMany(List<UpdateCustomerConsentDto> dtos) {
        try {
            Instant date = Instant.now();
            List<CustomerConsent> updatedConsentsResult = new ArrayList<>();

            for (UpdateCustomerConsentDto dto : dtos) {
                if (dto.getId() == null) {
                    throw new ResponseStatusException(
                            HttpStatus.BAD_REQUEST,
                                                      "CustomerConsent ID is missing in one of the DTOs for updateMany.");
                }
                CustomerConsent consentToUpdate = customerConsentRepository.findById(dto.getId())
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                "CustomerConsent with ID " + dto.getId() + " not found during batch update."));

                if (dto.getGiven() != null) {
                    boolean dtoGiven = dto.getGiven();
                    consentToUpdate.setGiven(dto.getGiven());
                    consentToUpdate.setGivenAt(dtoGiven ? date : null);
                    consentToUpdate.setRevokedAt(dtoGiven ? null : date);
                }
                consentToUpdate.setUpdatedAt(date);
                updatedConsentsResult.add(customerConsentRepository.save(consentToUpdate));
            }
            return updatedConsentsResult;
        } catch (ResponseStatusException e) {
            throw e;
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error updating CustomerConsents.", e);
        }
    }

    /**
     * validatingUserPermissionCustomerConsent
     * Validates if the authenticated user has the necessary permissions to access or modify a specific customer consent.
     * For users with the 'CUSTOMER' role, it checks if the consent belongs to them. Other roles may have broader access.
     *
     * @param customerConsentId The ID of the CustomerConsent record to check permissions for.
     * @param user              The authenticated user whose permissions are being checked.
     * @throws ResponseStatusException if the customerConsentId is invalid (400), if the consent or its associated customer is not found (404),
     *                                 or if the user does not have permission (403 for CUSTOMER role, 401 for invalid user ID format).
     * @ts-legacy The original TypeScript code might not have explicitly handled a null `customer.user_id` during permission checks; this Java version includes a specific check for robustness against such a scenario.
     */
    public void validatingUserPermissionCustomerConsent(Integer customerConsentId,
                                                        AuthenticatedUser user) {
        if (customerConsentId == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid CustomerConsent ID (null).");
        }


        CustomerConsent customerConsent = customerConsentRepository.findByIdAndFetchCustomer(customerConsentId)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "Customer consent not found with ID: " + customerConsentId));

        Customer customer = customerConsent.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Associated Customer not found for the consent.");
        }


        if (user.getRole() == Role.CUSTOMER) {
            int authenticatedUserId;
            try {
                authenticatedUserId = Integer.parseInt(user.getId());
            } catch (NumberFormatException e) {
                throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Authenticated user ID format is invalid.");
            }

            if (customer.getUserId() == null) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "Cannot verify permission; customer user association missing.");
            }

            if (!Objects.equals(customer.getUserId(), authenticatedUserId)) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have permission to access this customer consent.");
            }
        }
    }
}