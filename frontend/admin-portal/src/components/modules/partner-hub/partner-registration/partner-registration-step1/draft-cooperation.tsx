"use client";

import { Divider } from "@/components/common/divider";
import { cn } from "@/utils/cn";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Pdf, Question, Upload } from "@interzero/oneepr-react-ui/Icon";
import { useRef } from "react";
import { useFormContext } from "react-hook-form";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { CreatePartnerParams } from "..";

export function DraftCooperation() {
  const form = useFormContext<CreatePartnerParams>();

  const contractFileInputRef = useRef<HTMLInputElement>(null);

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 mb-12">
      <p className="text-2xl font-bold text-primary">Draft cooperation agreement</p>
      <p
        className={cn(
          "text-base font-normal text-primary",
          form.formState.errors?.step_1?.contract_file && "text-error"
        )}
      >
        Upload the contract
      </p>
      {form.watch("step_1.contract_file") && (
        <div className="flex items-center gap-2">
          <Pdf className="w-9 h-9" />
          <p className="font-bold text-tonal-dark-cream-20">{form.watch("step_1.contract_file").name}</p>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Question className="size-5 fill-secondary transition-all duration-300" />
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-white shadow-elevation-04-1 p-5 w-64">
                <p className="text-primary">Partner contract</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      )}
      <Divider initialMarginDisabled />
      <input
        type="file"
        ref={contractFileInputRef}
        className="hidden"
        onChange={(e) => {
          form.setValue("step_1.contract_file", e.target.files?.[0]);
        }}
      />
      <div className="max-w-56">
        <Button
          type="button"
          color={!!form.formState.errors?.step_1?.contract_file ? "red" : "light-blue"}
          size="medium"
          variant="text"
          leadingIcon={<Upload />}
          onClick={() => contractFileInputRef.current?.click()}
        >
          Upload Document
        </Button>
      </div>
    </div>
  );
}
