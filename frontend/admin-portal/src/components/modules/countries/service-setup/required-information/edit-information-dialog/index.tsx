"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DragFile } from "@/components/ui/drag-file";
import { Textarea } from "@/components/ui/textarea";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { updateRequiredInformation } from "@/lib/api/required-information";
import { uploadFile } from "@/lib/api/upload-files";
import { queryClient } from "@/lib/react-query";
import { RequiredInformation, UpdateRequiredInformation } from "@/types/service-setup/required-information";
import { Role } from "@/utils/user";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import React, { ReactNode, useEffect, useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import MultipleSelector from "@/components/ui/multiple-selector";
import { ChevronDown } from "lucide-react";
import { REQUIRED_INFORMATION_TYPES } from "..";
import { CharacterCounter } from "@/components/ui/character-counter";
import { Error } from "@interzero/oneepr-react-ui/Icon";

// Helper function to get label and placeholder based on type
const getFieldLabels = (type: string) => {
  switch (type) {
    case "DOCUMENT":
      return {
        label: "Document title *",
        placeholder: "Enter document title",
        errorMessage: "Document title is required",
      };
    case "FILE":
      return {
        label: "File title *",
        placeholder: "Enter file title",
        errorMessage: "File title is required",
      };
    case "IMAGE":
      return {
        label: "Image title *",
        placeholder: "Enter image title",
        errorMessage: "Image title is required",
      };
    default:
      return {
        label: "Question to be answered *",
        placeholder: "Enter question to be answered",
        errorMessage: "Question is required",
      };
  }
};

const editInformationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().default(""),
  packaging_service_ids: z.array(z.string()).min(1, "Please select at least one service type"),
  file: z
    .any()
    .optional()
    .refine(
      (file) => {
        // Skip validation on server side
        if (typeof window === "undefined") return true;
        // On client side, check if it's a File instance when required
        return file instanceof File || file === undefined;
      },
      { message: "Invalid file type" }
    ),
});

type EditInformationFormData = z.infer<typeof editInformationSchema>;

interface RequiredInformationFormItem {
  id: number;
  name: string;
  description: string;
  type: "TEXT" | "NUMBER" | "DOCUMENT" | "FILE" | "IMAGE";
  file_id: string | null;
  has_criteria?: boolean;
  packaging_service_ids?: number[];
  file?: {
    id: string;
    name: string;
    original_name: string;
    size: string;
  } | null;
  description_visible?: boolean;
  country_id?: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
}

interface EditInformationDialogProps {
  children: ReactNode;
  requiredInformation: RequiredInformation | RequiredInformationFormItem;
}

export function EditInformationDialog({ children, requiredInformation }: EditInformationDialogProps) {
  const { country, packagingServices } = useServiceSetup();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const { data: session } = useSession();

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    clearErrors,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<EditInformationFormData>({
    resolver: zodResolver(editInformationSchema),
    defaultValues: {
      name: requiredInformation.name,
      description: requiredInformation.description || "",
      packaging_service_ids: requiredInformation.packaging_service_ids?.map(String) || [],
    },
  });

  const { mutate: updateInformation, isPending } = useMutation({
    mutationFn: async (data: UpdateRequiredInformation & { file?: File; userId: number; userRole: Role }) => {
      let file_id = requiredInformation.file_id;

      // Upload new file if provided
      if (data.file) {
        const file = await uploadFile({
          user_id: data.userId,
          user_role: data.userRole,
          file: data.file,
          document_type: "COUNTRY_DOCUMENT",
          country_id: String(country.id),
        });
        file_id = file?.id || null;
      }

      const updateData: UpdateRequiredInformation = {
        name: data.name,
        description: data.description,
        packaging_service_ids: data.packaging_service_ids,
        file_id,
      };

      return updateRequiredInformation(requiredInformation.id, updateData);
    },
  });

  // Reset form when dialog opens with current data
  useEffect(() => {
    if (isDialogOpen) {
      reset({
        name: requiredInformation.name,
        description: requiredInformation.description || "",
        packaging_service_ids: requiredInformation.packaging_service_ids?.map(String) || [],
      });

      if ("file" in requiredInformation && requiredInformation.file) {
        const fileData = requiredInformation.file;
        const pseudoFile = new File([""], fileData.original_name || fileData.name, {
          type: requiredInformation.type === "IMAGE" ? "image/jpeg" : "application/pdf",
          lastModified: Date.now(),
        });
        Object.defineProperty(pseudoFile, "size", {
          value: parseInt(fileData.size) || 0,
          writable: false,
        });
        setCurrentFile(pseudoFile);
      } else {
        setCurrentFile(null);
      }
    }
  }, [isDialogOpen, requiredInformation, reset]);

  async function handleFormSubmit(data: EditInformationFormData) {
    if (!session) return;

    const userId = session.user.id;
    const userRole = session.user.role;

    const serviceTypeIds = data.packaging_service_ids.map((id) => parseInt(id));

    updateInformation(
      {
        name: data.name,
        description: data.description,
        packaging_service_ids: serviceTypeIds,
        file: data.file,
        userId,
        userRole,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["service-setup-required-informations", country.code] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
          enqueueSnackbar("Required information updated successfully", { variant: "success" });
          setIsDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar("Failed to update required information", { variant: "error" });
        },
      }
    );
  }

  function handleAddFile(file: File) {
    setValue("file", file);
    clearErrors("file");
  }

  const truncateText = (text: string, maxLength: number = 44) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + "...";
  };

  const serviceTypeOptions = useMemo(() => {
    if (!packagingServices || packagingServices.length === 0) {
      return [];
    }

    return packagingServices.map((service) => ({
      value: String(service.id),
      label: truncateText(`EPR Compliance Packaging - ${service.name}`, 44),
      fullName: `EPR Compliance Packaging - ${service.name}`,
    }));
  }, [packagingServices]);

  function handleDialogOpenChange(open: boolean) {
    setIsDialogOpen(open);
  }

  const showFileUpload = ["DOCUMENT", "FILE", "IMAGE"].includes(requiredInformation.type);
  const fileAccept = requiredInformation.type === "IMAGE" ? "image/png,image/jpeg,image/jpg" : "application/pdf";

  const typeInfo = REQUIRED_INFORMATION_TYPES[requiredInformation.type];

  const nameValue = watch("name") || "";
  const descriptionValue = watch("description") || "";

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <div onClick={(e) => e.stopPropagation()}>{children}</div>
      </DialogTrigger>
      <DialogContent className="bg-[#F0F0EF] max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Required Information</DialogTitle>
          <DialogDescription>Update the existing information for the licensing process.</DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="mt-6 space-y-6">
          {/* Display the type (read-only) */}
          <div className="flex items-center gap-3 p-3 bg-surface-02 rounded-lg">
            <div className="h-10 w-10 flex-none flex items-center justify-center">{typeInfo?.icon()}</div>
            <div>
              <p className="text-primary text-sm font-bold">{typeInfo?.name}</p>
              <p className="text-sm text-tonal-dark-cream-50">{typeInfo?.description}</p>
            </div>
          </div>

          {/* Service Types Selection */}
          <div>
            <label className="block text-base  mb-2">{"Service type *"}</label>
            {serviceTypeOptions.length === 0 ? (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  No service types available. Please add service types in the Service details section first.
                </p>
              </div>
            ) : (
              <Controller
                control={control}
                name="packaging_service_ids"
                render={({ field }) => (
                  <MultipleSelector
                    badgeClassName="bg-[#FFF3E1] border-0 font-centra text-base leading-5 font-normal"
                    value={serviceTypeOptions.filter((opt) => field.value?.includes(opt.value))}
                    options={serviceTypeOptions}
                    onChange={(selected) => field.onChange(selected.map((opt) => opt.value))}
                    placeholder="Select"
                    hidePlaceholderWhenSelected={true}
                    disabled={isSubmitting || isPending}
                    emptyIndicator={<span>No service types available</span>}
                    invalid={!!errors.packaging_service_ids}
                    disableInput={true}
                    hideSearchIcon={true}
                    dropdownIcon={<ChevronDown className="h-5 w-5 flex-shrink-0" style={{ color: "#002652" }} />}
                  />
                )}
              />
            )}
            {errors.packaging_service_ids && (
              <div className="flex items-center gap-2 mt-2">
                <Error className="size-5 fill-error flex-shrink-0" />
                <p className="text-sm text-error">Service type is required</p>
              </div>
            )}
          </div>

          {/* Name Input */}
          <div>
            <div className="[&_input]:text-primary [&_input]:opacity-80">
              <Input
                label={getFieldLabels(requiredInformation.type).label}
                placeholder={getFieldLabels(requiredInformation.type).placeholder}
                {...register("name")}
                variant={errors.name ? "error" : "default"}
                enabled={!isSubmitting && !isPending}
                maxLength={150}
              />
              {errors.name && (
                <div className="flex items-center gap-2 mt-2">
                  <Error className="size-5 fill-error flex-shrink-0" />
                  <p className="text-sm text-error">{getFieldLabels(requiredInformation.type).errorMessage}</p>
                </div>
              )}
            </div>
            <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
          </div>

          {/* Description */}
          <div>
            <Textarea
              label="Help text"
              placeholder="Enter help text"
              rows={4}
              className="text-base text-primary/80 resize-none"
              maxLength={350}
              {...register("description")}
              disabled={isSubmitting || isPending}
            />
            <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={350} />
          </div>

          {/* File Upload (only for DOCUMENT, FILE, IMAGE types) */}
          {showFileUpload && (
            <DragFile
              title="Upload a template"
              description={currentFile ? "" : "or drag it here"}
              onFile={handleAddFile}
              selectedFile={currentFile || undefined}
              errorMessage={
                typeof errors.file?.message === "string" ? errors.file.message : errors.file?.message?.toString()
              }
              accept={fileAccept}
              disabled={isSubmitting || isPending}
              hideDownloadButton={true}
            />
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-end gap-4">
            <Button
              type="button"
              variant="outlined"
              color="dark-blue"
              size="medium"
              className="rounded-full"
              onClick={() => setIsDialogOpen(false)}
              disabled={isSubmitting || isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              className="rounded-full"
              disabled={isSubmitting || isPending}
            >
              {isSubmitting || isPending ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
