import { Country } from "../country";
import { CountryPriceList } from "./country-price-list";
import { OtherCost } from "./other-cost";
import { PackagingService } from "./packaging-service";
import { PriceList } from "./price-list";
import { ReportSet } from "./report-set";
import { ReportSetColumn } from "./report-set-column";
import { ReportSetColumnFraction } from "./report-set-column-fraction";
import { ReportSetFraction } from "./report-set-fraction";
import { ReportSetFrequency } from "./report-set-frequency";
import { ReportSetPriceList } from "./report-set-price-list";
import { RepresentativeTier } from "./representative-tier";
import { RequiredInformation } from "./required-information";
export type { ObligationCheckSection } from "./obligation-check-section";

export type FullReportSet = ReportSet & {
  packaging_service: {
    id: number;
    name: string;
  };
  fractions: (ReportSetFraction & {
    children: (ReportSetFraction & {
      children: ReportSetFraction[];
    })[];
  })[];
  columns: (ReportSetColumn & {
    children: (ReportSetColumn & {
      fractions: ReportSetColumnFraction[];
    })[];
  })[];
  price_lists: (ReportSetPriceList & {
    items: { price_list_id: number; fraction_code: string; price: number }[];
  })[];
  has_criteria?: boolean;
};

export type SetupPriceList = CountryPriceList & {
  price_list: PriceList;
};

export type ServiceSetup = Country & {
  packaging_services: (PackagingService & {
    report_set_frequencies: ReportSetFrequency[];
    report_sets: FullReportSet[];
  })[];
  representative_tiers: RepresentativeTier[];
  required_informations: (RequiredInformation & {
    file: {
      country_id: string;
      created_at: string;
      creator_type: string;
      document_type: string;
      extension: string;
      id: string;
      name: string;
      original_name: string;
      size: string;
      updated_at: string;
      user_id: string;
    };
  })[];
  other_costs: OtherCost[];
  country_price_lists: SetupPriceList[];
};
