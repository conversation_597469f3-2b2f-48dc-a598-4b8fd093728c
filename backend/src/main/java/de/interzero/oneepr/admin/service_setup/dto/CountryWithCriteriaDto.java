package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CriteriaDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.List;

/**
 * A helper Data Transfer Object created to represent the nested 'country' object
 * within the RepresentativeTierResponseDto.
 * <p>
 * It originates from the 'include' statement in the original Prisma query, which selectively
 * included 'criterias' that were filtered by type and mode. This DTO is necessary to
 * structure the JSON response to match the shape produced by the NestJS service,
 * embedding the list of filtered criteria within the country object.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CountryWithCriteriaDto {

    @Schema(
            description = "The unique identifier of the country.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The name of the country.",
            example = "Germany"
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "The ISO 3166-1 alpha-2 code of the country.",
            example = "DE"
    )
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "URL of the country's flag image.",
            example = "http://example.com/de.png"
    )
    @JsonProperty("flag_url")
    private String flagUrl;

    @Schema(description = "Indicates if an authorized representative is obligated for this country.")
    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated;

    @Schema(description = "Indicates if other costs are obligated for this country.")
    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated;

    @Schema(description = "Indicates if the country is published and visible to users.")
    @JsonProperty("is_published")
    private Boolean isPublished;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of associated criteria filtered for 'REPRESENTATIVE_TIER' and 'COMMITMENT'.")
    @JsonProperty("criterias")
    private List<CriteriaDto> criterias;
}