package de.interzero.oneepr.admin.price_list;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "price_list",
        schema = "public"
)
public class PriceList {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "price_list_id_seq"
    )
    @SequenceGenerator(
            name = "price_list_id_seq",
            sequenceName = "price_list_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @NotNull
    @Column(
            name = "end_date",
            nullable = false
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @Column(name = "basic_price")
    @JsonProperty("basic_price")
    private Integer basicPrice;

    @Column(name = "minimum_price")
    @JsonProperty("minimum_price")
    private Integer minimumPrice;

    @Column(name = "registration_fee")
    @JsonProperty("registration_fee")
    private Integer registrationFee;

    @Column(name = "variable_handling_fee")
    @JsonProperty("variable_handling_fee")
    private Double variableHandlingFee;

    @Column(name = "price")
    @JsonProperty("price")
    private Integer price;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "condition_type",
            nullable = false
    )
    @JsonProperty("condition_type")
    private ConditionType conditionType;

    @NotNull
    @Column(
            name = "condition_type_value",
            nullable = false
    )
    @JsonProperty("condition_type_value")
    private String conditionTypeValue;

    @Column(name = "handling_fee")
    @JsonProperty("handling_fee")
    private Integer handlingFee;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "thresholds")
    @JsonProperty("thresholds")
    private String thresholds;

    @OneToMany(mappedBy = "priceList")
    @JsonIgnore
    @JsonProperty("country_price_lists")
    private List<CountryPriceList> countryPriceLists = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    public enum Type {
        EU_LICENSE,
        DIRECT_LICENSE,
        ACTION_GUIDE
    }

    public enum ConditionType {
        LICENSE_YEAR
    }
}