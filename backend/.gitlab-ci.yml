# ----------------- G<PERSON><PERSON>BAL CONFIGURATIONS ----------------- #
default:
  tags: ["aws"]

image: gitlab.interzero.de/software-development/dependency_proxy/containers/node:20.18.1-alpine

# Global variables
variables:
  COMPONENT: be
  COMPONENT_PATH: backend
  DEPLOYMENT_FILE_BACKEND: ${COMPONENT_PATH}/deployment.yml

# ----------------- TEMPLATES ----------------- #
.docker_build_template: &docker_build_template
  image:
    name: gcr.io/kaniko-project/executor:v1.14.0-debug
    entrypoint: [""]
  before_script:
    # Create Docker authentication for Kanik<PERSON> (existing DOCKER_AUTH_CONFIG with ECR token)
    - mkdir -p /kaniko/.docker
    - echo "Replacing ECR token placeholder in DOCKER_AUTH_CONFIG..."
    - echo "$DOCKER_AUTH_CONFIG" | sed 's/<ECR_TOKEN>/'$ECR_TOKEN'/g' > /kaniko/.docker/config.json
  script:
    - /kaniko/executor
      --context $CI_PROJECT_DIR/${COMPONENT_PATH}
      --dockerfile $CI_PROJECT_DIR/${COMPONENT_PATH}/Dockerfile${DOCKERFILE_EXTENSION}
      --build-arg PROJECT_NAME=$SHORT_PROJECT_NAME
      --build-arg COMPONENT=$COMPONENT
      --build-arg CI_COMMIT_REF_SLUG=$CI_COMMIT_REF_SLUG
      --cache=true
      --cache-repo=811261252380.dkr.ecr.eu-central-1.amazonaws.com/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}-cache
      --destination 811261252380.dkr.ecr.eu-central-1.amazonaws.com/${SHORT_PROJECT_NAME}-${COMPONENT_NAME}:${CI_COMMIT_REF_SLUG}

.deployment_template: &deployment_template
  stage: deploy
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.16.4
  script:
    - echo "Deploy to ${DEPLOY_ENV} server"
    - echo "Delete old service"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - echo "Add new service"
    - kubectl config view
    - sed -i "s/<BRANCH>/${CI_COMMIT_REF_SLUG}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<NAMESPACE>/${NAMESPACE}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<COMPONENT>/${COMPONENT}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<SPRING_ENVIRONMENT>/${SPRING_ENVIRONMENT}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<PROJECT_NAME>/${SHORT_PROJECT_NAME}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<NEXUS_PORT>/${NEXUS_PORT}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<HOSTNAME>/${HOSTNAME}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<ALB_SCHEME>/${ALB_SCHEME}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<ALB_GROUP>/${ALB_GROUP}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<PREFIX>/${PREFIX}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<FRONTEND_HOSTNAME>/${FRONTEND_HOSTNAME}/g" ${DEPLOYMENT_FILE_BACKEND}
    # Customer IO
    - sed -i "s/<CUSTOMER_IO_API_KEY>/${CUSTOMER_IO_API_KEY}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<CUSTOMER_IO_SITE_ID>/${CUSTOMER_IO_SITE_ID}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<CUSTOMER_IO_REGION>/${CUSTOMER_IO_REGION}/g" ${DEPLOYMENT_FILE_BACKEND}
    # Mail 
    - sed -i "s/<SPRING_MAIL_HOST>/${SPRING_MAIL_HOST}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<SPRING_MAIL_PORT>/${SPRING_MAIL_PORT}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<MAIL_GATEWAY>/${MAIL_GATEWAY}/g" ${DEPLOYMENT_FILE_BACKEND}
    # Database
    - sed -i "s/<DB_PASSWORD>/${DB_PASSWORD}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<DB_SERVICE_NAME>/${DB_SERVICE_NAME}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<DB_URL>/${DB_URL}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<DB_USER>/${DB_USER}/g" ${DEPLOYMENT_FILE_BACKEND}
    # CPU and RAM
    - sed -i "s/<CPU_REQUEST>/${CPU_REQUEST}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<CPU_LIMIT>/${CPU_LIMIT}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<MEMORY_REQUEST>/${MEMORY_REQUEST}/g" ${DEPLOYMENT_FILE_BACKEND}
    - sed -i "s/<MEMORY_LIMIT>/${MEMORY_LIMIT}/g" ${DEPLOYMENT_FILE_BACKEND}
    # Deploy
    - cat ${DEPLOYMENT_FILE_BACKEND}
    - kubectl apply -f ${DEPLOYMENT_FILE_BACKEND}

be_set_feature_release_env:
  stage: deploy
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - echo $CI_COMMIT_REF_SLUG
    - MODIFIED_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - echo "DYNAMIC_ENV_URL=https://${SHORT_PROJECT_NAME}-${COMPONENT}-${MODIFIED_CI_COMMIT_REF_SLUG}.feature.interzero.dev" >> deploy.env
  environment:
    name: review/${CI_COMMIT_REF_NAME}/backend
    url: $DYNAMIC_ENV_URL
    auto_stop_in: 21 days
    on_stop: be_destroy_feature_release
  artifacts:
    reports:
      dotenv: deploy.env

.deployment_template_feature_release: &deployment_template_feature_release
  <<: *deployment_template
# ----------------- DEPENDENCY INSTALLATION ----------------- #
be_install_dependencies:
  <<: *docker_build_template
  stage: install_dependencies
  tags: ["eks-entw-qat-with-s3"]
  dependencies:
    - generate_ecr_token
  only:
    - develop
    - staging
    - main
    - merge_requests
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}-build"
    DOCKERFILE_EXTENSION: ".build"

copy_database:
  stage: install_dependencies
  image: alpine:3.20.3
  tags: ["eks-entw-qat-with-s3"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - env
    - apk add --no-cache postgresql-client
    - echo "Creating a copy of the database for ${CI_COMMIT_REF_SLUG}"
    - ./backend/scripts/create_database_copy.sh ${PROJECT_NAME}-entw ${PROJECT_NAME}-${CI_COMMIT_REF_SLUG} ${DB_USER} ${DB_PASSWORD} ${DB_URL} 3000
  environment:
    name: review/${CI_COMMIT_REF_NAME}/database
    auto_stop_in: 21 days
    on_stop: delete_database

# ----------------- TESTING ----------------- #
be_unit_tests:
  stage: test
  image: 811261252380.dkr.ecr.eu-central-1.amazonaws.com/${SHORT_PROJECT_NAME}-${COMPONENT}-build:${CI_COMMIT_REF_SLUG}
  tags: ["eks-entw-qat-with-s3"]
  services:
    - name: postgres:17.3
      alias: postgres-db-test
      command: ["postgres", "-c", "max_connections=2500"]
  variables:
    POSTGRES_DB: test_db
    POSTGRES_USER: test_user
    POSTGRES_PASSWORD: test_password
    POSTGRES_HOST_AUTH_METHOD: trust
  only:
    - develop
    - staging
    - main
    - merge_requests
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - cd /app
    - mvn test -Dspring.profiles.active=pipeline

# ----------------- DOCKER IMAGE BUILD ----------------- #
be_docker_build_develop:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - develop
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "dev"

be_docker_build_staging:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - staging
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "qat"

be_docker_build_main:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - main
  variables:
    NEXUS_PORT: "${NEXUS_PROD}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "prod"

be_docker_build_feature_release:
  <<: *docker_build_template
  stage: build_docker_image
  tags: ["eks-entw-qat-with-s3"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    COMPONENT_NAME: "${COMPONENT}"
    DOCKERFILE_EXTENSION: ".prod"
    SPRING_ENVIRONMENT: "dev"

# ----------------- DEPLOYMENT ----------------- #
be_deploy_develop:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - develop
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.entw.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.entw.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    SPRING_ENVIRONMENT: "dev"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "oneepr-entw"
    PREFIX: ""
    CUSTOMER_IO_API_KEY: "$CUSTOMER_IO_API_KEY"
    CUSTOMER_IO_SITE_ID: "$CUSTOMER_IO_SITE_ID"
    CUSTOMER_IO_REGION: "$CUSTOMER_IO_REGION"
    SPRING_MAIL_HOST: ""
    SPRING_MAIL_PORT: ""
    MAIL_GATEWAY: "test"
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.entw.interzero.dev"

be_deploy_staging:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-entw-qat"]
  only:
    - staging
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "qat-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.qat.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.qat.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-entw-qat-vpn"
    SPRING_ENVIRONMENT: "qat"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "oneepr-qat"
    PREFIX: ""
    CUSTOMER_IO_API_KEY: "$CUSTOMER_IO_API_KEY"
    CUSTOMER_IO_SITE_ID: "$CUSTOMER_IO_SITE_ID"
    CUSTOMER_IO_REGION: "$CUSTOMER_IO_REGION"
    SPRING_MAIL_HOST: ""
    SPRING_MAIL_PORT: ""
    MAIL_GATEWAY: "test"
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.qat.interzero.dev"

be_deploy_main:
  <<: *deployment_template
  stage: deploy
  tags: ["eks-prod"]
  only:
    - main
  when: manual
  variables:
    DEPLOY_ENV: "PROD"
    NAMESPACE: "prod-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_PROD}"
    HOSTNAME: "${SHORT_PROJECT_NAME}-${COMPONENT}.vpn.interzero.dev"
    FRONTEND_HOSTNAME: "${SHORT_PROJECT_NAME}.vpn.interzero.dev"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-prod-vpn"
    SPRING_ENVIRONMENT: "prod"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    DB_SERVICE_NAME: "oneepr-prod"
    PREFIX: ""
    CUSTOMER_IO_API_KEY: "$CUSTOMER_IO_API_KEY"
    CUSTOMER_IO_SITE_ID: "$CUSTOMER_IO_SITE_ID"
    CUSTOMER_IO_REGION: "$CUSTOMER_IO_REGION"
    SPRING_MAIL_HOST: ""
    SPRING_MAIL_PORT: ""
    MAIL_GATEWAY: "customerio"
  environment:
    name: ${CI_COMMIT_REF_NAME}/backend
    url: "https://${SHORT_PROJECT_NAME}-${COMPONENT}.vpn.interzero.dev"

be_deploy_feature_release:
  <<: *deployment_template_feature_release
  stage: deploy
  tags: ["eks-entw-qat"]
  before_script:
    - export SHORTEN_CI_COMMIT_REF_SLUG=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/--.*//')
    - export HOSTNAME="${SHORT_PROJECT_NAME}-${COMPONENT}-${SHORTEN_CI_COMMIT_REF_SLUG}.feature.interzero.dev"
    - export FRONTEND_HOSTNAME="${SHORT_PROJECT_NAME}-${SHORTEN_CI_COMMIT_REF_SLUG}.feature.interzero.dev"
    - export DB_SERVICE_NAME="oneepr-${CI_COMMIT_REF_SLUG}"
    - export PREFIX="${CI_COMMIT_REF_SLUG}-"
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  variables:
    DEPLOY_ENV: "ENTW/QAT"
    NAMESPACE: "entw-${PROJECT_NAME}"
    NEXUS_PORT: "${NEXUS_ENTW_QAT}"
    ALB_SCHEME: "internal"
    ALB_GROUP: "eks-feature-vpn"
    SPRING_ENVIRONMENT: "dev"
    CPU_REQUEST: "250m"
    CPU_LIMIT: "800m"
    MEMORY_REQUEST: "4Gi"
    MEMORY_LIMIT: "4Gi"
    CUSTOMER_IO_API_KEY: "$CUSTOMER_IO_API_KEY"
    CUSTOMER_IO_SITE_ID: "$CUSTOMER_IO_SITE_ID"
    CUSTOMER_IO_REGION: "$CUSTOMER_IO_REGION"
    SPRING_MAIL_HOST: ""
    SPRING_MAIL_PORT: ""
    MAIL_GATEWAY: "test"

# ----------------- CLEANUP ----------------- #
be_destroy_feature_release:
  image: gitlab.interzero.de/software-development/dependency_proxy/containers/dtzar/helm-kubectl:3.16.4
  stage: cleanup
  when: manual
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  tags: ["eks-entw-qat"]
  script:
    - echo "Stopping environment ${CI_COMMIT_REF_SLUG}"
    - kubectl delete -n ${NAMESPACE} deployment ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} service ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} ingress ${PREFIX}${SHORT_PROJECT_NAME}-${COMPONENT} --ignore-not-found
    - kubectl delete -n ${NAMESPACE} secret ${PREFIX}${SHORT_PROJECT_NAME}-secret --ignore-not-found
  variables:
    NAMESPACE: "entw-${PROJECT_NAME}"
  environment:
    name: review/${CI_COMMIT_REF_NAME}/backend
    action: stop

delete_database:
  stage: cleanup
  when: manual
  image: alpine:3.20.3
  tags: ["eks-entw-qat"]
  only:
    - /^feature\/.*$/
    - /^release\/.*$/
  script:
    - apk add --no-cache postgresql-client
    - echo "Creating a copy of the database for ${CI_COMMIT_REF_SLUG}"
    - ./backend/scripts/destroy_database.sh ${PROJECT_NAME}-${CI_COMMIT_REF_SLUG} ${DB_USER} ${DB_PASSWORD} ${DB_URL} 3000
  environment:
    name: review/${CI_COMMIT_REF_NAME}/database
    action: stop
