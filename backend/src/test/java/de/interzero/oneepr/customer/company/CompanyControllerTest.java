package de.interzero.oneepr.customer.company;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.company.dto.*;
import de.interzero.oneepr.customer.company_billing.CompanyBilling;
import de.interzero.oneepr.customer.company_billing.CompanyBillingRepository;
import de.interzero.oneepr.customer.company_email.CompanyEmail;
import de.interzero.oneepr.customer.company_email.CompanyEmailRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.entity.CompanyAddress;
import de.interzero.oneepr.customer.entity.CompanyAddressRepository;
import de.interzero.oneepr.customer.entity.CompanyContact;
import de.interzero.oneepr.customer.partner.Partner;
import de.interzero.oneepr.customer.partner.PartnerRepository;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link CompanyController}.
 * This class tests all APIs, services, and repositories without using any mocks.
 * All dependencies are autowired to test the complete integration flow.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CompanyControllerTest {

    // WireMock server for mocking external API calls
    private static final WireMockServer wireMockServer = new WireMockServer(WireMockConfiguration.options()
                                                                                    .dynamicPort());

    // Static initialization of WireMock server
    static {
        wireMockServer.start();
        WireMock.configureFor("localhost", wireMockServer.port());
    }

    // Configure application properties to use WireMock server
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Override the VIES and EVATR API URLs to point to our WireMock server
        registry.add("app.vies.api.url", () -> wireMockServer.baseUrl() + "/vies");
        registry.add("app.evatr.api.url", () -> wireMockServer.baseUrl() + "/evatr");
    }

    private static final String API_BASE_URL = Api.COMPANY;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private PartnerRepository partnerRepository;

    @Autowired
    private CompanyAddressRepository companyAddressRepository;

    @Autowired
    private CompanyEmailRepository companyEmailRepository;

    @Autowired
    private CompanyBillingRepository companyBillingRepository;

    @Autowired
    private CompanyContactRepository companyContactRepository;

    @Value("classpath:__files/evatr-test.xml")
    private Resource xmlResource;

    @Value("classpath:__files/vies-test-response.json")
    private Resource viesTestResponse;

    private Customer testCustomer;

    private Partner testPartner;

    private Company testCompany;

    private CompanyAddress testAddress;

    private CompanyContact testContact;

    @BeforeEach
    void setUp() {
        objectMapper.registerModule(new JavaTimeModule());

        // Create test customer
        testCustomer = new Customer();
        testCustomer.setFirstName("John");
        testCustomer.setLastName("Doe");
        testCustomer.setEmail("john.doe." + Instant.now().toEpochMilli() + "@example.com");
        testCustomer.setUserId(101);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer.setType(Customer.Type.REGULAR);
        testCustomer.setIsActive(true);
        testCustomer = customerRepository.saveAndFlush(testCustomer);

        // Create test partner
        testPartner = new Partner();
        testPartner.setFirstName("Jane");
        testPartner.setLastName("Smith");
        testPartner.setEmail("jane.smith." + Instant.now().toEpochMilli() + "@example.com");
        testPartner.setUserId(102);
        testPartner.setCreatedAt(Instant.now());
        testPartner.setUpdatedAt(Instant.now());
        testPartner.setChecked(true);
        testPartner = partnerRepository.saveAndFlush(testPartner);

        // Create test address
        testAddress = new CompanyAddress();
        testAddress.setCountryCode("DE");
        testAddress.setAddressLine("Test Address Line");
        testAddress.setCity("Berlin");
        testAddress.setZipCode("12345");
        testAddress.setStreetAndNumber("Test Street 123");
        testAddress.setAdditionalAddress("Additional Address");
        testAddress.setCreatedAt(Instant.now());
        testAddress.setUpdatedAt(Instant.now());
        testAddress = companyAddressRepository.saveAndFlush(testAddress);

        // Create test contact
        testContact = new CompanyContact();
        testContact.setName("Test Contact");
        testContact.setEmail("<EMAIL>");
        testContact.setPhoneMobile("+49*********");
        testContact.setCreatedAt(Instant.now());
        testContact.setUpdatedAt(Instant.now());
        testContact = companyContactRepository.saveAndFlush(testContact);

        // Create test company
        testCompany = new Company();
        testCompany.setName("Test Company GmbH");
        testCompany.setDescription("Test Company Description");
        testCompany.setVat("DE*********");
        testCompany.setCustomer(testCustomer);
        testCompany.setAddress(testAddress);
        testCompany.setContacts(testContact);
        testCompany.setCreatedAt(Instant.now());
        testCompany.setUpdatedAt(Instant.now());
        testCompany = companyRepository.saveAndFlush(testCompany);

        // Update contact with company reference
        testContact.setCompany(testCompany);
        companyContactRepository.saveAndFlush(testContact);
    }

    @AfterEach
    void tearDown() {
        // Reset WireMock stubs after each test
        WireMock.reset();

        // Clean up database
        companyRepository.deleteAllInBatch();
        companyContactRepository.deleteAllInBatch();
        companyAddressRepository.deleteAllInBatch();
        companyEmailRepository.deleteAllInBatch();
        companyBillingRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();
        partnerRepository.deleteAllInBatch();
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findAll_ShouldReturnAllCompanies() throws Exception {
        mockMvc.perform(get(API_BASE_URL))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(greaterThanOrEqualTo(1))))
                .andExpect(jsonPath("$[0].id", is(testCompany.getId())))
                .andExpect(jsonPath("$[0].name", is("Test Company GmbH")))
                .andExpect(jsonPath("$[0].vat", is("DE*********")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findOne_ShouldReturnCompanyById() throws Exception {
        mockMvc.perform(get(API_BASE_URL + "/" + testCompany.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCompany.getId())))
                .andExpect(jsonPath("$.name", is("Test Company GmbH")))
                .andExpect(jsonPath("$.description", is("Test Company Description")))
                .andExpect(jsonPath("$.vat", is("DE*********")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findOne_ShouldReturn404_WhenCompanyNotFound() throws Exception {
        mockMvc.perform(get(API_BASE_URL + "/99999")).andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(
            username = "102",
            roles = {TestRole.ADMIN}
    )
    void findOneByPartner_ShouldReturnCompanyByPartnerId() throws Exception {
        // Create company with partner
        Company partnerCompany = new Company();
        partnerCompany.setName("Partner Company");
        partnerCompany.setDescription("Partner Company Description");
        partnerCompany.setVat("DE987654321");
        partnerCompany.setPartner(testPartner);
        partnerCompany.setAddress(testAddress);
        partnerCompany.setContacts(testContact);
        partnerCompany.setCreatedAt(Instant.now());
        partnerCompany.setUpdatedAt(Instant.now());
        partnerCompany.setCustomer(testCustomer);
        partnerCompany = companyRepository.saveAndFlush(partnerCompany);

        mockMvc.perform(get(API_BASE_URL + "/partner/" + testPartner.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(partnerCompany.getId())))
                .andExpect(jsonPath("$.name", is("Partner Company")))
                .andExpect(jsonPath("$.vat", is("DE987654321")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findOneByPartner_ShouldReturn404_WhenPartnerNotFound() throws Exception {
        mockMvc.perform(get(API_BASE_URL + "/partner/99999")).andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldCreateNewCompanyWithCustomer() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setName("New Test Company");
        createDto.setVat("DE111222333");

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name", is("New Test Company")))
                .andExpect(jsonPath("$.vat", is("DE111222333")))
                .andExpect(jsonPath("$.description", is("Test Description")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldCreateNewCompanyWithPartner() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setPartnerId(testPartner.getId());
        createDto.setName("Partner Test Company");
        createDto.setVat("DE444555666");

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name", is("Partner Test Company")))
                .andExpect(jsonPath("$.vat", is("DE444555666")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn400_WhenBothVatAndTinProvided() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setVat("DE*********");
        createDto.setTin("*********");

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn409_WhenVatAlreadyExists() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setVat("DE*********"); // Same as testCompany

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isConflict());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn409_WhenLucidAlreadyExists() throws Exception {
        // Update test company with LUCID
        testCompany.setLucid("LUCID123");
        companyRepository.saveAndFlush(testCompany);

        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setVat("DE999888777");
        createDto.setLucid("LUCID123"); // Same as testCompany

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isConflict());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_ShouldUpdateExistingCompany() throws Exception {
        UpdateCompanyDto updateDto = new UpdateCompanyDto();
        updateDto.setName("Updated Company Name");
        updateDto.setDescription("Updated Description");
        updateDto.setWebsite("https://updated.example.com");

        mockMvc.perform(put(API_BASE_URL + "/" + testCompany.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated Company Name")))
                .andExpect(jsonPath("$.description", is("Updated Description")))
                .andExpect(jsonPath("$.website", is("https://updated.example.com")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_ShouldReturn400_WhenBothVatAndTinProvided() throws Exception {
        UpdateCompanyDto updateDto = new UpdateCompanyDto();
        updateDto.setVat("DE*********");
        updateDto.setTin("*********");

        mockMvc.perform(put(API_BASE_URL + "/" + testCompany.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void remove_ShouldDeleteCompany() throws Exception {
        mockMvc.perform(delete(API_BASE_URL + "/" + testCompany.getId())).andExpect(status().isOk());

        // Verify company is deleted
        mockMvc.perform(get(API_BASE_URL + "/" + testCompany.getId())).andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findOneCustomerMondayById_ShouldReturnOk() throws Exception {
        mockMvc.perform(get(API_BASE_URL + "/crm/" + testCustomer.getId())).andExpect(status().isOk());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findByVatId_ShouldValidateVatId() throws Exception {
        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("DE*********");
        vatIdDto.setCountryCode("DE");
        vatIdDto.setCompanyName("Test Company");
        vatIdDto.setCompanyCity("Berlin");
        vatIdDto.setCompanyZipcode("12345");
        vatIdDto.setCompanyStreet("Test Street 123");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.is_valid").exists());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findByVatId_ShouldReturnInvalid_WhenVatIdTooShort() throws Exception {
        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("D");
        vatIdDto.setCountryCode("DE");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.is_valid", is(false)))
                .andExpect(jsonPath("$.error", is("INVALID")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void findByVatId_ShouldReturnInvalid_WhenCountryCodeMismatch() throws Exception {
        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("DE*********");
        vatIdDto.setCountryCode("FR"); // Mismatch

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.is_valid", is(false)))
                .andExpect(jsonPath("$.error", is("INVALID")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.CUSTOMER}
    )
    void findOne_ShouldReturn403_WhenCustomerAccessesOtherCompany() throws Exception {
        // Create another customer
        Customer otherCustomer = new Customer();
        otherCustomer.setFirstName("Other");
        otherCustomer.setLastName("Customer");
        otherCustomer.setEmail("other." + Instant.now().toEpochMilli() + "@example.com");
        otherCustomer.setUserId(999);
        otherCustomer.setCreatedAt(Instant.now());
        otherCustomer.setUpdatedAt(Instant.now());
        otherCustomer.setType(Customer.Type.REGULAR);
        otherCustomer.setIsActive(true);
        otherCustomer = customerRepository.saveAndFlush(otherCustomer);

        // Create company for other customer
        Company otherCompany = new Company();
        otherCompany.setName("Other Company");
        otherCompany.setVat("DE999999999");
        otherCompany.setCustomer(otherCustomer);
        otherCompany.setAddress(testAddress);
        otherCompany.setContacts(testContact);
        otherCompany.setCreatedAt(Instant.now());
        otherCompany.setUpdatedAt(Instant.now());
        otherCompany = companyRepository.saveAndFlush(otherCompany);

        // Customer 101 trying to access company of customer 999
        mockMvc.perform(get(API_BASE_URL + "/" + otherCompany.getId())).andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn400_WhenNoCustomerOrPartnerProvided() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        // Don't set customer or partner ID

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn404_WhenCustomerNotFound() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(99999); // Non-existent customer

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldReturn404_WhenPartnerNotFound() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setPartnerId(99999); // Non-existent partner

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldCreateCompanyWithEmails() throws Exception {
        CreateCompanyDto createDto = createValidCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setName("Email Test Company");
        createDto.setVat("DE777888999");
        createDto.setEmails(Arrays.asList("<EMAIL>", "<EMAIL>"));

        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name", is("Email Test Company")))
                .andExpect(jsonPath("$.vat", is("DE777888999")));

        // Verify emails were created
        // Note: In a real integration test, you would verify the emails in the database
        // but since we're using @Transactional, the data will be rolled back
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_ShouldUpdateCompanyEmails() throws Exception {
        UpdateCompanyDto updateDto = new UpdateCompanyDto();
        updateDto.setEmails(Arrays.asList("<EMAIL>", "<EMAIL>"));
        updateDto.setName("Updated Company");
        updateDto.setDescription("Test Company Description");
        updateDto.setVat("DE*********");
        mockMvc.perform(put(API_BASE_URL + "/" + testCompany.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isOk());

        // In a real scenario, you would verify the emails were updated in the database
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_ShouldUpdateCompanyContact() throws Exception {
        UpdateCompanyDto updateDto = new UpdateCompanyDto();
        CreateCompanyContactDto newContact = new CreateCompanyContactDto();
        updateDto.setName("Updated Contact");
        updateDto.setDescription("Updated Company Description");
        newContact.setEmail("<EMAIL>");
        newContact.setPhoneMobile("+49987654321");
        updateDto.setContact(newContact);

        mockMvc.perform(put(API_BASE_URL + "/" + testCompany.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isOk());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void create_ShouldCreateCompanyWithAllFields() throws Exception {
        CreateCompanyDto createDto = createCompleteCreateCompanyDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setName("Complete Test Company");
        createDto.setVat("DE111222333");
        createDto.setDescription("Complete company with all fields");
        createDto.setManagingDirector("Jane Smith");
        createDto.setWebsite("https://complete-test.com");
        CreateCompanyBillingDto createCompanyBillingDto = new CreateCompanyBillingDto();
        createCompanyBillingDto.setCompanyName("Billing Company Name");
        createCompanyBillingDto.setCity("City");
        createCompanyBillingDto.setCountryCode("DE");
        createCompanyBillingDto.setZipCode("12345");
        createCompanyBillingDto.setStreetAndNumber("Street 123");
        createCompanyBillingDto.setFullName("Full Name");
        createCompanyBillingDto.setCountryName("Country");
        createDto.setBilling(createCompanyBillingDto);
        createDto.setEmails(List.of("<EMAIL>", "<EMAIL>"));


        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name", is("Complete Test Company")))
                .andExpect(jsonPath("$.vat", is("DE111222333")))
                .andExpect(jsonPath("$.description", is("Complete company with all fields")))
                .andExpect(jsonPath("$.website", is("https://complete-test.com")));

        // Verify all related entities were created
        // Find the created company
        Optional<Company> createdCompany = companyRepository.findByVat("DE111222333");
        Assertions.assertTrue(createdCompany.isPresent());
        // Verify address was created
        Assertions.assertNotNull(createdCompany.get().getAddress());
        Assertions.assertEquals("DE", createdCompany.get().getAddress().getCountryCode());
        Assertions.assertEquals("Complete Address Line", createdCompany.get().getAddress().getAddressLine());
        Assertions.assertEquals("Munich", createdCompany.get().getAddress().getCity());
        Assertions.assertEquals("80331", createdCompany.get().getAddress().getZipCode());

        // Verify contact was created
        Optional<CompanyContact> createdContact = companyContactRepository.findCompanyContactByCompany_Id(createdCompany.get()
                                                                                                                  .getId());
        Assertions.assertTrue(createdContact.isPresent());
        Assertions.assertEquals("Complete Contact", createdContact.get().getName());
        Assertions.assertEquals("<EMAIL>", createdContact.get().getEmail());
        Assertions.assertEquals("+49555666777", createdContact.get().getPhoneMobile());

        Optional<CompanyBilling> companyBillingByCompanyId = companyBillingRepository.findCompanyBillingByCompany_Id(
                createdCompany.get().getId());
        Assertions.assertTrue(companyBillingByCompanyId.isPresent());
        Assertions.assertEquals("DE", companyBillingByCompanyId.get().getCountryCode());
        Assertions.assertEquals("Country", companyBillingByCompanyId.get().getCountryName());
        Assertions.assertEquals("Street 123", companyBillingByCompanyId.get().getStreetAndNumber());

        List<String> emails = companyEmailRepository.findAllByCompany_Id(createdCompany.get().getId())
                .stream()
                .map(CompanyEmail::getEmail)
                .toList();
        Assertions.assertTrue(emails.contains("<EMAIL>"));
        Assertions.assertTrue(emails.contains("<EMAIL>"));

    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void update_ShouldUpdateCompanyWithAllFields() throws Exception {
        UpdateCompanyDto updateDto = createCompleteUpdateCompanyDto();
        updateDto.setName("Updated Complete Company");
        updateDto.setDescription("Updated complete description");
        updateDto.setManagingDirector("Updated Director");
        updateDto.setWebsite("https://updated-complete.com");
        updateDto.setEmails(List.of("<EMAIL>"));

        mockMvc.perform(put(API_BASE_URL + "/" + testCompany.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated Complete Company")))
                .andExpect(jsonPath("$.description", is("Updated complete description")))
                .andExpect(jsonPath("$.website", is("https://updated-complete.com")));

        // Verify all related entities were updated
        Company updatedCompany = companyRepository.findById(testCompany.getId()).orElse(null);
        Assertions.assertNotNull(updatedCompany);
        Assertions.assertEquals("Updated Complete Company", updatedCompany.getName());
        Assertions.assertEquals("Updated complete description", updatedCompany.getDescription());

        // Verify address was updated
        Assertions.assertNotNull(updatedCompany.getAddress());
        Assertions.assertEquals("Updated Address Line", updatedCompany.getAddress().getAddressLine());
        Assertions.assertEquals("Hamburg", updatedCompany.getAddress().getCity());
        Assertions.assertEquals("20095", updatedCompany.getAddress().getZipCode());

        // Verify contact was updated
        Optional<CompanyContact> updatedContact = companyContactRepository.findCompanyContactByCompany_Id(updatedCompany.getId());
        Assertions.assertTrue(updatedContact.isPresent());
        Assertions.assertNotNull(updatedContact.get());
        Assertions.assertEquals("Updated Contact", updatedContact.get().getName());
        Assertions.assertEquals("<EMAIL>", updatedContact.get().getEmail());
        Assertions.assertEquals("+49888999000", updatedContact.get().getPhoneMobile());

        // Verify other fields were updated
        Assertions.assertEquals("https://updated-complete.com", updatedCompany.getWebsite());
        Assertions.assertTrue(updatedCompany.getEmails()
                                      .stream()
                                      .map(CompanyEmail::getEmail)
                                      .toList()
                                      .contains("<EMAIL>"));

    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void validateVat_ShouldValidateGermanVatWithViesApi() throws Exception {
        // Setup WireMock to mock VIES API response
        String viesResponseJson = viesTestResponse.getContentAsString(StandardCharsets.UTF_8);

        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/vies"))
                                 .willReturn(WireMock.aResponse()
                                                     .withStatus(200)
                                                     .withHeader("Content-Type", "application/json")
                                                     .withBody(viesResponseJson)));

        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("DE*********");
        vatIdDto.setCountryCode("DE");
        vatIdDto.setCompanyName("Test Company GmbH");
        vatIdDto.setCompanyCity("Berlin");
        vatIdDto.setCompanyZipcode("12345");
        vatIdDto.setCompanyStreet("Test Street 123");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.system", is("VIES")))
                .andExpect(jsonPath("$.is_valid", is(true)))
                .andExpect(jsonPath("$.data.valid", is(true)))
                .andExpect(jsonPath("$.data.countryCode", is("DE")))
                .andExpect(jsonPath("$.data.vatNumber", is("*********")))
                .andExpect(jsonPath("$.data.name", is("Test Company GmbH")))
                .andExpect(jsonPath("$.data.address", is("Test Street 123, 12345 Berlin")));

        // Verify that the VIES API was called
        WireMock.verify(WireMock.postRequestedFor(WireMock.urlEqualTo("/vies")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void validateVat_ShouldValidateForeignVatWithEvatrApiAndParseXml() throws Exception {
        // Setup WireMock to mock EVATR API response with realistic XML
        String evatrXmlResponse = xmlResource.getContentAsString(StandardCharsets.UTF_8);

        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/evatr.*"))
                                 .willReturn(WireMock.aResponse()
                                                     .withStatus(200)
                                                     .withHeader("Content-Type", "application/xml")
                                                     .withBody(evatrXmlResponse)));

        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("FR*********");
        vatIdDto.setCountryCode("FR");
        vatIdDto.setCompanyName("Test Company SARL");
        vatIdDto.setCompanyCity("Paris");
        vatIdDto.setCompanyZipcode("75001");
        vatIdDto.setCompanyStreet("Test Rue 123");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.system", is("EVATR")))
                .andExpect(jsonPath("$.is_valid", is(true)))
                .andExpect(jsonPath("$.data.own_vat_id", is("DE257906838")))
                .andExpect(jsonPath("$.data.partner_vat_id", is("FR*********")))
                .andExpect(jsonPath("$.data.error_code", is("200")))
                .andExpect(jsonPath("$.data.date", is("01.12.2023")))
                .andExpect(jsonPath("$.data.time", is("14:30:00")))
                .andExpect(jsonPath("$.data.result_name", is("Test Company SARL")))
                .andExpect(jsonPath("$.data.result_street", is("Test Rue 123")))
                .andExpect(jsonPath("$.data.result_postal_code", is("75001")))
                .andExpect(jsonPath("$.data.result_city", is("Paris")));

    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void validateVat_ShouldHandleEvatrErrorResponse() throws Exception {
        // Setup WireMock to mock EVATR API response with error code
        String evatrErrorXmlResponse = """
                <?xml version="1.0" encoding="UTF-8"?>
                <methodResponse>
                    <params>
                        <param>
                            <value>
                                <array>
                                    <data>
                                        <value><string>UstId_1</string></value>
                                        <value><string>DE257906838</string></value>
                                    </data>
                                </array>
                            </value>
                        </param>
                        <param>
                            <value>
                                <array>
                                    <data>
                                        <value><string>UstId_2</string></value>
                                        <value><string>FR999999999</string></value>
                                    </data>
                                </array>
                            </value>
                        </param>
                        <param>
                            <value>
                                <array>
                                    <data>
                                        <value><string>ErrorCode</string></value>
                                        <value><string>201</string></value>
                                    </data>
                                </array>
                            </value>
                        </param>
                        <param>
                            <value>
                                <array>
                                    <data>
                                        <value><string>Datum</string></value>
                                        <value><string>01.12.2023</string></value>
                                    </data>
                                </array>
                            </value>
                        </param>
                    </params>
                </methodResponse>
                """;

        WireMock.stubFor(WireMock.get(WireMock.urlMatching("/evatr.*"))
                                 .willReturn(WireMock.aResponse()
                                                     .withStatus(200)
                                                     .withHeader("Content-Type", "application/xml")
                                                     .withBody(evatrErrorXmlResponse)));

        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("FR999999999");
        vatIdDto.setCountryCode("FR");
        vatIdDto.setCompanyName("Invalid Company");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.system", is("EVATR")))
                .andExpect(jsonPath("$.is_valid", is(false)))
                .andExpect(jsonPath("$.data.error_code", is("201")))
                .andExpect(jsonPath("$.error", is("INVALID")));

        // Verify that the EVATR API was called
        WireMock.verify(WireMock.getRequestedFor(WireMock.urlMatching("/evatr.*")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void validateVat_ShouldHandleViesInvalidResponse() throws Exception {
        // Setup WireMock to mock VIES API response with invalid VAT
        String viesInvalidResponseJson = """
                {
                    "valid": false,
                    "countryCode": "DE",
                    "vatNumber": "999999999",
                    "requestDate": "2023-12-01"
                }
                """;

        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/vies"))
                                 .willReturn(WireMock.aResponse()
                                                     .withStatus(200)
                                                     .withHeader("Content-Type", "application/json")
                                                     .withBody(viesInvalidResponseJson)));

        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("DE999999999");
        vatIdDto.setCountryCode("DE");
        vatIdDto.setCompanyName("Invalid Company");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.system", is("VIES")))
                .andExpect(jsonPath("$.is_valid", is(false)))
                .andExpect(jsonPath("$.data.valid", is(false)))
                .andExpect(jsonPath("$.error", is("INVALID")));

        // Verify that the VIES API was called
        WireMock.verify(WireMock.postRequestedFor(WireMock.urlEqualTo("/vies")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {TestRole.ADMIN}
    )
    void validateVat_ShouldHandleNetworkError() throws Exception {
        // Setup WireMock to simulate network error
        WireMock.stubFor(WireMock.post(WireMock.urlEqualTo("/vies"))
                                 .willReturn(WireMock.aResponse().withStatus(500).withBody("Internal Server Error")));

        VatIdDto vatIdDto = new VatIdDto();
        vatIdDto.setVatId("DE*********");
        vatIdDto.setCountryCode("DE");

        mockMvc.perform(post(API_BASE_URL + "/validate-vat").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(vatIdDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.system", is("VIES")))
                .andExpect(jsonPath("$.is_valid", is(false)))
                .andExpect(jsonPath("$.error", containsString("Internal Server Error")));
    }

    /**
     * Helper method to create a valid CreateCompanyDto for testing.
     */
    private CreateCompanyDto createValidCreateCompanyDto() {
        CreateCompanyDto dto = new CreateCompanyDto();
        dto.setName("Test Company");
        dto.setDescription("Test Description");

        // Address
        CreateCompanyAddressDto address = new CreateCompanyAddressDto();
        address.setCountryCode("DE");
        address.setAddressLine("Test Address");
        address.setCity("Berlin");
        address.setZipCode("12345");
        address.setStreetAndNumber("Test Street 123");
        address.setAdditionalAddress("Additional");
        dto.setAddress(address);

        // Contact
        CreateCompanyContactDto contact = new CreateCompanyContactDto();
        contact.setName("Test Contact");
        contact.setEmail("<EMAIL>");
        contact.setPhoneMobile("+49*********");
        dto.setContact(contact);

        // Emails
        dto.setEmails(Arrays.asList("<EMAIL>", "<EMAIL>"));

        return dto;
    }

    /**
     * Helper method to create a complete CreateCompanyDto with all fields for comprehensive testing.
     */
    private CreateCompanyDto createCompleteCreateCompanyDto() {
        CreateCompanyDto dto = new CreateCompanyDto();
        dto.setName("Complete Test Company");
        dto.setDescription("Complete company description");
        dto.setManagingDirector("Complete Director");
        dto.setWebsite("https://complete-test.com");

        // Complete Address
        CreateCompanyAddressDto address = new CreateCompanyAddressDto();
        address.setCountryCode("DE");
        address.setAddressLine("Complete Address Line");
        address.setCity("Munich");
        address.setZipCode("80331");
        address.setStreetAndNumber("Complete Street 456");
        address.setAdditionalAddress("Complete Additional");
        dto.setAddress(address);

        // Complete Contact
        CreateCompanyContactDto contact = new CreateCompanyContactDto();
        contact.setName("Complete Contact");
        contact.setEmail("<EMAIL>");
        contact.setPhoneMobile("+49555666777");
        dto.setContact(contact);

        // Complete Emails
        dto.setEmails(Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>"));

        // Complete Billing
        CreateCompanyBillingDto billing = new CreateCompanyBillingDto();
        billing.setFullName("Complete Billing Name");
        billing.setCountryCode("DE");
        billing.setCountryName("Germany");
        billing.setCompanyName("Complete Billing Company");
        billing.setStreetAndNumber("Complete Billing Street 789");
        billing.setCity("Munich");
        billing.setZipCode("80331");
        dto.setBilling(billing);

        return dto;
    }

    /**
     * Helper method to create a complete UpdateCompanyDto with all fields for comprehensive testing.
     */
    private UpdateCompanyDto createCompleteUpdateCompanyDto() {
        UpdateCompanyDto dto = new UpdateCompanyDto();
        dto.setName("Updated Complete Company");
        dto.setDescription("Updated complete description");
        dto.setManagingDirector("Updated Director");
        dto.setWebsite("https://updated-complete.com");

        // Updated Address
        CreateCompanyAddressDto address = new CreateCompanyAddressDto();
        address.setCountryCode("DE");
        address.setAddressLine("Updated Address Line");
        address.setCity("Hamburg");
        address.setZipCode("20095");
        address.setStreetAndNumber("Updated Street 789");
        address.setAdditionalAddress("Updated Additional");
        dto.setAddress(address);

        // Updated Contact
        CreateCompanyContactDto contact = new CreateCompanyContactDto();
        contact.setName("Updated Contact");
        contact.setEmail("<EMAIL>");
        contact.setPhoneMobile("+49888999000");
        dto.setContact(contact);

        // Updated Emails
        dto.setEmails(Arrays.asList("<EMAIL>", "<EMAIL>"));

        // Updated Billing
        CreateCompanyBillingDto billing = new CreateCompanyBillingDto();
        billing.setFullName("Updated Billing Name");
        billing.setCountryCode("DE");
        billing.setCountryName("Germany");
        billing.setCompanyName("Updated Billing Company");
        billing.setStreetAndNumber("Updated Billing Street 123");
        billing.setCity("Hamburg");
        billing.setZipCode("20095");
        dto.setBilling(billing);

        return dto;
    }
}
