"use client";

import { ServicesRevenueOvertime } from "@/types/dashboard/services-revenue-overtime";
import { GraphColors } from "@/utils/graph-colors";
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts";

const MONTHS = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "ago", "sept", "oct", "nov", "dez"];

export function ServicesRevenueChart({
  graphColors,
  data,
}: {
  graphColors: GraphColors;
  data: ServicesRevenueOvertime;
}) {
  const chartData = MONTHS.map((month, monthIndex) => {
    const monthData: { [key: string]: string | number } = {
      month,
    };
    data.data.forEach((service) => {
      const monthPerformance = service.month_perfomances.find((perf) => perf.month === monthIndex + 1);
      monthData[service.setup_packaging_service_id.toString()] = (monthPerformance?.result || 0) / 100;
    });
    return monthData;
  });
  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart accessibilityLayer data={chartData} margin={{ left: 10, right: 15 }}>
        <CartesianGrid vertical={false} />
        <Tooltip />
        <YAxis tickLine={false} axisLine={false} />
        <XAxis dataKey="month" tickLine={false} axisLine={false} interval={0} />
        {data.data.map((service) => (
          <Area
            key={service.setup_packaging_service_id}
            type="monotone"
            dataKey={service.setup_packaging_service_id.toString()}
            name={service.label}
            fill={graphColors.getColor(service.setup_packaging_service_id)}
            fillOpacity={0.4}
            stroke={graphColors.getColor(service.setup_packaging_service_id)}
          />
        ))}
      </AreaChart>
    </ResponsiveContainer>
  );
}
