import { Button } from "@interzero/oneepr-react-ui/Button";
import Link from "next/link";

export function SuccessResetPassword() {
  return (
    <div className="text-left text-primary mb-10 flex flex-col size-full justify-center">
      <h1 className=" text-4xl font-medium">Thank you!</h1>
      <p className="font-light text-sm">
        We’ve sent password reset instructions to <strong className="font-bold">your email address</strong>. If no email
        is received within ten minutes, check if the submitted address is correct.
      </p>
      <div className="flex mt-10">
        <Link href="/en/auth/login" className="w-full">
          <Button size="medium" variant="filled" color="dark-blue" className="w-full">
            Back to login
          </Button>
        </Link>
      </div>
    </div>
  );
}
