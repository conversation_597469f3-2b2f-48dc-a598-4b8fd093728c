interface AuthLayoutProps {
  children: React.ReactNode;
  imageUrl?: string;
}

export function AuthLayout({ children, imageUrl = "/assets/images/login-person.png" }: AuthLayoutProps) {
  return (
    <div className="w-full px-4 py-14">
      <div className="w-full lg:max-w-screen-lg mx-auto">
        <div className="text-gray-500 rounded-3xl shadow-xl w-full overflow-hidden" style={{ maxWidth: "1000px" }}>
          <div className="md:flex w-full">
            <div
              className="hidden md:flex md:-mr-2 w-1/2 bg-tonal-dark-blue-90 z-10 rounded-3xl "
              style={{
                backgroundImage: `url(${imageUrl})`,
                backgroundPosition: "center",
                backgroundSize: "cover",
                backgroundRepeat: "no-repeat",
              }}
            ></div>
            <div className="w-full md:-ml-2 md:w-1/2 py-10 px-5 md:px-20 md:bg-tonal-dark-blue-96 rounded-r-3xl md:m-0 mt-10 md:min-h-144">
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
