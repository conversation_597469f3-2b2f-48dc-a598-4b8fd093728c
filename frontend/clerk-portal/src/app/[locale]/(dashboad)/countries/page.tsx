import type { Metadata } from "next";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { TitleAndSubTitle } from "@/components/_common/title-and-subtitle";
import { CountriesFilterTabs } from "@/components/modules/country/components/countries-filter-tabs";
import { SortCountries } from "@/components/modules/country/components/sort-countries";
import { CountriesGrid } from "@/components/modules/country/components/countries-grid";
import { SearchInput } from "@/components/_common/search-input";
import { SelectRepresentativeModal } from "@/components/modules/country/components/select-representative-modal";
import { useTranslations } from "next-intl";

const breadcrumbPaths = [
  { label: "Dashboard", href: "/" },
  { label: "Countries", href: "" },
];

export const metadata: Metadata = {
  title: "Countries",
};

export default function CountriesPage() {
  const t = useTranslations("CustomerProfileHeader");
  return (
    <>
      <Container>
        <Breadcrumb paths={breadcrumbPaths} />
        <TitleAndSubTitle icon={false} title={t("countries")} subText="Overview of all countries" />
        <CountriesFilterTabs />
        <section className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mt-6">
          <div className="w-full md:max-w-[392px]">
            <SearchInput queryName="search" />
          </div>
          <SortCountries />
        </section>
        <section className="mt-6 pb-6">
          <CountriesGrid />
        </section>
      </Container>

      <SelectRepresentativeModal />
    </>
  );
}
