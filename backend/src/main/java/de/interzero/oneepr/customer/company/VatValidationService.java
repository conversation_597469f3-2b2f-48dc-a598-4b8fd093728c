package de.interzero.oneepr.customer.company;

import de.interzero.oneepr.customer.company.dto.EvatrResponse;
import de.interzero.oneepr.customer.company.dto.VatIdDto;
import de.interzero.oneepr.customer.company.dto.VatValidationResult;
import de.interzero.oneepr.customer.company.dto.ViesResponse;
import de.interzero.oneepr.customer.company.util.XmlParserUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for VAT ID validation using external services.
 * Converted from TypeScript VAT validation utilities.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VatValidationService {

    private static final String INVALID = "INVALID";

    private static final String EVATR = "EVATR";

    private final RestTemplate restTemplate;

    @Value("${app.lizenzero.vat.id:DE257906838}")
    private String lizenzeroVatId;

    @Value("${app.vies.api.url:https://ec.europa.eu/taxation_customs/vies/rest-api/check-vat-number}")
    private String viesApiUrl;

    @Value("${app.evatr.api.url:https://evatr.bff-online.de/evatrRPC}")
    private String evatrApiUrl;

    /**
     * Validates German VAT ID using VIES (VAT Information Exchange System).
     * Equivalent to TypeScript: validateGermanVat(data)
     *
     * @param data VatIdDto with VAT information to validate
     * @return VatValidationResult with VIES validation results
     */
    public VatValidationResult validateGermanVat(VatIdDto data) {
        try {
            if (data.getVatId() == null || data.getCountryCode() == null) {
                return VatValidationResult.builder()
                        .system("VIES")
                        .isValid(false)
                        .data(new ViesResponse())
                        .error(INVALID)
                        .build();
            }

            // Prepare request body - exact same as TypeScript
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("countryCode", data.getCountryCode());
            requestBody.put("vatNumber", data.getVatId().replaceFirst("^DE", ""));
            requestBody.put("requesterMemberStateCode", "DE");
            requestBody.put("requesterNumber", lizenzeroVatId.replaceFirst("^DE", ""));

            // Set headers - exact same as TypeScript
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);

            // Make HTTP call - equivalent to TypeScript fetch
            ResponseEntity<ViesResponse> response = restTemplate.postForEntity(viesApiUrl, request, ViesResponse.class);
            log.info("Vies VALIDATION RESPONSE: {}", response);

            if (!response.getStatusCode().is2xxSuccessful()) {
                return VatValidationResult.builder()
                        .system("VIES")
                        .isValid(false)
                        .data(new ViesResponse())
                        .error("NETWORK_ERROR")
                        .build();
            }

            ViesResponse validationData = response.getBody();

            if (validationData == null || validationData.getValid() == null) {
                return VatValidationResult.builder()
                        .system("VIES")
                        .isValid(false)
                        .data(new ViesResponse())
                        .error(INVALID)
                        .build();
            }

            if (!validationData.getValid()) {
                return VatValidationResult.builder()
                        .system("VIES")
                        .isValid(false)
                        .data(validationData)
                        .error(INVALID)
                        .build();
            }

            return VatValidationResult.builder().system("VIES").isValid(true).data(validationData).build();

        } catch (HttpClientErrorException e) {
            log.error("HTTP error during VIES validation: {}", e.getMessage());
            return VatValidationResult.builder()
                    .system("VIES")
                    .isValid(false)
                    .data(new ViesResponse())
                    .error("NETWORK_ERROR")
                    .build();
        } catch (Exception e) {
            log.error("Error during VIES validation: {}", e.getMessage());
            return VatValidationResult.builder()
                    .system("VIES")
                    .isValid(false)
                    .data(new ViesResponse())
                    .error(e.getMessage() != null ? e.getMessage() : "UNKNOWN_ERROR")
                    .build();
        }
    }

    /**
     * Validates foreign VAT ID using EVATR (German Federal Central Tax Office).
     * Equivalent to TypeScript: validateForeignVat(vatIdDto)
     *
     * @param vatIdDto VatIdDto with VAT information to validate
     * @return VatValidationResult with EVATR validation results
     */
    public VatValidationResult validateForeignVat(VatIdDto vatIdDto) {
        try {
            if (vatIdDto.getVatId() == null) {
                return VatValidationResult.builder()
                        .system(EVATR)
                        .isValid(false)
                        .data(new EvatrResponse())
                        .error("Invalid VAT ID")
                        .build();
            }

            // Build query parameters - exact same as TypeScript
            String url = UriComponentsBuilder.fromHttpUrl(evatrApiUrl)
                    .queryParam("UstId_1", lizenzeroVatId)
                    .queryParam("UstId_2", vatIdDto.getVatId())
                    .queryParam("Firmenname", vatIdDto.getCompanyName() != null ? vatIdDto.getCompanyName() : "")
                    .queryParam("Ort", vatIdDto.getCompanyCity() != null ? vatIdDto.getCompanyCity() : "")
                    .queryParam("PLZ", vatIdDto.getCompanyZipcode() != null ? vatIdDto.getCompanyZipcode() : "")
                    .queryParam("Strasse", vatIdDto.getCompanyStreet() != null ? vatIdDto.getCompanyStreet() : "")
                    .toUriString();

            // Make HTTP call - equivalent to TypeScript fetch
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            log.info("VAT VALIDATION RESPONSE: {}", response);

            if (!response.getStatusCode().is2xxSuccessful()) {
                return VatValidationResult.builder()
                        .system(EVATR)
                        .isValid(false)
                        .data(new EvatrResponse())
                        .error("EVATR request failed with status " + response.getStatusCode().value())
                        .build();
            }

            String xml = response.getBody();
            EvatrResponse formattedData = parseEvatrResponse(xml);

            if (formattedData == null) {
                return VatValidationResult.builder()
                        .system(EVATR)
                        .isValid(false)
                        .data(new EvatrResponse())
                        .error("Invalid response format from EVATR")
                        .build();
            }

            String resultCode = formattedData.getErrorCode();

            if (!"200".equals(resultCode)) {
                String errorType = getEvatrErrorType(resultCode);
                return VatValidationResult.builder()
                        .system(EVATR)
                        .isValid(false)
                        .data(formattedData)
                        .error(errorType != null ? errorType : "Unknown error occurred during EVATR validation")
                        .build();
            }

            return VatValidationResult.builder().system(EVATR).isValid(true).data(formattedData).build();

        } catch (Exception e) {
            log.error("Error during EVATR validation: {}", e.getMessage());
            return VatValidationResult.builder()
                    .system(EVATR)
                    .isValid(false)
                    .data(new EvatrResponse())
                    .error(e.getMessage() != null ? e.getMessage() : "Unknown error occurred during EVATR validation")
                    .build();
        }
    }

    /**
     * Parse EVATR XML response to EvatrResponse object.
     * Uses XmlParserUtil for proper XML parsing.
     *
     * @param xml XML response from EVATR
     * @return EvatrResponse object
     */
    private EvatrResponse parseEvatrResponse(String xml) {
        return XmlParserUtil.parseEvatrResponse(xml);
    }

    /**
     * Get error type for EVATR error code.
     * Equivalent to TypeScript EVATR_ERRORS mapping.
     *
     * @param errorCode Error code from EVATR
     * @return Error type string
     */
    private String getEvatrErrorType(String errorCode) {
        Map<String, String> errorTypes = new HashMap<>();
        errorTypes.put("200", "VALID");
        errorTypes.put("201", INVALID);
        errorTypes.put("202", "NOT_REGISTERED");
        errorTypes.put("203", "VALID_FROM_FUTURE");
        errorTypes.put("204", "VALID_PAST_ONLY");
        errorTypes.put("205", "TEMPORARY_UNAVAILABLE");
        errorTypes.put("206", "OWN_VAT_INVALID");
        errorTypes.put("208", "PROCESSING_BY_OTHERS");
        errorTypes.put("209", "FORMAT_INVALID_FOR_COUNTRY");
        errorTypes.put("210", "CHECKSUM_INVALID");
        errorTypes.put("211", "ILLEGAL_CHARACTERS");
        errorTypes.put("212", "INVALID_COUNTRY_PREFIX");
        errorTypes.put("213", "NOT_AUTHORIZED_FOR_GERMAN");
        errorTypes.put("214", "OWN_VAT_FORMAT_INVALID");
        errorTypes.put("215", "SIMPLE_MISSING_PARAMS");
        errorTypes.put("216", "QUALIFIED_MISSING_PARAMS");

        return errorTypes.get(errorCode);
    }
}
