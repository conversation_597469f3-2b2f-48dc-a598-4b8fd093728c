"use client";

import { Divider } from "@/components/common/divider";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Elipse, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import StackedBarChart from "./bar-graph";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function PartnersDealsUsed() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col">
      <h3 className="text-primary text-2xl font-bold">Partners deals used</h3>
      <p className="text-[#808FA9] mb-2">Offers from the partners</p>
      <div className="flex flex-row gap-4 items-center mt-2">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {LICENSE_YEAR_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeLicenseYear(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
        <Divider vertical initialMarginDisabled className="h-[20px]" />
        <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
      </div>

      <div className="flex items-center gap-3 mt-4">
        <div className="flex items-center gap-2">
          <Elipse className="fill-[#1B6C64] size-4" />
          <p className="text-[#000000] text-small-paragraph-regular">Revenue</p>
        </div>
        <div className="flex items-center gap-2">
          <Elipse className="fill-[#D8F2D8] size-4" />
          <p className="text-[#000000] text-small-paragraph-regular">Earnings</p>
        </div>
      </div>
      <div className="w-full h-[280px] mt-4">
        <StackedBarChart />
      </div>
    </div>
  );
}
