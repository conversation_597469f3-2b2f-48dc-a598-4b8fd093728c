"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DragFile } from "@/components/ui/drag-file";
import { Textarea } from "@/components/ui/textarea";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createRequiredInformation } from "@/lib/api/required-information";
import { uploadFile } from "@/lib/api/upload-files";
import { queryClient } from "@/lib/react-query";
import { UploadedFile } from "@/types/file";
import { CreateRequiredInformation } from "@/types/service-setup/required-information";
import { Role } from "@/utils/user";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import React, { ReactNode, useMemo, useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import MultipleSelector from "@/components/ui/multiple-selector";
import { REQUIRED_INFORMATION_TYPES } from "@/components/modules/countries/service-setup/required-information";
import { CharacterCounter } from "@/components/ui/character-counter";

const addNewInformationSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    description: z.string().default(""),
    type: z.enum(["TEXT", "NUMBER", "DOCUMENT", "FILE", "IMAGE"]),
    file: z
      .any()
      .optional()
      .refine(
        (file) => {
          // Skip validation on server side
          if (typeof window === "undefined") return true;
          // On client side, check if it's a File instance when required
          return file instanceof File || file === undefined;
        },
        { message: "Invalid file type" }
      ),
    packaging_service_ids: z.array(z.string()).min(1, "Service type is required"),
  })
  .superRefine((data, ctx) => {
    if (data.type === "DOCUMENT" || data.type === "FILE" || data.type === "IMAGE") {
      if (!data.file) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "File is required",
          path: ["file"],
        });
      }
    }
  });

type AddNewInformationFormData = z.infer<typeof addNewInformationSchema>;

interface AddNewInformationDialogProps {
  children: ReactNode;
}

export function AddNewInformationDialog({ children }: AddNewInformationDialogProps) {
  const { country, packagingServices } = useServiceSetup();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    clearErrors,
    watch,
    formState: { errors },
  } = useForm<AddNewInformationFormData>({
    resolver: zodResolver(addNewInformationSchema),
  });

  const { data: session } = useSession();

  const { mutate: createInformation, isPending: isCreatingInformation } = useMutation({
    mutationFn: async (
      data: Omit<CreateRequiredInformation, "file_id"> & { userId: number; userRole: Role; file?: File }
    ) => {
      let file: UploadedFile | null = null;

      if (data.file) {
        file = await uploadFile({
          user_id: data.userId,
          user_role: data.userRole,
          file: data.file!,
          document_type: "COUNTRY_DOCUMENT",
          country_id: String(country.id),
        });
      }

      return createRequiredInformation({ ...data, file_id: file?.id || null });
    },
  });

  // TODO: if don't need, should be removed
  // useEffect(() => {
  //   reset();
  //
  //   // TODO: check if it is possible to remove this eslint-disable comment
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [isDialogOpen]);

  async function handleFormSubmit(data: AddNewInformationFormData) {
    if (!session) return;

    const userId = session.user.id;
    const userRole = session.user.role;

    const serviceTypeIds = data.packaging_service_ids ? data.packaging_service_ids.map((id) => parseInt(id)) : [];

    createInformation(
      {
        country_id: country.id,
        type: data.type,
        name: data.name,
        description: data.description,
        packaging_service_ids: serviceTypeIds,
        file: data.file,
        userId,
        userRole,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["service-setup-required-informations", country.code] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

          enqueueSnackbar("Required information created successfully", { variant: "success" });
          setIsDialogOpen(false);
          reset();
        },
        onError: () => {
          enqueueSnackbar("Failed to create required information", { variant: "error" });
        },
      }
    );
  }

  function handleAddFile(file: File) {
    setValue("file", file);
    clearErrors("file");
  }

  const requiredInformationType = useWatch({ control, name: "type" });
  const nameValue = watch("name") || "";
  const descriptionValue = watch("description") || "";

  const selectedRequiredInformationType = useMemo(() => {
    return REQUIRED_INFORMATION_TYPES[requiredInformationType as keyof typeof REQUIRED_INFORMATION_TYPES] || null;
  }, [requiredInformationType]);

  const truncateText = (text: string, maxLength: number = 44) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + "...";
  };

  const serviceTypeOptions = useMemo(() => {
    if (!packagingServices || packagingServices.length === 0) {
      return [];
    }

    return packagingServices.map((service) => ({
      value: String(service.id),
      label: truncateText(`EPR Compliance Packaging - ${service.name}`, 44),
      fullName: `EPR Compliance Packaging - ${service.name}`,
    }));
  }, [packagingServices]);

  function handleDialogOpenChange(open: boolean) {
    if (!open) reset();
    setIsDialogOpen(open);
  }

  return (
    <Dialog open={isDialogOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-[#F0F0EF] max-w-2xl">
        <DialogHeader>
          <DialogTitle>New information</DialogTitle>
          <DialogDescription>
            {requiredInformationType ? "Request a new information to the license process." : "Select type"}
          </DialogDescription>
        </DialogHeader>
        <div id="add-new-information-form" className="w-full space-y-10">
          <div className="w-full">
            {!requiredInformationType && (
              <div className="bg-background rounded-2xl overflow-hidden">
                {Object.entries(REQUIRED_INFORMATION_TYPES).map(([key, type]) => (
                  <label
                    htmlFor={key}
                    key={key}
                    className="flex items-center gap-2 p-4 hover:bg-surface-02 cursor-pointer"
                  >
                    <input type="radio" id={key} className="hidden" {...register("type")} value={key} />
                    <div className="h-12 w-12 flex-none flex items-center justify-center">
                      <type.icon />
                    </div>
                    <div className="space-y-1 flex-1">
                      <p className="text-tonal-dark-cream-20 font-bold">{type.name}</p>
                      <span className="text-tonal-dark-cream-50 text-sm">{type.description}</span>
                    </div>
                    <ChevronRight className="stroke-support-blue" />
                  </label>
                ))}
              </div>
            )}
            {!!selectedRequiredInformationType && (
              <div className="w-full space-y-10">
                <div className="space-y-4">
                  <Button
                    variant="text"
                    color="light-blue"
                    size="medium"
                    leadingIcon={<ChevronLeft className="size-5" />}
                    onClick={() => reset()}
                  >
                    Back
                  </Button>
                  <div className="flex items-center gap-2">
                    <div className="h-12 w-12 flex-none flex items-center justify-center">
                      <selectedRequiredInformationType.icon />
                    </div>
                    <div className="space-y-1 flex-1">
                      <p className="text-tonal-dark-cream-20 font-bold">{selectedRequiredInformationType.name}</p>
                      <span className="text-tonal-dark-cream-50 text-sm">
                        {selectedRequiredInformationType.description}
                      </span>
                    </div>
                  </div>
                  <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
                </div>
                <div className="space-y-10">
                  <div className="mb-4">
                    <label className="block text-base  mb-2">{"Service type *"}</label>
                    {serviceTypeOptions.length === 0 ? (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm text-yellow-800">
                          No service types available. Please add service types in the Service details section first.
                        </p>
                      </div>
                    ) : (
                      <>
                        <Controller
                          control={control}
                          name="packaging_service_ids"
                          render={({ field }) => (
                            <MultipleSelector
                              badgeClassName="bg-[#FFF3E1] border-0 font-centra text-base leading-5 font-normal"
                              value={serviceTypeOptions.filter((opt) => field.value?.includes(opt.value))}
                              options={serviceTypeOptions}
                              onChange={(selected) => field.onChange(selected.map((opt) => opt.value))}
                              placeholder="Select"
                              hidePlaceholderWhenSelected={true}
                              disabled={isCreatingInformation}
                              emptyIndicator={<span>No service types available</span>}
                              invalid={!!errors.packaging_service_ids}
                              disableInput={true}
                              hideSearchIcon={true}
                              dropdownIcon={
                                <ChevronDown className="h-5 w-5 flex-shrink-0" style={{ color: "#002652" }} />
                              }
                            />
                          )}
                        />
                        {errors.packaging_service_ids && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">Service type is required</p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                  {requiredInformationType === "DOCUMENT" && (
                    <>
                      <div>
                        <Input
                          label="Document tittle *"
                          placeholder="Enter document title"
                          {...register("name")}
                          variant={errors.name ? "error" : "default"}
                          enabled={!isCreatingInformation}
                          maxLength={150}
                        />
                        {errors.name && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">Document tittle is required</p>
                          </div>
                        )}
                        <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
                      </div>
                      <DragFile
                        title="Upload a template"
                        onFile={handleAddFile}
                        errorMessage={
                          typeof errors.file?.message === "string"
                            ? errors.file.message
                            : errors.file?.message?.toString()
                        }
                        accept="application/pdf"
                        disabled={isCreatingInformation}
                      />
                      <div>
                        <Textarea
                          id="description"
                          label="Help text"
                          placeholder="Enter help text"
                          rows={6}
                          className="resize-none"
                          maxLength={500}
                          {...register("description")}
                          disabled={isCreatingInformation}
                        />
                        <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={500} />
                      </div>
                    </>
                  )}
                  {requiredInformationType === "FILE" && (
                    <>
                      <div>
                        <Input
                          label="File tittle *"
                          placeholder="Enter file title"
                          {...register("name")}
                          variant={errors.name ? "error" : "default"}
                          enabled={!isCreatingInformation}
                          maxLength={150}
                        />
                        {errors.name && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">File tittle is required</p>
                          </div>
                        )}
                        <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
                      </div>
                      <DragFile
                        title="Upload a template"
                        onFile={handleAddFile}
                        errorMessage={
                          typeof errors.file?.message === "string"
                            ? errors.file.message
                            : errors.file?.message?.toString()
                        }
                        accept="application/pdf"
                        disabled={isCreatingInformation}
                      />
                      <div>
                        <Textarea
                          id="description"
                          label="Help text"
                          placeholder="Enter help text"
                          rows={6}
                          className="resize-none"
                          maxLength={500}
                          {...register("description")}
                          disabled={isCreatingInformation}
                        />
                        <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={500} />
                      </div>
                    </>
                  )}
                  {requiredInformationType === "IMAGE" && (
                    <>
                      <div>
                        <Input
                          label="Image title *"
                          placeholder="Enter image title"
                          {...register("name")}
                          variant={errors.name ? "error" : "default"}
                          enabled={!isCreatingInformation}
                          maxLength={150}
                        />
                        {errors.name && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">Image tittle is required</p>
                          </div>
                        )}
                        <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
                      </div>
                      <div>
                        <Textarea
                          id="description"
                          label="Help text"
                          placeholder="Enter help text"
                          rows={6}
                          className="resize-none"
                          maxLength={500}
                          {...register("description")}
                          disabled={isCreatingInformation}
                        />
                        <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={500} />
                      </div>
                      <DragFile
                        title="Upload an example"
                        description="if necessary"
                        onFile={handleAddFile}
                        errorMessage={
                          typeof errors.file?.message === "string"
                            ? errors.file.message
                            : errors.file?.message?.toString()
                        }
                        accept="image/png,image/jpeg,image/jpg"
                        disabled={isCreatingInformation}
                      />
                    </>
                  )}
                  {requiredInformationType === "TEXT" && (
                    <>
                      <div>
                        <Input
                          label="Question to be answered *"
                          placeholder="Enter question to be answered"
                          {...register("name")}
                          variant={errors.name ? "error" : "default"}
                          enabled={!isCreatingInformation}
                          maxLength={150}
                        />
                        {errors.name && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">Question is required</p>
                          </div>
                        )}
                        <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
                      </div>
                      <div>
                        <Textarea
                          id="description"
                          label="Help text"
                          placeholder="Enter help text"
                          rows={6}
                          className="resize-none"
                          maxLength={500}
                          {...register("description")}
                          disabled={isCreatingInformation}
                        />
                        <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={500} />
                      </div>
                    </>
                  )}
                  {requiredInformationType === "NUMBER" && (
                    <>
                      <div>
                        <Input
                          label="Question to be answered *"
                          placeholder="Enter question to be answered"
                          {...register("name")}
                          variant={errors.name ? "error" : "default"}
                          enabled={!isCreatingInformation}
                          maxLength={150}
                        />
                        {errors.name && (
                          <div className="flex items-center gap-2 mt-2">
                            <Error className="size-5 fill-error flex-shrink-0" />
                            <p className="text-sm text-error">Question is required</p>
                          </div>
                        )}
                        <CharacterCounter className="mt-2 justify-start" value={nameValue.length} max={150} />
                      </div>
                      <div>
                        <Textarea
                          id="description"
                          label="Help text"
                          placeholder="Enter help text"
                          rows={6}
                          className="resize-none"
                          maxLength={500}
                          {...register("description")}
                          disabled={isCreatingInformation}
                        />
                        <CharacterCounter className="mt-2 justify-start" value={descriptionValue.length} max={500} />
                      </div>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
          {!!selectedRequiredInformationType && (
            <div className="flex flex-col mt-8">
              <div className="flex items-center justify-end">
                <Button
                  form="add-new-information-form"
                  type="submit"
                  variant="filled"
                  color="yellow"
                  size="medium"
                  onClick={() => handleSubmit(handleFormSubmit)()}
                  disabled={isCreatingInformation}
                >
                  {isCreatingInformation ? "Creating..." : "Save"}
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
