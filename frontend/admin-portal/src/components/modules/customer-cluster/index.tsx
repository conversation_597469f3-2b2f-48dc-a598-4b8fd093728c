"use client";

import { useRouter } from "next/navigation";

import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, Pin } from "@interzero/oneepr-react-ui/Icon";

import { CustomerClustersTable } from "./customer-clusters-table";

export function CustomerClustersModule() {
  const router = useRouter();

  return (
    <ModuleContent>
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center sm:gap-10 mb-10">
        <ModuleTitle icon={Pin} title="Customer's Clusters" description="See all the clusters for your customers." />
        <Button
          variant="filled"
          color="yellow"
          size="medium"
          leadingIcon={<Add />}
          onClick={() => router.push("/customer-clusters/create")}
        >
          Create new
        </Button>
      </div>
      <CustomerClustersTable />
    </ModuleContent>
  );
}
