package de.interzero.oneepr.admin.upload_data.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GermanyFraction {
    GLASS("A7B2X", "Glass"),
    PAPER("K9P4M", "Paper / Paperboard / Cardboard (PPK)"),
    FERROUS_METALS("W3N8L", "Ferrous Metals"),
    ALUMINIUM("R5T9V", "Aluminium incl. Composites"),
    LIQUID_COMPOSITES("H6Y4Z", "Liquid Composites"),
    PPK_COMPOSITES("Q2C7D", "Other Composites based on PPK"),
    PLASTICS("J8F3S", "Plastics incl. Composites"),
    OTHER_MATERIALS("M1G5B", "Other Materials");

    private final String code;

    private final String name;

    /**
     * Finds the GermanyFraction enum by its name.
     *
     * @param name The name of the fraction to find.
     * @return The GermanyFraction enum if found, null otherwise.
     */
    public static GermanyFraction findByName(String name) {
        return Arrays.stream(values()).filter(f -> f.getName().equals(name)).findFirst().orElse(null);
    }

}
