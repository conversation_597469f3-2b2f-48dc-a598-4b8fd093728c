interface License {
  id: number;
  contract_id: number;
  registration_number: string;
  registration_status: string;
  clerk_control_status: string;
  contract_status: string;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  start_date: Date;
  end_date: Date;
  termination_id: number | null;
  termination_date?: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  packaging_services?: any[];
  price_list?: any;
}

interface Action_guides {
  id: number;
  contract_id: number;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}
[];

export interface Contract {
  id: number;
  customer_id: number;
  type: string;
  status: string;
  title: string;
  start_date: Date;
  termination_id: number | null;
  termination_date?: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  licenses: License[];
  action_guides: Action_guides[];
}
