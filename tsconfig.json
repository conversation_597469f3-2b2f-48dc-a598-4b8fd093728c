{"compilerOptions": {"noErrorTruncation": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "test/**/*", "types/**/*", "scripts/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist"]}