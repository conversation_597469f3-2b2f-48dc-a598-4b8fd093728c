import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiQuery } from "@nestjs/swagger";
import { CreateLicenseDto } from "./dto/create-license.dto";
import { UpdateLicenseDto } from "./dto/update-license.dto";
import { LicenseService } from "./license.service";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Controller("licenses")
@ApiTags("licenses")
export class LicenseController {
  constructor(private readonly licenseService: LicenseService) {}

  @Get()
  @ApiQuery({ name: "contract_id", required: false, type: Number })
  @ApiOperation({ summary: "Get all licenses" })
  findAll(@Query("contract_id") contract_id?: string) {
    return this.licenseService.findAll(contract_id ? parseInt(contract_id) : undefined);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a license by id" })
  findOne(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseService.findOne(id, user);
  }

  @Post()
  @ApiOperation({ summary: "Create a license" })
  create(@Body() data: CreateLicenseDto) {
    return this.licenseService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a license" })
  update(@Param("id") id: number, @Body() data: UpdateLicenseDto, @User() user: AuthenticatedUser) {
    return this.licenseService.update(id, data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a license" })
  remove(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseService.remove(id, user);
  }
}
