package de.interzero.oneepr.customer.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license.License;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * Create License DTO
 * Converted from TypeScript create-license.dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Data
public class CreateLicenseDto {

    /**
     * The contract ID
     * Equivalent to TypeScript: contract_id: number;
     */
    @JsonProperty("contract_id")
    @Schema(
            description = "The contract ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer contractId;

    /**
     * The registration status of the contract
     * Equivalent to TypeScript: registration_status: LicenseRegistrationStatus;
     */
    @JsonProperty("registration_status")
    @Schema(
            description = "The registration status of the contract",
            implementation = License.RegistrationStatus.class,
            example = "DONE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private License.RegistrationStatus registrationStatus;

    /**
     * The clerk control status of the contract
     * Equivalent to TypeScript: clerk_control_status: LicenseClerkControlStatus;
     */
    @JsonProperty("clerk_control_status")
    @Schema(
            description = "The clerk control status of the contract",
            implementation = License.ClerkControlStatus.class,
            example = "DONE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private License.ClerkControlStatus clerkControlStatus;

    /**
     * The contract status of the contract
     * Equivalent to TypeScript: contract_status: LicenseContractStatus;
     */
    @JsonProperty("contract_status")
    @Schema(
            description = "The registration status of the contract",
            implementation = License.ContractStatus.class,
            example = "DONE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private License.ContractStatus contractStatus;

    /**
     * The country ID
     * Equivalent to TypeScript: country_id: number;
     */
    @JsonProperty("country_id")
    @Schema(
            description = "The country ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer countryId;

    /**
     * The country code
     * Equivalent to TypeScript: country_code: string;
     */
    @JsonProperty("country_code")
    @Schema(
            description = "The country code",
            example = "DE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryCode;

    /**
     * The country name
     * Equivalent to TypeScript: country_name: string;
     */
    @JsonProperty("country_name")
    @Schema(
            description = "The country name",
            example = "Germany",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryName;

    /**
     * The country flag
     * Equivalent to TypeScript: country_flag: string;
     */
    @JsonProperty("country_flag")
    @Schema(
            description = "The country flag",
            example = "https://flagcdn.com/de.svg",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryFlag;

    /**
     * The year of the license
     * Equivalent to TypeScript: year: number;
     */
    @JsonProperty("year")
    @Schema(
            description = "The year of the license",
            example = "2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer year;

    /**
     * The start date of the license
     * Equivalent to TypeScript: start_date: Date;
     */
    @JsonProperty("start_date")
    @Schema(
            description = "The start date of the license",
            example = "2024-01-01",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant startDate;

    /**
     * The end date of the license (optional)
     * Equivalent to TypeScript: end_date?: Date;
     */
    @JsonProperty("end_date")
    @Schema(
            description = "The end date of the license",
            example = "2024-01-01",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Instant endDate;

    /**
     * The termination ID of the license (optional)
     * Equivalent to TypeScript: termination_id?: number;
     */
    @JsonProperty("termination_id")
    @Schema(
            description = "The termination of the license",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer terminationId;
}
