import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { Transaction } from "./types";

export async function getTransactions(status?: string) {
  const query = status ? `?status=${status}` : "";
  const { data } = await api.get(`${ApiEndpoints.transaction.get}${query}`);
  return data;
}

export async function getTransactionById(transactionId: number): Promise<Transaction> {
  const { data } = await api.get(ApiEndpoints.transaction.getById(transactionId));
  return data;
}

export async function getTransactionTotals() {
  const { data } = await api.get(ApiEndpoints.transaction.totals);
  return data;
}

export async function getPotentialMatches(transactionId: number) {
  const { data } = await api.get(ApiEndpoints.transaction.getPotentialMatches(transactionId));
  return data;
}

export async function assignTransactionToInvoices({
  transactionId,
  invoiceIds,
  amount,
}: {
  transactionId: string;
  invoiceIds: string[];
  amount: number;
}) {
  try {
    const response = await api.post(ApiEndpoints.transaction.assign(transactionId), {
      invoiceIds,
      amount,
    });
    if (response.status !== 200 && response.status !== 201) throw response;
    return { success: true, data: response.data };
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message;
    return { success: false, errorMessage };
  }
}

export async function refundTransaction({ transactionId, amount }: { transactionId: string; amount: number }) {
  try {
    const response = await api.post(ApiEndpoints.transaction.refund(transactionId), {
      amount,
    });

    if (response.status !== 200 && response.status !== 201) throw response;
    return { success: true, data: response.data };
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message;
    return { success: false, errorMessage };
  }
}
