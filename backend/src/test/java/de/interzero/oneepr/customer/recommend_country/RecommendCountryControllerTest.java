package de.interzero.oneepr.customer.recommend_country;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.recommend_country.dto.CreateRecommendCountryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link RecommendCountryController}.
 * This class uses {@link SpringBootTest} to load the full application context
 * and {@link AutoConfigureMockMvc} to configure {@link MockMvc} for sending HTTP requests
 * to the controller endpoints. It verifies the behavior of the recommended country API.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class RecommendCountryControllerTest {

    private static final String API_RECOMMEND_COUNTRY_BASE_URL = Api.RECOMMEND_COUNTRY;

    // Define the external API URL as a constant if used in multiple mocks
    private static final String COUNTRIES_NOW_API_URL = "https://countriesnow.space/api/v0.1/countries/positions";


    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private RecommendCountryRepository recommendCountryRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private RestTemplate restTemplate;

    private Customer testCustomer1;

    private Customer testCustomer2;


    /**
     * Sets up the test environment before each test method execution.
     * Clears relevant repositories and initializes common test data.
     */
    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        recommendCountryRepository.deleteAllInBatch();
        customerRepository.deleteAllInBatch();

        testCustomer1 = createAndSaveTestCustomer("<EMAIL>", "John", "Doe", "Mr.", 101);
        testCustomer2 = createAndSaveTestCustomer("<EMAIL>", "Jane", "Roe", "Ms.", 102);
    }

    /**
     * Helper method to create and persist a {@link Customer} entity.
     */
    private Customer createAndSaveTestCustomer(String email,
                                               String firstName,
                                               String lastName,
                                               String salutation,
                                               Integer userId) {
        Customer customer = new Customer();
        customer.setEmail(email);
        customer.setFirstName(firstName);
        customer.setLastName(lastName);
        customer.setSalutation(salutation);
        customer.setUserId(userId);
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer.setType(Customer.Type.REGULAR);
        return customerRepository.saveAndFlush(customer);
    }


    /**
     * Helper method to create and persist a {@link RecommendedCountry} entity.
     */
    private RecommendedCountry createAndSaveTestRecommendCountry(String name,
                                                                 Customer customer) {
        RecommendedCountry rc = new RecommendedCountry();
        rc.setName(name);
        rc.setCustomer(customer);
        rc.setCreatedAt(Instant.now());
        rc.setUpdatedAt(Instant.now());
        return recommendCountryRepository.saveAndFlush(rc);
    }

    /**
     * Test for {@link RecommendCountryController#create(CreateRecommendCountryDto)}.
     * Verifies that a new recommended country can be created successfully.
     * Mocks the external country validation API call.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateNewRecommendedCountry() throws Exception {
        CreateRecommendCountryDto createDto = new CreateRecommendCountryDto();
        createDto.setRecommendedCountryName("Wonderland");
        createDto.setCustomerId(testCustomer1.getId());

        // Mock the external RestTemplate call for country validation
        when(restTemplate.postForEntity(
                eq(COUNTRIES_NOW_API_URL),
                any(RecommendCountryService.CountryValidationRequest.class),
                eq(Object.class))).thenReturn(ResponseEntity.ok().build());

        ResultActions resultActions = mockMvc.perform(post(API_RECOMMEND_COUNTRY_BASE_URL).with(csrf())
                                                              .contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(createDto))
                                                              .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.name").value("Wonderland"))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.updated_at").exists());

        String responseString = resultActions.andReturn().getResponse().getContentAsString();
        RecommendedCountry createdRC = objectMapper.readValue(responseString, RecommendedCountry.class);
        assertNotNull(createdRC.getId(), "ID should be present in the response object");


        // Verify the entity in the database
        Optional<RecommendedCountry> foundInDbOptional = recommendCountryRepository.findById(createdRC.getId());
        assertTrue(foundInDbOptional.isPresent(), "Created entity should be in DB");
        RecommendedCountry foundInDb = foundInDbOptional.get();
        assertEquals("Wonderland", foundInDb.getName());
        assertNotNull(foundInDb.getCustomer(), "Customer association should exist in DB");
        assertEquals(testCustomer1.getId(), foundInDb.getCustomer().getId(), "Customer ID should match in DB");
    }

    /**
     * Test for {@link RecommendCountryController#countRecommendations()}.
     * Verifies retrieval of country recommendation counts with the specific JSON structure.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void countRecommendations_shouldReturnCountryCounts() throws Exception {
        createAndSaveTestRecommendCountry("Utopia", testCustomer1);
        createAndSaveTestRecommendCountry("Utopia", null);
        createAndSaveTestRecommendCountry("Erewhon", testCustomer2);

        mockMvc.perform(get(API_RECOMMEND_COUNTRY_BASE_URL + "/count").accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2))) // Expecting 2 distinct countries: Utopia, Erewhon
                .andExpect(jsonPath("$[?(@.name == 'Utopia')]._count.name").value(2)) // Check count for Utopia
                .andExpect(jsonPath("$[?(@.name == 'Erewhon')]._count.name").value(1)); // Check count for Erewhon
    }

    /**
     * Test for {@link RecommendCountryController#remove(Integer)}.
     * Verifies that a recommended country can be successfully removed.
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void remove_shouldRemoveRecommendedCountry() throws Exception {
        RecommendedCountry savedRC = createAndSaveTestRecommendCountry("Narnia", testCustomer1);

        mockMvc.perform(delete(API_RECOMMEND_COUNTRY_BASE_URL + "/{id}", savedRC.getId()).with(csrf())
                                .accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(savedRC.getId()))
                .andExpect(jsonPath("$.name").value("Narnia"))
                .andExpect(jsonPath("$.customer_id").value(testCustomer1.getId()));

        assertFalse(recommendCountryRepository.existsById(savedRC.getId()), "Entity should be deleted from DB");
    }

    /**
     * Test for {@link RecommendCountryController#getByCustomer(Integer)}.
     * Verifies retrieval of recommendations for a specific customer.
     */
    @Test
    @WithMockUser(roles = {TestRole.CUSTOMER})
    void getByCustomer_shouldReturnRecommendationsForCustomer() throws Exception {
        createAndSaveTestRecommendCountry("Oz", testCustomer1);
        createAndSaveTestRecommendCountry("MiddleEarth", testCustomer1);
        createAndSaveTestRecommendCountry("Hogwarts", testCustomer2);

        mockMvc.perform(get(API_RECOMMEND_COUNTRY_BASE_URL + "/customer/{customerId}", testCustomer1.getId()).accept(
                        MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$[1].customer_id").value(testCustomer1.getId()));
    }

    /**
     * Test for {@link RecommendCountryController#getAll()}.
     * Verifies retrieval of all recommended countries.
     */
    @Test
    @WithMockUser(roles = {TestRole.CLERK})
    void getAll_shouldReturnAllRecommendedCountries() throws Exception {
        RecommendedCountry rc1 = createAndSaveTestRecommendCountry("Xanadu", testCustomer1);
        RecommendedCountry rc2 = createAndSaveTestRecommendCountry("El Dorado", testCustomer2);

        mockMvc.perform(get(API_RECOMMEND_COUNTRY_BASE_URL).accept(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[?(@.id == " + rc1.getId() + ")].name").value("Xanadu"))
                .andExpect(jsonPath("$[?(@.id == " + rc1.getId() + ")].customer_id").value(testCustomer1.getId()))
                .andExpect(jsonPath("$[?(@.id == " + rc2.getId() + ")].name").value("El Dorado"))
                .andExpect(jsonPath("$[?(@.id == " + rc2.getId() + ")].customer_id").value(testCustomer2.getId()));
    }
}