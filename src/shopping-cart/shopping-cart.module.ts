import { Module } from "@nestjs/common";
import { ShoppingCartService } from "./shopping-cart.service";
import { ShoppingCartController } from "./shopping-cart.controller";

import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { HttpApiModule } from "@/http/http.module";
import { MondayService } from "@/integration/monday.service";
import { CouponModule } from "@/coupon/coupon.module";

@Module({
  imports: [CustomerIoModule, HttpApiModule, CouponModule],
  controllers: [ShoppingCartController],
  providers: [ShoppingCartService, MondayService],
})
export class ShoppingCartModule {}
