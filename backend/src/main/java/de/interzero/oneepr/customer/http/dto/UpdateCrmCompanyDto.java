package de.interzero.oneepr.customer.http.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UpdateCrmCompanyDto {

    @Schema(description = "Customer's company name")
    @JsonProperty("name")
    private String name;

    @Schema(description = "VAT ID")
    @JsonProperty("vat")
    private String vat;

    @Schema(description = "Tax ID")
    @JsonProperty("tin")
    private String tin;

    @Schema(description = "Address")
    @JsonProperty("address")
    private AddressDto address;

    @Data
    public static class AddressDto {

        @Schema(description = "City")
        @JsonProperty("city")
        private String city;

        @Schema(description = "ZIP code")
        @JsonProperty("zip_code")
        private String zipCode;

        @Schema(description = "Country code")
        @JsonProperty("country_code")
        private String countryCode;

        @Schema(description = "Street and number")
        @JsonProperty("street_and_number")
        private String streetAndNumber;
    }
}
