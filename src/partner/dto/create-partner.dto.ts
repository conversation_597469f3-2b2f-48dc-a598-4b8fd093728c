import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { CreatePartnerBankingDto } from "./create-partner-banking.dto";
import { CreatePartnerCompanyDto } from "./create-partner-company.dto";

export class CreatePartnerDto {
  @ApiProperty({ description: "First name of the partner" })
  @IsString()
  @IsNotEmpty()
  partner_firstname: string;

  @ApiProperty({ description: "Last name of the partner" })
  @IsString()
  @IsNotEmpty()
  partner_lastname: string;

  @ApiProperty({ description: "Email address of the partner" })
  @IsString()
  @IsNotEmpty()
  partner_email: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: "Password of the partner" })
  partner_password: string;

  @ApiPropertyOptional({ description: "User ID associated with the partner" })
  user_id?: number;

  @ApiPropertyOptional()
  banking?: CreatePartnerBankingDto;

  @ApiPropertyOptional()
  @IsOptional()
  company?: CreatePartnerCompanyDto;

  @ApiPropertyOptional()
  @IsOptional()
  no_provision_negotiated?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  payout_cycle?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  commission_mode?: string;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  commission_percentage?: number;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coupons?: number[];

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  marketing_material_id?: number;

  @ApiPropertyOptional({ description: "Marketing material fields" })
  @IsOptional()
  new_marketing_material_name?: string;

  @IsOptional()
  @ApiPropertyOptional({
    type: "string",
    format: "binary",
    description: "Contract file",
  })
  contract_file?: any;

  @IsOptional()
  @ApiPropertyOptional({
    type: "array",
    items: {
      type: "string",
      format: "binary",
    },
    description: "Marketing material files",
  })
  new_marketing_material_files?: any[];

  @ApiPropertyOptional({ description: "User information" })
  @IsOptional()
  user?: {
    id: string;
    role: string;
  };
}
