import { ApiProperty } from "@nestjs/swagger";

export class CreateLicenseVolumeReportItemDto {
  @ApiProperty({
    description: "The license volume report ID",
    example: 1,
  })
  license_volume_report_id: number;

  @ApiProperty({
    description: "The setup fraction ID",
    example: 1,
  })
  setup_fraction_id: number;

  @ApiProperty({
    description: "The setup fraction code",
    example: "DEOK34",
  })
  setup_fraction_code: string;

  @ApiProperty({
    description: "The setup column ID",
    example: 1,
  })
  setup_column_id: number;

  @ApiProperty({
    description: "The setup column code",
    example: "DEOK34",
  })
  setup_column_code: string;

  @ApiProperty({
    description: "The value of the license volume report item",
    example: 1,
  })
  value: number;
}
