import { LicenseVolumeReportStatus } from "@prisma/client";
import { ApiProperty } from "@nestjs/swagger";

export class CreateLicenseVolumeReportDto {
  @ApiProperty({
    description: "The setup report set fraction ID",
    example: 1,
  })
  setup_report_set_fraction_id: number;

  @ApiProperty({
    description: "The license packaging service ID",
    example: 1,
  })
  license_packaging_service_id: number;

  @ApiProperty({
    description: "The status of the license volume report",
    example: LicenseVolumeReportStatus.DECLINED,
  })
  status: LicenseVolumeReportStatus;

  @ApiProperty({
    description: "The year of the license volume report",
    example: 2024,
  })
  year: number;

  @ApiProperty({
    description: "The interval of the license volume report",
    example: "Q1, Q2, JANUARY, FEBRUARY, 2023, 2024",
  })
  interval: string;

  @ApiProperty({
    description: "The report table of the license volume report",
    example: {},
  })
  report_table: Record<string, any>;
}
