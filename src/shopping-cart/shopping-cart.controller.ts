import { Controller, Get, Post, Body, Param, Delete, Put } from "@nestjs/common";
import { ShoppingCartService } from "./shopping-cart.service";
import { CreateShoppingCartDto } from "./dto/create-shopping-cart.dto";
import { UpdateShoppingCartDto } from "./dto/update-shopping-cart.dto";
import { ApiTags } from "@nestjs/swagger";
import { CreateShoppingCartItemDto } from "./dto/create-shopping-cart-item.dto";
import { UpdateShoppingCartItemDto } from "./dto/update-shopping-cart-item.dto";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { PublicRoute } from "@/shared/auth/public-route.decorator";

@ApiTags("Shopping Cart")
@Controller("shopping-cart")
export class ShoppingCartController {
  constructor(private readonly shoppingCartService: ShoppingCartService) {}

  @Post()
  @PublicRoute()
  create(@Body() createShoppingCartDto: CreateShoppingCartDto) {
    return this.shoppingCartService.create(createShoppingCartDto);
  }

  @Get(":id")
  @PublicRoute()
  findOne(@Param("id") id: string) {
    return this.shoppingCartService.findOne(id);
  }

  @Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
  @Get("email/:email")
  findOneByEmail(@Param("email") email: string, @User() user: AuthenticatedUser) {
    return this.shoppingCartService.findOneByEmail(email, user);
  }

  @Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
  @Get("purchased/:email")
  findLastPurchasedByEmail(@Param("email") email: string, @User() user: AuthenticatedUser) {
    return this.shoppingCartService.findLastPurchasedByEmail(email, user);
  }

  @PublicRoute()
  @Put(":id")
  update(
    @Param("id") id: string,
    @Body() updateShoppingCartDto: UpdateShoppingCartDto,
    @User() user: AuthenticatedUser
  ) {
    return this.shoppingCartService.update(id, updateShoppingCartDto, user);
  }

  @Post(":id/items")
  @PublicRoute()
  addItem(
    @Param("id") id: string,
    @Body() addShoppingCartItemDto: CreateShoppingCartItemDto,
    @User() user: AuthenticatedUser
  ) {
    return this.shoppingCartService.addItem(id, addShoppingCartItemDto, user);
  }

  @Put(":id/items/:item_id")
  @PublicRoute()
  updateItem(
    @Param("id") id: string,
    @Param("item_id") item_id: string,
    @Body() updateShoppingCartItemDto: UpdateShoppingCartItemDto,
    @User() user: AuthenticatedUser
  ) {
    return this.shoppingCartService.updateItem(id, Number(item_id), updateShoppingCartItemDto, user);
  }

  @Delete(":id/items/:item_id")
  @PublicRoute()
  removeItem(@Param("id") id: string, @Param("item_id") item_id: string, @User() user: AuthenticatedUser) {
    return this.shoppingCartService.removeItem(id, Number(item_id), user);
  }
}
