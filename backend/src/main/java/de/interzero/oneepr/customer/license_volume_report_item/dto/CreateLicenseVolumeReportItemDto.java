package de.interzero.oneepr.customer.license_volume_report_item.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new license volume report item.
 * Converted from TypeScript create-license-volume-report-item.dto.ts
 * Maintains exact same structure, variable names, and properties.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateLicenseVolumeReportItemDto {

    @JsonProperty("license_volume_report_id")
    @Schema(
            description = "The license volume report ID",
            example = "1"
    )
    private Integer licenseVolumeReportId;

    @JsonProperty("setup_fraction_id")
    @Schema(
            description = "The setup fraction ID",
            example = "1"
    )
    private Integer setupFractionId;

    @JsonProperty("setup_fraction_code")
    @Schema(
            description = "The setup fraction code",
            example = "DEOK34"
    )
    private String setupFractionCode;

    @JsonProperty("setup_column_id")
    @Schema(
            description = "The setup column ID",
            example = "1"
    )
    private Integer setupColumnId;

    @JsonProperty("setup_column_code")
    @Schema(
            description = "The setup column code",
            example = "DEOK34"
    )
    private String setupColumnCode;

    @JsonProperty("value")
    @Schema(
            description = "The value of the license volume report item",
            example = "1"
    )
    private Integer value;
}
