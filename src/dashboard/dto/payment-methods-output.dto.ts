import { ApiProperty } from "@nestjs/swagger";

class PaymentMethodSummaryDto {
  @ApiProperty()
  label: string;

  @ApiProperty()
  total_transactions: number;

  @ApiProperty()
  percentage: number;
}

export class PaymentMethodsSummaryOutputDto {
  @ApiProperty({ isArray: true, type: PaymentMethodSummaryDto })
  payment_methods: PaymentMethodSummaryDto[];

  @ApiProperty()
  total_transactions: number;
}
