package de.interzero.oneepr.admin.admin;

import de.interzero.oneepr.admin.admin.dto.AdminUserResponse;
import de.interzero.oneepr.admin.admin.dto.CreateAdminDto;
import de.interzero.oneepr.admin.admin.dto.FindAllAdminDto;
import de.interzero.oneepr.admin.admin.dto.UpdateAdminDto;
import de.interzero.oneepr.auth.user.UserMapper;
import de.interzero.oneepr.auth.user.dto.UserResponseDto;
import de.interzero.oneepr.common.string.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(Api.ADMIN)
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;

    /**
     * Create a new admin user.
     *
     * @param dto data for new admin user
     * @return created user
     */
    @PostMapping
    @Operation(summary = "Create admin user")
    @ApiResponse(
            responseCode = "200",
            description = "Admin user created successfully",
            content = @Content(schema = @Schema(implementation = AdminUserResponse.class))
    )
    @ApiResponse(
            responseCode = "500",
            description = "Failed to create admin user"
    )
    public ResponseEntity<AdminUserResponse> create(@Valid @RequestBody CreateAdminDto dto) {
        return ResponseEntity.ok(AdminMapper.toDto(adminService.create(dto)));
    }

    /**
     * Find all admin users filtered by role, name, and active status.
     *
     * @param isActive whether the user is active (optional)
     * @param role     comma-separated list of role names (required)
     * @param name     name or part of the name to filter by (optional)
     * @return list of users
     */
    @GetMapping
    @Operation(summary = "Get all admin users")
    @ApiResponse(
            responseCode = "200",
            description = "Admin users retrieved successfully",
            content = @Content(array = @ArraySchema(schema = @Schema(implementation = AdminUserResponse.class)))

    )
    @ApiResponse(
            responseCode = "500",
            description = "Failed to retrieve admin users"
    )
    public ResponseEntity<List<AdminUserResponse>> findAll(@RequestParam(
                                                                   name = "is_active",
                                                                   required = false
                                                           ) String isActive,
                                                           @RequestParam(
                                                                   name = "role"
                                                           ) String role,
                                                           @RequestParam(
                                                                   name = "name",
                                                                   required = false
                                                           ) String name) {
        FindAllAdminDto dto = new FindAllAdminDto();
        dto.setIsActive(isActive);
        dto.setRole(role);
        dto.setName(name);
        return ResponseEntity.ok(AdminMapper.toDtoList(adminService.findAll(dto)));
    }

    /**
     * Find a single admin user by ID.
     *
     * @param id ID of the user
     * @return user details
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get admin user by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Admin user retrieved successfully",
            content = @Content(schema = @Schema(implementation = AdminUserResponse.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Admin user not found"
    )
    @ApiResponse(
            responseCode = "500",
            description = "Failed to retrieve admin user"
    )
    public ResponseEntity<AdminUserResponse> findOne(@PathVariable String id) {
        return ResponseEntity.ok(AdminMapper.toDto(adminService.findOne(id)));
    }

    /**
     * Update an admin user by ID.
     *
     * @param id  ID of the user
     * @param dto update data
     * @return updated user
     */
    @PatchMapping("/{id}")
    @Operation(summary = "Update admin user by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Admin user updated successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Admin user not found"
    )
    @ApiResponse(
            responseCode = "500",
            description = "Failed to update admin user"
    )
    public ResponseEntity<UserResponseDto> update(@PathVariable Integer id,
                                                  @Valid @RequestBody UpdateAdminDto dto) {
        return ResponseEntity.ok(UserMapper.toDto(adminService.update(id, dto)));
    }

    /**
     * Delete an admin user by ID.
     *
     * @param id ID of the user
     * @return 204 No Content if success
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete admin user by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Admin user deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Admin user not found"
    )
    @ApiResponse(
            responseCode = "500",
            description = "Failed to delete admin user"
    )
    public ResponseEntity<Integer> remove(@PathVariable Integer id) {
        return ResponseEntity.ok(adminService.remove(id));
    }
}
