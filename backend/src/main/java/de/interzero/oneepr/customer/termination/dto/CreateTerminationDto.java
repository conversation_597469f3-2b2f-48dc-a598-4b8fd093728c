package de.interzero.oneepr.customer.termination.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.termination.Termination;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Data Transfer Object for creating a new contract termination request.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateTerminationDto extends BaseDto {

    @NotNull
    @JsonProperty("contract_id")
    @Schema(
            description = "The ID of the contract to be terminated.",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer contractId;

    @JsonProperty("status")
    @Schema(
            description = "The initial status of the termination request.",
            example = "REQUESTED",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Termination.Status status;

    @JsonProperty("country_codes")
    @Schema(
            description = "A list of country codes related to the termination.",
            example = "[\"FR\", \"IT\", \"ES\"]",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<String> countryCodes;

    @NotEmpty
    @JsonProperty("reason_ids")
    @Schema(
            description = "A list of reason IDs explaining the termination.",
            example = "[1, 2, 3]",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private List<Integer> reasonIds;

    @JsonProperty("termination_file_id")
    @Schema(
            description = "The ID of the uploaded termination request file.",
            example = "file_abc123",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String terminationFileId;

    @JsonProperty("proof_of_termination_file_id")
    @Schema(
            description = "The ID of the uploaded proof of termination file.",
            example = "file_xyz789",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String proofOfTerminationFileId;
}
