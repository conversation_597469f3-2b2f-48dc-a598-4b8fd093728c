import { createColumnHelper } from "@tanstack/react-table";
import { Launch } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { ContactInformationModal } from "@/components/modules/customers/customer-profile/contact-information-modal";
import { ListCustomer } from "@/lib/api/customer/types";

interface Client {
  companyName?: string;
  countryCode?: string;
  addressLine?: string;
  city?: string;
  zipCode?: string;
  streetAndNumber?: string;
  documentType?: "TAX" | "VAT";
  vatId?: string;
  taxNumber?: string;
  salutation?: string;
  firstName?: string;
  surname?: string;
  phone?: string;
  mobile?: string;
  emails?: string[];
  countries?: string[];
  phones?: string[];
  emailLogin?: string;
}

export function useClientsCheckColumns() {
  const columnHelper = createColumnHelper<ListCustomer>();
  const [isOpenContactInformationModal, setIsOpenContactInformationModal] = useState(false);

  return [
    columnHelper.display({
      id: "companyName",
      header: "Company Name",
      cell: (info) => info.row.original.companies?.[0].name,
    }),
    columnHelper.display({
      id: "contact",
      header: "Contact",
      cell: (info) => {
        const { first_name, last_name } = info.row.original;

        return (
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              {first_name} {last_name}
              <Launch
                className="fill-support-blue w-5 h-5 cursor-pointer"
                onClick={() => setIsOpenContactInformationModal(true)}
              />
            </div>
            <div className="text-xs font-medium text-[#656773]">{info.row.original.email ?? ""}</div>
            <ContactInformationModal customer={info.row.original} />
          </div>
        );
      },
    }),
    // columnHelper.accessor("countryCode", {
    //   header: "Country Code",
    //   cell: (info) => info.getValue(),
    // }),
    // columnHelper.accessor("city", {
    //   header: "City",
    //   cell: (info) => info.getValue(),
    // }),
    // columnHelper.accessor("zipCode", {
    //   header: "ZIP Code",
    //   cell: (info) => info.getValue(),
    // }),
  ];
}
