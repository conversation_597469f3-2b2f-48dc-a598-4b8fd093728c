package de.interzero.oneepr.admin.service_setup.packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new packaging service.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreatePackagingServiceDto extends BaseDto {

    @JsonProperty("name")
    @Schema(description = "Name of the packaging service")
    private String name;

    @JsonProperty("description")
    @Schema(description = "Description of the packaging service")
    private String description;

    @JsonProperty("country_id")
    @Schema(description = "ID of the country this packaging service belongs to")
    private Integer countryId;
}
