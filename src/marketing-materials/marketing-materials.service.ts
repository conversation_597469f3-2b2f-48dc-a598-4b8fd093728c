import { DatabaseService } from "@/database/database.service";
import { FileService } from "@/file/file.service";
import { Injectable, NotFoundException } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { CreateMarketingMaterialDto } from "./dto/create-marketing-material.dto";
import { FindAllMarketingMaterialsDto } from "./dto/find-all-marketing-materials.dto";
import { UpdateMarketingMaterialDto } from "./dto/update-marketing-material.dto";

interface CreateMarketingMaterialParams extends CreateMarketingMaterialDto {
  files: Express.Multer.File[];
  user: { id: string; role: string };
}

interface UpdateMarketingMaterialParams extends UpdateMarketingMaterialDto {
  files: Express.Multer.File[];
  user: { id: string; role: string };
}

@Injectable()
export class MarketingMaterialsService {
  constructor(private databaseService: DatabaseService, private fileService: FileService) {}

  async create(createMarketingMaterialDto: CreateMarketingMaterialParams, transaction?: Prisma.TransactionClient) {
    const { files, user } = createMarketingMaterialDto;

    const partners = createMarketingMaterialDto.partners
      ? createMarketingMaterialDto.partners.split(",").map(Number).filter(Boolean)
      : [];

    const marketingMaterial = await (transaction ?? this.databaseService).marketingMaterial.create({
      data: {
        name: createMarketingMaterialDto.name,
        start_date: createMarketingMaterialDto.start_date,
        end_date: createMarketingMaterialDto.end_date,
        category: createMarketingMaterialDto.category,
        partner_restriction: createMarketingMaterialDto.partner_restriction,
        ...(partners.length > 0 && {
          partners: {
            createMany: {
              data: partners.map((partner) => ({
                partner_id: partner,
              })),
            },
          },
        }),
      },
    });

    if (files.length > 0) {
      await Promise.all(
        files.map((file) =>
          this.fileService.uploadFile(
            {
              type: "MARKETING_MATERIAL",
              user: {
                id: user.id ?? "1314",
                role: user.role,
              },
              marketing_material_id: marketingMaterial.id,
            },
            file
          )
        )
      );
    }

    return marketingMaterial;
  }

  async findAll(query: FindAllMarketingMaterialsDto) {
    const { page = 1, limit = 10, name, is_active } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: Prisma.MarketingMaterialWhereInput = {
      deleted_at: null,
      ...(name && { name: { startsWith: name, mode: "insensitive" } }),
      ...(is_active !== undefined &&
        (is_active === "true"
          ? {
              OR: [{ end_date: null }, { end_date: { gte: new Date() } }],
            }
          : {
              AND: [{ end_date: { not: null } }, { end_date: { lt: new Date() } }],
            })),
    };

    const [marketingMaterials, total] = await Promise.all([
      this.databaseService.marketingMaterial.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: name ? undefined : { created_at: "desc" },
        include: {
          files: { where: { deleted_at: null } },
          partners: true,
        },
      }),
      this.databaseService.marketingMaterial.count({ where }),
    ]);

    return {
      marketingMaterials,
      count: total,
      pages: Math.ceil(total / Number(limit)),
      current_page: Number(page),
      limit: Number(limit),
    };
  }

  async findOne(id: number) {
    const marketingMaterial = await this.databaseService.marketingMaterial.findUnique({
      where: { id },
      include: {
        files: { where: { deleted_at: null } },
        partners: {
          include: {
            partner: true,
          },
        },
      },
    });

    if (!marketingMaterial) {
      throw new NotFoundException("Marketing material not found");
    }

    if (marketingMaterial.partner_restriction === "ALL") {
      const partners = await this.databaseService.partner.findMany({
        where: {
          deleted_at: null,
        },
      });

      marketingMaterial.partners = partners.map((partner) => ({
        id: partner.id,
        partner_id: partner.id,
        marketing_material_id: marketingMaterial.id,
        partner: partner,
      }));
    }

    return marketingMaterial;
  }

  async update(id: number, updateMarketingMaterialDto: UpdateMarketingMaterialParams) {
    const { files, user } = updateMarketingMaterialDto;

    const existingMaterial = await this.databaseService.marketingMaterial.findUnique({
      where: { id },
    });

    if (!existingMaterial) {
      throw new NotFoundException(`Marketing material with ID ${id} not found`);
    }

    return this.databaseService.$transaction(async (tx) => {
      if (updateMarketingMaterialDto.partners) {
        await tx.marketingMaterialPartner.deleteMany({
          where: { marketing_material_id: id },
        });

        const partners = updateMarketingMaterialDto.partners
          ? updateMarketingMaterialDto.partners.split(",").map(Number).filter(Boolean)
          : [];

        if (partners.length > 0) {
          await this.databaseService.marketingMaterialPartner.createMany({
            data: partners.map((partner) => ({
              marketing_material_id: id,
              partner_id: partner,
            })),
          });
        }
      }

      if (files.length > 0) {
        await Promise.all(
          files.map((file) =>
            this.fileService.uploadFile(
              {
                type: "MARKETING_MATERIAL",
                user: {
                  id: user.id ?? "1314",
                  role: user.role,
                },
                marketing_material_id: id,
              },
              file
            )
          )
        );
      }

      return tx.marketingMaterial.update({
        where: { id },
        data: {
          name: updateMarketingMaterialDto.name,
          start_date: updateMarketingMaterialDto.start_date,
          end_date: updateMarketingMaterialDto.end_date,
          category: updateMarketingMaterialDto.category,
          partner_restriction: updateMarketingMaterialDto.partner_restriction,
          updated_at: new Date(),
        },
        include: {
          files: { where: { deleted_at: null } },
          partners: true,
        },
      });
    });
  }

  remove(id: number) {
    return this.databaseService.marketingMaterial.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }
}
