import { Injectable, OnModuleInit } from "@nestjs/common";
import { PrismaClient } from "@prisma/client";

@Injectable()
export class DatabaseService extends PrismaClient implements OnModuleInit {
  constructor() {
    super({
      // log: ["info", "warn", "error", "query"],
      transactionOptions: {
        maxWait: 30000,
        timeout: 30000,
      },
    });
  }

  async onModuleInit() {
    await this.$connect();
  }
}
