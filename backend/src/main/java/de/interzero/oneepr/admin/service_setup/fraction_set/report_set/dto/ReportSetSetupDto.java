package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.dto.FilesDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.dto.ReportSetColumnDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto.ReportSetFractionDto;
import de.interzero.oneepr.admin.service_setup.packaging_service.PackagingService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * Represents a specialized view of a {@link ReportSet} for the service setup administration interface.
 *
 * <p><b>Why this DTO exists:</b>
 * This DTO is designed to provide a specific data structure for the frontend when an administrator is configuring
 * report sets within a country's service setup. Its primary purpose is to flatten and denormalize information
 * from the parent {@link PackagingService}, specifically whether that service has a "REPORT_SET" commitment criteria.
 * This pre-calculation simplifies client-side logic by providing a direct boolean flag.
 *
 * <p><b>What this DTO contains:</b>
 * It includes all essential fields from the {@code ReportSet} entity, the full parent {@code PackagingService} object,
 * and a calculated boolean field {@code has_criteria}. This flag is {@code true} if the associated packaging service
 * has at least one commitment criteria of type 'REPORT_SET'.
 *
 * <p><b>Where this DTO is used:</b>
 * This DTO is the return type for the {@code ServiceSetupService.findServiceSetupReportSets} method and is serialized
 * as the JSON response for the corresponding endpoint in the {@code ServiceSetupController}.
 *
 * @see de.interzero.oneepr.admin.service_setup.ServiceSetupService#findServiceSetupReportSets(String)
 * @see de.interzero.oneepr.admin.service_setup.ServiceSetupController#findServiceSetupReportSets(String)
 */
@Data
public class ReportSetSetupDto {
    @Schema(description = "The unique identifier of the report set.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The display name of the report set.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "The mode of the report set.", implementation = ReportSet.ReportSetMode.class)
    @JsonProperty("mode")
    private ReportSet.ReportSetMode mode;

    @Schema(description = "The type of the report set.", implementation = ReportSet.ReportSetType.class)
    @JsonProperty("type")
    private ReportSet.ReportSetType type;

    @Schema(description = "The ID of the packaging service this report set belongs to.")
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @Schema(description = "The full object of the associated sheet file template, if any.")
    @JsonProperty("sheet_file")
    private FilesDto sheetFile;

    @Schema(description = "The ID of the associated sheet file template.")
    @JsonProperty("sheet_file_id")
    private String sheetFileId;

    @Schema(description = "A description for the associated sheet file.")
    @JsonProperty("sheet_file_description")
    private String sheetFileDescription;

    @Schema(description = "The timestamp when the report set was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp when the report set was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The timestamp when the report set was soft-deleted.")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Schema(description = "A flag indicating if the parent packaging service has relevant criteria.")
    @JsonProperty("has_criteria")
    private Boolean hasCriteria;

    @Schema(description = "The collection of columns associated with this report set.")
    @JsonProperty("columns")
    private List<ReportSetColumnDto> columns;

    @Schema(description = "The collection of fractions associated with this report set.")
    @JsonProperty("fractions")
    private List<ReportSetFractionDto> fractions;
}
