package de.interzero.oneepr.customer.recommend_country;


import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.recommend_country.dto.CreateRecommendCountryDto;
import de.interzero.oneepr.customer.recommend_country.dto.TargetCountFormatDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "RecommendCountry",
        description = "Operations related to Recommended Countries"
)

@RestController
@RequestMapping(Api.RECOMMEND_COUNTRY)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class RecommendCountryController {

    private final RecommendCountryService recommendCountryService;

    /**
     * Create a new recommended country.
     * This route is public as per the @PublicRoute() decorator in the original NestJS code.
     * The service validates the country externally and checks for customer existence.
     */
    @PreAuthorize("permitAll()")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
            summary = "Create Recommended Country",
            responses = {@ApiResponse(
                    responseCode = "201",
                    description = "The recommended country has been successfully created.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = RecommendedCountry.class)
                    )
            ), @ApiResponse(
                    responseCode = "404",
                    description = "Country name does not exist (validated externally) OR Customer not found OR RecommendedCountry not found",
                    content = @Content
            ), @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error during validation or database operation",
                    content = @Content
            )}
    )
    public RecommendedCountry create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "DTO for creating a new recommended country.",
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateRecommendCountryDto.class)
            )
    ) @RequestBody CreateRecommendCountryDto createRecommendCountryDto) {
        return this.recommendCountryService.create(createRecommendCountryDto);
    }

    /**
     * Count recommendations grouped by country name.
     * The service layer performs the aggregation.
     */
    @GetMapping("/count")
    @Operation(
            summary = "Count Recommendations by Country",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "Count of recommendations by country.",
                    content = @Content(
                            mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = TargetCountFormatDto.class))
                    )
            )}
    )
    public List<TargetCountFormatDto> countRecommendations() {
        return this.recommendCountryService.countRecommendations();
    }

    /**
     * Remove a recommended country by its ID.
     * The service layer handles finding and deleting the entity.
     *
     * @param id The integer ID of the recommended country to remove.
     */
    @DeleteMapping("/{id}")
    @Operation(
            summary = "Remove Recommended Country",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The recommended country has been successfully removed.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = RecommendedCountry.class)
                    )
            ), @ApiResponse(
                    responseCode = "404",
                    description = "RecommendedCountry not found with the given ID",
                    content = @Content
            )}
    )
    public RecommendedCountry remove(@Parameter(description = "ID of the recommended country to remove") @PathVariable("id") Integer id) {
        return this.recommendCountryService.remove(id);
    }

    /**
     * Get recommended countries by a specific customer ID.
     * The service layer fetches recommendations associated with the given customer.
     *
     * @param customerId The integer ID of the customer.
     */
    @GetMapping("/customer/{customerId}")
    @Operation(
            summary = "Get Recommendations by Customer",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "List of recommendations by the specified customer.",
                    content = @Content(
                            mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = RecommendedCountry.class))
                    )
            ), @ApiResponse(
                    responseCode = "400",
                    description = "Customer ID cannot be null (if validation added in service/controller)",
                    content = @Content
            )}
    )
    public List<RecommendedCountry> getByCustomer(@Parameter(description = "ID of the customer to fetch recommendations for") @PathVariable("customerId") Integer customerId) {
        return this.recommendCountryService.getByCustomer(customerId);
    }

    /**
     * Get all recommended countries.
     * The service layer retrieves all records.
     */
    @GetMapping
    @Operation(
            summary = "Get All Recommended Countries",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "List of all recommended countries.",
                    content = @Content(
                            mediaType = "application/json",
                            array = @ArraySchema(schema = @Schema(implementation = RecommendedCountry.class))
                    )
            )}
    )
    public List<RecommendedCountry> getAll() {
        return this.recommendCountryService.getAll();
    }
}