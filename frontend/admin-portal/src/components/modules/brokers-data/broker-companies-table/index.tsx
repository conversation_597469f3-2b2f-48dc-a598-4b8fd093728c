"use client";

// import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { createColumnHelper } from "@tanstack/react-table";

// import { FilterAlt, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import AsyncPaginatedTable from "@/components/common/async-paginated-table";
// import { DateRangeFilter } from "@/components/common/date-range-filter";
// import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
// import { Datatable } from "@/components/common/datatable";
import type { BrokerCompany } from "@/lib/api/broker-companies/types";
import { getBrokerCompanies } from "@/lib/api/broker-companies";
// import { useDateRangeFilter } from "@/hooks/use-date-range-filter";
import { useQueryFilter } from "@/hooks/use-query-filter";
// import { dateManager } from "@/utils/date-manager";
// import { YEARS } from "@/utils/get-years";

const columnHelper = createColumnHelper<BrokerCompany>();

interface BrokerCompaniesTableProps {
  brokerId: number;
}

export function BrokerCompaniesTable({ brokerId }: BrokerCompaniesTableProps) {
  const { paramValues, changeParam, changeParams } = useQueryFilter(["search", "page", "startDate", "endDate", "year"]);

  const search = paramValues.search || undefined;
  const page = paramValues.page ? Number(paramValues.page) : 1;

  const { data: brokerCompanies, isLoading } = useQuery({
    queryKey: ["broker-companies", { page, limit: 10, search }],
    queryFn: async () => await getBrokerCompanies(brokerId, { page, limit: 10, search }),
    enabled: !!brokerId,
  });

  function handleSearch(value: string) {
    changeParams({ search: value, page: "1" }, { scroll: false });
  }

  const columns = [
    columnHelper.accessor("name", {
      header: "Company Name",
      cell: (info) => {
        return <span className="px-4 text-[#002652]">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("register_number", {
      header: "Reg. Nr.",
      cell: (info) => {
        return <span className="px-4 text-sm">{info.getValue()}</span>;
      },
    }),
    columnHelper.accessor("vat", {
      header: "VAT",
      cell: (info) => {
        return <span className="px-4 text-sm">{info.getValue() || "---"}</span>;
      },
    }),
    columnHelper.accessor("tax", {
      header: "Tax",
      cell: (info) => {
        return <span className="px-4 text-sm">{info.getValue() || "---"}</span>;
      },
    }),
    columnHelper.accessor("country_code", {
      header: () => (
        <span>
          Tax Nr. <br /> Country CD
        </span>
      ),
      cell: (info) => {
        return <span className="px-4 text-sm">{info.getValue() || "---"}</span>;
      },
    }),
  ];

  return (
    <div className="flex flex-col gap-6 rounded-3xl">
      <div className="flex items-center justify-between gap-8">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={handleSearch} placeholder="Search by name" />
        </div>
        {/* <div className="flex items-center gap-6">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {yearsFilter.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => setSelectedYear(filter)}
                className="flex items-center gap-2 text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <div className="h-4 border w-px border-tonal-dark-cream-60" />
          <DateRangeFilter
            endDate={endDate}
            selectedMonth={selectedMonth}
            selectedYear={selectedYear}
            onMonthChange={setSelectedMonth}
            onYearChange={setSelectedYear}
          />
        </div> */}
      </div>
      <div className="pb-24 rounded-xl overflow-hidden text-surface-01">
        <AsyncPaginatedTable
          columns={columns}
          currentPage={page}
          isLoading={isLoading}
          data={brokerCompanies?.companies || []}
          pages={brokerCompanies?.pages || 0}
          onPageChange={(page) => changeParam("page", page.toString())}
          noResultsMessage={search ? `No results for "${search}" in Broker Companies` : "No broker companies found"}
        />
      </div>
    </div>
  );
}
