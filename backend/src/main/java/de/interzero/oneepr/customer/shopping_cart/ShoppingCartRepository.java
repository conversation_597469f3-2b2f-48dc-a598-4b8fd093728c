package de.interzero.oneepr.customer.shopping_cart;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for managing {@link ShoppingCart} entities.
 */
@Repository
public interface ShoppingCartRepository extends JpaRepository<ShoppingCart, String> {

    /**
     * Finds a shopping cart by ID where it has not been soft-deleted.
     *
     * @param shoppingCartId the ID of the shopping cart
     * @return an optional containing the shopping cart if found and not deleted
     */
    Optional<ShoppingCart> findByIdAndDeletedAtIsNull(String shoppingCartId);

    /**
     * Updates the email associated with all shopping carts matching the old email,
     * only if the cart has not been soft-deleted.
     *
     * @param oldEmail the current email address stored in the shopping cart
     * @param newEmail the new email address to update
     */
    @Modifying
    @Query(
            """
                        UPDATE ShoppingCart sc 
                        SET sc.email = :newEmail 
                        WHERE sc.email = :oldEmail AND sc.deletedAt IS NULL
                    """
    )
    void updateEmailByOldEmailAndDeletedAtIsNull(@Param("oldEmail") String oldEmail,
                                                 @Param("newEmail") String newEmail);

    @Modifying
    @Query("UPDATE ShoppingCart s SET s.email = :newEmail WHERE s.email = :oldEmail AND s.deletedAt IS NULL")
    void updateEmailForActiveCarts(@Param("oldEmail") String oldEmail,
                                   @Param("newEmail") String newEmail);

    /**
     * Finds a shopping cart by its ID, status, and ensures it has not been soft-deleted.
     *
     * @param id the unique identifier of the shopping cart
     * @param status the status of the shopping cart
     * @return an optional containing the shopping cart if found and not deleted
     */
    Optional<ShoppingCart> findByIdAndStatusAndDeletedAtIsNull(String id,
                                                               ShoppingCart.Status status);


    /**
     * Find an open shopping cart by email.
     *
     * @param email the email address of the customer
     * @return optional shopping cart
     */
    Optional<ShoppingCart> findByEmailAndStatusAndDeletedAtIsNull(String email,
                                                                  ShoppingCart.Status status);

    /**
     * Find the most recently purchased shopping cart by email.
     *
     * @param email the email address of the customer
     * @return optional shopping cart with status PURCHASED, ordered by createdAt DESC
     */
    Optional<ShoppingCart> findTopByEmailAndStatusAndDeletedAtIsNullOrderByCreatedAtDescIdDesc(String email,
                                                                                               ShoppingCart.Status status);

    /**
     * Retrieves a list of shopping carts based on the specified status, where the email is not null,
     * the cart has not been soft-deleted, the cart is not marked as churned, and its last update
     * occurred before the specified date.
     *
     * @param status the status of the shopping carts to retrieve
     * @param churnDate the cutoff date; only carts updated before this date will be included
     * @return a list of shopping carts matching the specified criteria
     */
    List<ShoppingCart> findByStatusAndEmailIsNotNullAndDeletedAtIsNullAndIsChurnedFalseAndUpdatedAtBefore(ShoppingCart.Status status,
                                                                                                          Instant churnDate);

    /**
     * Marks carts as churned by setting {@code isChurned=true}.
     *
     * @param cartIds list of cart IDs
     */
    @Modifying
    @Query("UPDATE ShoppingCart c SET c.isChurned = true WHERE c.id IN :cartIds")
    void markCartsAsChurned(@Param("cartIds") List<String> cartIds);

    /**
     * Deletes all carts matching the given IDs.
     *
     * @param cartIds list of cart IDs
     */
    @Modifying
    @Query("DELETE FROM ShoppingCart c WHERE c.id IN :cartIds")
    void deleteCartsByIds(@Param("cartIds") List<String> cartIds);

}
