import { AuthLayout } from "../auth-layout";
import { RecoverPasswordForm } from "./recover-password-form";

interface RecoverPasswordModuleProps {
  email: string;
  token: string;
}

export function RecoverPasswordModule({ email, token }: RecoverPasswordModuleProps) {
  return (
    <AuthLayout imageUrl="/assets/images/garbage.png">
      <RecoverPasswordForm email={email} token={token} />
    </AuthLayout>
  );
}
