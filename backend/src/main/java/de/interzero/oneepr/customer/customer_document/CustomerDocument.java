package de.interzero.oneepr.customer.customer_document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "customer_document",
        schema = "public"
)
public class CustomerDocument {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_document_id_gen"
    )
    @SequenceGenerator(
            name = "customer_document_id_gen",
            sequenceName = "customer_document_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("customer")
    private Customer customer;

    @NotNull
    @Column(
            name = "document_url",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("document_url")
    private String documentUrl;

    @NotNull
    @Column(
            name = "status",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("status")
    private String status;

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return customer != null ? customer.getId() : null;
    }
}