import {
  CreateMarketingMaterialParams,
  GetAllMarketingMaterialsParams,
  GetAllMarketingMaterialsResponse,
  MarketingMaterialWithPartners,
  UpdateMarketingMaterialParams,
} from "./types";
import { customerApi } from "..";
import { ApiEndpoints } from "../endpoints";

export async function getMarketingMaterial(
  marketingMaterialId: string | number
): Promise<MarketingMaterialWithPartners> {
  const response = await customerApi.get(ApiEndpoints.marketingMaterials.findById(marketingMaterialId));
  return response.data;
}

export async function createMarketingMaterial(marketingMaterial: CreateMarketingMaterialParams) {
  const formData = new FormData();
  formData.append("name", marketingMaterial.name);

  if (marketingMaterial.start_date) formData.append("start_date", marketingMaterial.start_date);

  if (marketingMaterial.end_date) formData.append("end_date", marketingMaterial.end_date);

  formData.append("category", marketingMaterial.category);
  formData.append("partner_restriction", marketingMaterial.partner_restriction);

  if (marketingMaterial.partners?.length) formData.append("partners", marketingMaterial.partners.join(","));

  marketingMaterial.files
    .filter((file) => file.size > 0)
    .forEach((file) => {
      formData.append("files", file as unknown as Blob);
    });

  const response = await customerApi.post(ApiEndpoints.marketingMaterials.create, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
}

export async function updateMarketingMaterial(
  marketingMaterialId: string | number,
  data: UpdateMarketingMaterialParams
) {
  const formData = new FormData();
  formData.append("name", data.name);

  if (data.start_date) formData.append("start_date", data.start_date);
  if (data.end_date) formData.append("end_date", data.end_date);

  formData.append("category", data.category);
  formData.append("partner_restriction", data.partner_restriction);

  if (data.partners && data.partners.length > 0) {
    formData.append("partners", data.partners.join(","));
  }

  data.files
    ?.filter((file) => file.size > 0)
    .forEach((file) => {
      formData.append("files", file as unknown as Blob);
    });

  const response = await customerApi.put(ApiEndpoints.marketingMaterials.update(marketingMaterialId), formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
}

export async function deleteMarketingMaterial(marketingMaterialId: string | number) {
  await customerApi.delete(ApiEndpoints.marketingMaterials.delete(marketingMaterialId));
}

export async function getAllMarketingMaterials(
  params: GetAllMarketingMaterialsParams
): Promise<GetAllMarketingMaterialsResponse> {
  const response = await customerApi.get(ApiEndpoints.marketingMaterials.findAll, { params });
  return response.data;
}

export async function deleteMarketingMaterialFile(fileId: string) {
  await customerApi.delete(ApiEndpoints.marketingMaterials.deleteFile(fileId));
}

export async function downloadMarketingMaterialFile(fileId: string) {
  const response = await customerApi.get(ApiEndpoints.marketingMaterials.downloadFile(fileId), {
    responseType: "blob",
  });
  const url = response.request.responseURL;
  const filename = url.split("/").pop();
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
}
