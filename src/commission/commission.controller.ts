import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UsePipes, ValidationPipe } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import {
  CommissionServiceType,
  CommissionType,
  CommissionUserType,
  CouponDiscountType,
  CouponMode,
  CouponType,
} from "@prisma/client";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { CommissionService } from "./commission.service";
import { CreateCommissionDto } from "./dto/create-commission.dto";
import { FindAllCommissionsDto } from "./dto/find-all-comissions.dto";
import { UpdateCommissionDto } from "./dto/update-commission.dto";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("commission")
@Controller("commission")
@UsePipes(new ValidationPipe({ transform: true }))
export class CommissionController {
  constructor(private readonly commissionService: CommissionService) {}

  @Post("generate_coupon/:user_id")
  @ApiOperation({ summary: "Generate a coupon for a customer" })
  @ApiResponse({
    status: 200,
    description: "Coupon generated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string", nullable: true },
        type: { type: "enum", enum: Object.values(CouponType) },
        commission_percentage: { type: "number", nullable: true },
        is_active: { type: "boolean" },
        mode: { type: "enum", enum: Object.values(CouponMode) },
        link: { type: "string", nullable: true },
        buy_x_get_y: { type: "json", nullable: true },
        code: { type: "string" },
        discount_type: { type: "enum", enum: Object.values(CouponDiscountType) },
        elegible_products: { type: "json", nullable: true },
        end_date: { type: "string", format: "date-time" },
        max_amount: { type: "number", nullable: true },
        max_uses: { type: "number", nullable: true },
        max_uses_per_customer: { type: "number", nullable: true },
        min_amount: { type: "number", nullable: true },
        min_products: { type: "number", nullable: true },
        note: { type: "string", nullable: true },
        start_date: { type: "string", format: "date-time" },
        value: { type: "number" },
        redeemable_by_new_customers: { type: "boolean" },
        for_commission: { type: "boolean" },
        used_at: { type: "string", format: "date-time", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      id: 1,
      description: "Coupon 1",
      type: CouponType.CUSTOMER,
      commission_percentage: 10,
      is_active: true,
      mode: CouponMode.INDIVIDUAL,
      link: "https://www.google.com",
      buy_x_get_y: { x: 1, y: 1 },
      code: "123456",
      discount_type: CouponDiscountType.ABSOLUTE,
      elegible_products: [],
      end_date: new Date(),
      max_amount: 100,
      max_uses: 100,
      max_uses_per_customer: 100,
      min_amount: 100,
      min_products: 100,
      note: "Coupon 1",
      start_date: new Date(),
      value: 100,
      redeemable_by_new_customers: true,
      for_commission: true,
      used_at: null,
      created_at: new Date(),
      updated_at: new Date(),
      deleted_at: null,
    },
  })
  generateCoupon(@Param("user_id") user_id: string) {
    return this.commissionService.generateCoupon(+user_id);
  }

  @Post()
  @ApiOperation({ summary: "Create a commission" })
  @ApiResponse({
    status: 200,
    description: "Coupon generated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "enum", enum: Object.values(CommissionType) },
        commission_percentage: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        user_id: { type: "number" },
        user_type: { type: "enum", enum: Object.values(CommissionUserType) },
        commission_value: { type: "number" },
        coupon_id: { type: "number", nullable: true },
        coupon_code: { type: "string", nullable: true },
        affiliate_link: { type: "string", nullable: true },
        order_id: { type: "number" },
        service_type: { type: "enum", enum: Object.values(CommissionServiceType) },
        order_customer_id: { type: "number", nullable: true },
        used: { type: "boolean", nullable: true },
        net_turnover: { type: "number" },
      },
    },
    example: {
      id: 1,
      type: CommissionType.AFFILIATE_LINK,
      commission_percentage: 10,
      created_at: new Date(),
      user_id: 1,
      user_type: CommissionUserType.CUSTOMER,
      commission_value: 100,
      coupon_id: 1,
      coupon_code: "123456",
      affiliate_link: "https://www.google.com",
      order_id: 1,
      service_type: CommissionServiceType.EU_LICENSE,
      order_customer_id: 1,
      used: true,
      net_turnover: 100,
    },
  })
  create(@Body() createCommissionDto: CreateCommissionDto) {
    return this.commissionService.create(createCommissionDto);
  }

  @Get()
  @ApiOperation({ summary: "Find all commissions" })
  @ApiResponse({
    status: 200,
    description: "Commissions found successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          type: { type: "enum", enum: Object.values(CommissionType) },
          commission_percentage: { type: "number" },
          created_at: { type: "string", format: "date-time" },
          user_id: { type: "number" },
          user_type: { type: "enum", enum: Object.values(CommissionUserType) },
          commission_value: { type: "number" },
          coupon_id: { type: "number", nullable: true },
          coupon_code: { type: "string", nullable: true },
          affiliate_link: { type: "string", nullable: true },
          order_id: { type: "number" },
          service_type: { type: "enum", enum: Object.values(CommissionServiceType) },
          order_customer_id: { type: "number", nullable: true },
          used: { type: "boolean", nullable: true },
          net_turnover: { type: "number" },
        },
      },
    },
    example: [
      {
        id: 1,
        type: CommissionType.AFFILIATE_LINK,
        commission_percentage: 10,
        created_at: new Date(),
        user_id: 1,
        user_type: CommissionUserType.CUSTOMER,
        commission_value: 100,
        coupon_id: 1,
        coupon_code: "123456",
        affiliate_link: "https://www.google.com",
        order_id: 1,
        service_type: CommissionServiceType.EU_LICENSE,
        order_customer_id: 1,
        used: true,
        net_turnover: 100,
      },
    ],
  })
  findAll(@Query() query: FindAllCommissionsDto) {
    return this.commissionService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Find a commission by id" })
  @ApiResponse({
    status: 200,
    description: "Commission found successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "enum", enum: Object.values(CommissionType) },
        commission_percentage: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        user_id: { type: "number" },
        user_type: { type: "enum", enum: Object.values(CommissionUserType) },
        commission_value: { type: "number" },
        coupon_id: { type: "number", nullable: true },
        coupon_code: { type: "string", nullable: true },
        affiliate_link: { type: "string", nullable: true },
        order_id: { type: "number" },
        service_type: { type: "enum", enum: Object.values(CommissionServiceType) },
        order_customer_id: { type: "number", nullable: true },
        used: { type: "boolean", nullable: true },
        net_turnover: { type: "number" },
      },
    },
    example: {
      id: 1,
      type: CommissionType.AFFILIATE_LINK,
      commission_percentage: 10,
      created_at: new Date(),
      user_id: 1,
      user_type: CommissionUserType.CUSTOMER,
      commission_value: 100,
      coupon_id: 1,
      coupon_code: "123456",
      affiliate_link: "https://www.google.com",
      order_id: 1,
      service_type: CommissionServiceType.EU_LICENSE,
      order_customer_id: 1,
      used: true,
      net_turnover: 100,
    },
  })
  @ApiResponse({ status: 404, description: "Commission not found" })
  @ApiResponse({ status: 404, description: "Customer not found" })
  @ApiResponse({ status: 400, description: "Invalid commission id" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.commissionService.findOne(+id, user);
  }

  @Patch("use/:user_id")
  useCommission(@Param("user_id") user_id: string) {
    return this.commissionService.useCommission(+user_id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update a commission by id" })
  @ApiResponse({
    status: 200,
    description: "Commission updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "enum", enum: Object.values(CommissionType) },
        commission_percentage: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        user_id: { type: "number" },
        user_type: { type: "enum", enum: Object.values(CommissionUserType) },
        commission_value: { type: "number" },
        coupon_id: { type: "number", nullable: true },
        coupon_code: { type: "string", nullable: true },
        affiliate_link: { type: "string", nullable: true },
        order_id: { type: "number" },
        service_type: { type: "enum", enum: Object.values(CommissionServiceType) },
        order_customer_id: { type: "number", nullable: true },
        used: { type: "boolean", nullable: true },
        net_turnover: { type: "number" },
      },
    },
    example: {
      id: 1,
      type: CommissionType.AFFILIATE_LINK,
      commission_percentage: 10,
      created_at: new Date(),
      user_id: 1,
      user_type: CommissionUserType.CUSTOMER,
      commission_value: 100,
      coupon_id: 1,
      coupon_code: "123456",
      affiliate_link: "https://www.google.com",
      order_id: 1,
      service_type: CommissionServiceType.EU_LICENSE,
      order_customer_id: 1,
      used: true,
      net_turnover: 100,
    },
  })
  @ApiResponse({ status: 404, description: "Commission not found" })
  @ApiResponse({ status: 404, description: "Customer not found" })
  @ApiResponse({ status: 400, description: "Invalid commission id" })
  update(@Param("id") id: string, @Body() updateCommissionDto: UpdateCommissionDto, @User() user: AuthenticatedUser) {
    return this.commissionService.update(+id, updateCommissionDto, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a commission by id" })
  @ApiResponse({
    status: 200,
    description: "Commission deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Commission not found" })
  @ApiResponse({ status: 404, description: "Customer not found" })
  remove(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.commissionService.remove(+id, user);
  }
}
