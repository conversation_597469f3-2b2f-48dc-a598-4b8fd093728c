import * as Progress from "@radix-ui/react-progress";

const ProgressBar = ({ progress, color = "#002652" }: { progress: number; color?: string }) => {
  return (
    <Progress.Root
      className="relative overflow-hidden bg-tonal-dark-cream-80 rounded-full max-w-[300px] h-[8px] flex-1"
      style={{
        transform: "translateZ(0)",
      }}
      value={progress}
    >
      <Progress.Indicator
        className="w-full h-full transition-transform duration-&lsqb;660ms&rsqb; ease-[cubic-bezier(0.65, 0, 0.35, 1)]"
        style={{ backgroundColor: color, transform: `translateX(-${100 - progress}%)` }}
      />
    </Progress.Root>
  );
};

export default ProgressBar;
