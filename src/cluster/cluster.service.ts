import { DatabaseService } from "@/database/database.service";
import { Injectable, BadRequestException, NotFoundException } from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { CreateClusterDto } from "./dto/create-cluster.dto";
import { FindAllClustersPaginatedDto } from "./dto/find-all-clusters-paginated.dto";
import { UpdateClusterDto } from "./dto/update-cluster.dto";

@Injectable()
export class ClusterService {
  constructor(private databaseService: DatabaseService) {}

  async create(createClusterDto: CreateClusterDto) {
    const allCustomers = await this.databaseService.customer.findMany({
      where: {
        id: { in: createClusterDto.customers },
      },
    });

    if (allCustomers.length !== createClusterDto.customers.length) {
      throw new BadRequestException("Some customers do not exist");
    }

    const cluster = await this.databaseService.cluster.create({
      data: {
        ...createClusterDto,
        customers: {
          create: createClusterDto.customers.map((customer) => ({ customer_id: customer })),
        },
      },
      include: {
        customers: true,
      },
    });

    return cluster;
  }

  findAll() {
    return this.databaseService.cluster.findMany({
      where: { deleted_at: null },
      include: {
        customers: true,
      },
    });
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("Cluster ID is invalid");
    }

    const cluster = await this.databaseService.cluster.findUnique({
      where: { id, deleted_at: null },
      include: {
        customers: true,
      },
    });

    if (!cluster) {
      throw new NotFoundException("Cluster not found");
    }

    return cluster;
  }

  async update(id: number, updateClusterDto: UpdateClusterDto) {
    const cluster = await this.findOne(id);

    if (!cluster) {
      throw new NotFoundException("Cluster not found");
    }

    const { customers, ...rest } = updateClusterDto;

    if (customers) {
      const allCustomers = await this.databaseService.customer.findMany({
        where: { id: { in: customers } },
      });

      if (allCustomers.length !== customers.length) {
        throw new BadRequestException("Some customers do not exist");
      }
    }

    return this.databaseService.$transaction(async (tx) => {
      if (customers && customers.length) {
        await tx.clusterCustomers.deleteMany({
          where: { cluster_id: id },
        });

        await tx.clusterCustomers.createMany({
          data: customers.map((customer) => ({
            cluster_id: id,
            customer_id: customer,
          })),
        });
      }

      return tx.cluster.update({
        where: { id },
        data: {
          ...rest,
        },
        include: {
          customers: true,
        },
      });
    });
  }

  async remove(id: number) {
    const cluster = await this.findOne(id);

    if (!cluster) {
      throw new NotFoundException("Cluster not found");
    }

    await this.databaseService.cluster.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async findAllPaginated(query: FindAllClustersPaginatedDto) {
    const { page = 1, limit = 10 } = query;
    const skip = (page - 1) * limit;

    const whereInput: Prisma.ClusterWhereInput = {
      deleted_at: null,
      ...(query.name && { name: { startsWith: query.name, mode: "insensitive" } }),
      ...(query.start_date &&
        query.end_date && {
          registration_start_date: { gte: query.start_date },
          registration_end_date: { lte: query.end_date },
        }),
    };

    const [data, total] = await Promise.all([
      this.databaseService.cluster.findMany({
        where: whereInput,
        skip,
        take: Number(limit),
      }),
      this.databaseService.cluster.count({
        where: whereInput,
      }),
    ]);

    return {
      clusters: data,
      count: total,
      pages: Math.ceil(total / Number(limit)),
      current_page: Number(page),
      limit: Number(limit),
    };
  }
}
