import { ApiProperty } from "@nestjs/swagger";
import { ReportSetPriceListType } from "@prisma/client";

export class CreateReportSetPriceListDto {
  @ApiProperty({
    required: true,
    description: "ID of the associated report set",
  })
  report_set_id: number;

  @ApiProperty({
    required: true,
    description: "ID of the associated report set fraction",
  })
  fraction_id: number;

  @ApiProperty({
    required: true,
    description: "Code of the associated report set fraction",
  })
  fraction_code: string;

  @ApiProperty({
    required: true,
    description: "Title of the report set price list",
  })
  title: string;

  @ApiProperty({
    required: true,
    description: "License year of the report set price list",
  })
  license_year: number;

  @ApiProperty({
    required: true,
    description: "Start date of the report set price list",
  })
  start_date: Date;

  @ApiProperty({
    required: true,
    description: "End date of the report set price list",
  })
  end_date: Date;

  @ApiProperty({
    required: false,
    description: "Type of the report set price list",
  })
  type: ReportSetPriceListType;

  @ApiProperty({
    required: false,
    description: "Fixed price of the report set price list",
  })
  fixed_price: number;

  @ApiProperty({
    required: false,
    description: "Base price of the report set price list",
  })
  base_price: number;

  @ApiProperty({
    required: false,
    description: "Minimum fee of the report set price list",
  })
  minimum_fee: number;
}
