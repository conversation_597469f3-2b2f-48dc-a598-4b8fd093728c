import { Test, TestingModule } from "@nestjs/testing";
import { LicenseRepresentativeTierService } from "./license-representative-tier.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { CreateLicenseRepresentativeTierDto } from "./dto/create-license-representative-tier.dto";
import { UpdateLicenseRepresentativeTierDto } from "./dto/update-license-representative-tier.dto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

describe("LicenseRepresentativeTierService", () => {
  let service: LicenseRepresentativeTierService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    licenseRepresentativeTier: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicenseRepresentativeTierService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<LicenseRepresentativeTierService>(LicenseRepresentativeTierService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all license representative tiers when no license_id is provided", async () => {
      const mockItems = [
        { id: 1, name: "Premium Tier", price: 1000, deleted_at: null },
        { id: 2, name: "Standard Tier", price: 500, deleted_at: null },
      ];
      mockDatabaseService.licenseRepresentativeTier.findMany.mockResolvedValue(mockItems);

      const result = await service.findAll();

      expect(result).toEqual(mockItems);
      expect(mockDatabaseService.licenseRepresentativeTier.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
        },
        include: {
          license: true,
        },
        orderBy: {
          id: "desc",
        },
      });
    });

    it("should filter by license_id when provided", async () => {
      const license_id = 123;
      const mockItems = [{ id: 1, name: "Premium Tier", price: 1000, license_id: 123, deleted_at: null }];
      mockDatabaseService.licenseRepresentativeTier.findMany.mockResolvedValue(mockItems);

      const result = await service.findAll(license_id);

      expect(result).toEqual(mockItems);
      expect(mockDatabaseService.licenseRepresentativeTier.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
          license_id: 123,
        },
        include: {
          license: true,
        },
        orderBy: {
          id: "desc",
        },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException when id is invalid", async () => {
      await expect(service.findOne(NaN, user)).rejects.toThrow(BadRequestException);
      await expect(service.findOne(null, user)).rejects.toThrow(BadRequestException);
      expect(mockDatabaseService.licenseRepresentativeTier.findUnique).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license representative tier is not found", async () => {
      const id = 999;
      mockDatabaseService.licenseRepresentativeTier.findUnique.mockResolvedValue(null);

      await expect(service.findOne(id, user)).rejects.toThrow(NotFoundException);
      expect(mockDatabaseService.licenseRepresentativeTier.findUnique).toHaveBeenCalledWith({
        where: { id: 999, deleted_at: null },
        include: {
          license: {
            include: {
              contract: {
                include: {
                  customer: true,
                },
              },
            },
          },
        },
      });
    });
  });

  describe("create", () => {
    it("should create and return a new license representative tier", async () => {
      const createDto: CreateLicenseRepresentativeTierDto = {
        license_id: 1,
        setup_representative_tier_id: 2,
        name: "Premium Tier",
        price: 1000,
      };

      const mockCreatedItem = {
        id: 1,
        ...createDto,
        created_at: new Date(),
        updated_at: new Date(),
        deleted_at: null,
        license: {
          id: 1,
          contract: {
            id: 1,
            customer: {
              id: 1,
            },
          },
        },
      };

      mockDatabaseService.licenseRepresentativeTier.create.mockResolvedValue(mockCreatedItem);

      const result = await service.create(createDto);

      expect(result).toEqual(mockCreatedItem);
      expect(mockDatabaseService.licenseRepresentativeTier.create).toHaveBeenCalledWith({
        data: {
          license_id: createDto.license_id,
          setup_representative_tier_id: createDto.setup_representative_tier_id,
          name: createDto.name,
          price: createDto.price,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
        include: {
          license: {
            include: {
              contract: {
                include: {
                  customer: true,
                },
              },
            },
          },
        },
      });
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException when id is invalid", async () => {
      const updateDto: UpdateLicenseRepresentativeTierDto = {
        name: "Updated Tier",
      };

      await expect(service.update(NaN, updateDto, user)).rejects.toThrow(BadRequestException);
      await expect(service.update(null, updateDto, user)).rejects.toThrow(BadRequestException);
      expect(mockDatabaseService.licenseRepresentativeTier.findUnique).not.toHaveBeenCalled();
      expect(mockDatabaseService.licenseRepresentativeTier.update).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license representative tier is not found", async () => {
      const id = 999;
      const updateDto: UpdateLicenseRepresentativeTierDto = {
        name: "Updated Tier",
      };

      mockDatabaseService.licenseRepresentativeTier.findUnique.mockResolvedValue(null);

      await expect(service.update(id, updateDto, user)).rejects.toThrow(NotFoundException);
      expect(mockDatabaseService.licenseRepresentativeTier.findUnique).toHaveBeenCalledWith({
        include: {
          license: {
            include: {
              contract: {
                include: {
                  customer: true,
                },
              },
            },
          },
        },
        where: { id: 999, deleted_at: null },
      });
      expect(mockDatabaseService.licenseRepresentativeTier.update).not.toHaveBeenCalled();
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should soft delete the license representative tier", async () => {
      const id = 1;
      const mockDeletedItem = {
        id: 1,
        deleted_at: new Date(),
      };

      mockDatabaseService.licenseRepresentativeTier.update.mockResolvedValue(mockDeletedItem);

      jest.spyOn(service, "validatingUserPermissionLicenseRepresentativeTier").mockResolvedValue(undefined);

      const result = await service.remove(id, user);

      expect(result).toEqual(mockDeletedItem);
      expect(mockDatabaseService.licenseRepresentativeTier.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          deleted_at: expect.any(Date),
        },
      });
    });
  });
});
