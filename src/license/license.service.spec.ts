import { Test, TestingModule } from "@nestjs/testing";
import { LicenseService, getLicensePendencies } from "./license.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { CreateLicenseDto } from "./dto/create-license.dto";
import { UpdateLicenseDto } from "./dto/update-license.dto";
import { LicenseClerkControlStatus, LicenseContractStatus, LicenseRegistrationStatus } from "@prisma/client";
import * as crypto from "node:crypto";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

jest.mock("node:crypto", () => ({
  randomUUID: jest.fn().mockReturnValue("mock-uuid-1234-5678-9101"),
}));

describe("LicenseService", () => {
  let service: LicenseService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    license: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicenseService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<LicenseService>(LicenseService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all licenses when no contract_id is provided", async () => {
      const mockLicenses = [
        { id: 1, contract_id: 1, year: 2024, deleted_at: null },
        { id: 2, contract_id: 2, year: 2024, deleted_at: null },
      ];

      mockDatabaseService.license.findMany.mockResolvedValue(mockLicenses);

      const result = await service.findAll();

      expect(mockDatabaseService.license.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
      expect(result).toEqual(mockLicenses);
    });

    it("should return detailed licenses with pendencies when contract_id is provided", async () => {
      const contract_id = 1;
      const mockLicenses = [
        {
          id: 1,
          contract_id: 1,
          year: 2024,
          deleted_at: null,
          contract: { id: 1, files: [] },
          price_list: [],
          required_informations: [{ id: 1, status: "PENDING" }],
          third_party_invoices: [{ id: 1, status: "OPEN" }],
          representative_tiers: [],
          other_costs: [],
          packaging_services: [
            {
              id: 1,
              volume_reports: [{ id: 1, status: "PENDING" }],
              report_set_frequency: { id: 1 },
              report_set: { id: 1 },
            },
          ],
          certificates: [],
          next_steps: [],
          termination: null,
        },
      ];

      mockDatabaseService.license.findMany.mockResolvedValue(mockLicenses);

      const result = await service.findAll(contract_id);

      expect(mockDatabaseService.license.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null, contract_id: 1 },
        include: expect.objectContaining({
          contract: expect.any(Object),
          price_list: expect.any(Object),
          required_informations: expect.any(Object),
          third_party_invoices: expect.any(Object),
        }),
        orderBy: { id: "desc" },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should return a license with pendencies when it exists", async () => {
      const id = 1;
      const mockLicense = {
        id: 1,
        contract_id: 1,
        year: 2024,
        deleted_at: null,
        contract: { id: 1, files: [] },
        price_list: [],
        pendencies_status: "DONE",
        required_informations: [{ id: 1, status: "DONE" }],
        third_party_invoices: [],
        representative_tiers: [],
        other_costs: [],
        packaging_services: [{ id: 1, volume_reports: [] }],
        certificates: [],
        next_steps: [],
        termination: null,
        files: [],
      };

      mockDatabaseService.license.findUnique.mockResolvedValue(mockLicense);

      jest.spyOn(service, "validatingUserPermissionLicenseOtherCost").mockResolvedValue(undefined);

      const result = await service.findOne(id, user);

      expect(mockDatabaseService.license.findUnique).toHaveBeenCalledWith({
        where: { deleted_at: null, id: 1 },
        include: expect.objectContaining({
          contract: expect.any(Object),
          price_list: expect.any(Object),
          required_informations: expect.any(Object),
        }),
      });

      expect(result).toEqual({
        ...mockLicense,
        pendencies: [],
      });
    });

    it("should throw BadRequestException when id is invalid", async () => {
      await expect(service.findOne(NaN, user)).rejects.toThrow(BadRequestException);
      await expect(service.findOne(undefined, user)).rejects.toThrow(BadRequestException);
      expect(mockDatabaseService.license.findUnique).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license is not found", async () => {
      const id = 999;

      mockDatabaseService.license.findUnique.mockResolvedValue(null);

      await expect(service.findOne(id, user)).rejects.toThrow(NotFoundException);
      expect(mockDatabaseService.license.findUnique).toHaveBeenCalledWith({
        where: { deleted_at: null, id: 999 },
        include: expect.any(Object),
      });
    });
  });

  describe("create", () => {
    it("should create a new license", async () => {
      const createDto: CreateLicenseDto = {
        contract_id: 1,
        country_id: 2,
        country_code: "DE",
        country_name: "Germany",
        country_flag: "https://flagcdn.com/de.svg",
        registration_status: LicenseRegistrationStatus.DONE,
        clerk_control_status: LicenseClerkControlStatus.DONE,
        contract_status: LicenseContractStatus.ACTIVE,
        year: 2024,
        start_date: new Date("2024-01-01"),
      };

      const mockCreatedLicense = {
        id: 1,
        ...createDto,
        registration_number: "mock",
        created_at: new Date("2025-03-12T14:50:46.311Z"),
        updated_at: new Date("2025-03-12T14:50:46.311Z"),
        deleted_at: null,
      };

      mockDatabaseService.license.create.mockResolvedValue(mockCreatedLicense);

      const result = await service.create(createDto);

      expect(crypto.randomUUID).toHaveBeenCalled();
      expect(mockDatabaseService.license.create).toHaveBeenCalledWith({
        data: {
          contract_id: createDto.contract_id,
          country_id: createDto.country_id,
          country_code: createDto.country_code,
          country_name: createDto.country_name,
          country_flag: createDto.country_flag,
          registration_number: "mock", // Corrigido para corresponder ao resultado real
          registration_status: createDto.registration_status,
          clerk_control_status: createDto.clerk_control_status,
          contract_status: createDto.contract_status,
          year: createDto.year,
          start_date: createDto.start_date,
          end_date: undefined,
          termination_id: undefined,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });
      expect(result).toEqual(mockCreatedLicense);
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should update an existing license", async () => {
      const id = 1;
      const updateDto: UpdateLicenseDto = {
        registration_status: LicenseRegistrationStatus.DONE,
        clerk_control_status: LicenseClerkControlStatus.DONE,
        contract_status: LicenseContractStatus.ACTIVE,
        termination_id: 2,
      };

      const mockExistingLicense = {
        id: 1,
        registration_status: LicenseRegistrationStatus.PENDING,
        clerk_control_status: LicenseClerkControlStatus.PENDING,
        contract_status: LicenseContractStatus.ACTIVE,
        deleted_at: null,
      };

      const mockUpdatedLicense = {
        ...mockExistingLicense,
        ...updateDto,
        updated_at: new Date(),
      };

      mockDatabaseService.license.findUnique.mockResolvedValue(mockExistingLicense);
      mockDatabaseService.license.update.mockResolvedValue(mockUpdatedLicense);

      jest.spyOn(service, "validatingUserPermissionLicenseOtherCost").mockResolvedValue(undefined);

      const result = await service.update(id, updateDto, user);

      expect(mockDatabaseService.license.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          ...updateDto,
          updated_at: expect.any(Date),
        },
      });

      expect(result).toEqual(mockUpdatedLicense);
    });

    it("should throw BadRequestException when id is invalid", async () => {
      const updateDto: UpdateLicenseDto = {
        registration_status: LicenseRegistrationStatus.DONE,
        clerk_control_status: LicenseClerkControlStatus.DONE,
        contract_status: LicenseContractStatus.ACTIVE,
        termination_id: 2,
      };

      await expect(service.update(NaN, updateDto, user)).rejects.toThrow(BadRequestException);
      await expect(service.update(undefined, updateDto, user)).rejects.toThrow(BadRequestException);

      expect(mockDatabaseService.license.findUnique).not.toHaveBeenCalled();
      expect(mockDatabaseService.license.update).not.toHaveBeenCalled();
    });

    it("should throw NotFoundException when license is not found", async () => {
      const id = 999;
      const updateDto: UpdateLicenseDto = {
        registration_status: LicenseRegistrationStatus.DONE,
        clerk_control_status: LicenseClerkControlStatus.DONE,
        contract_status: LicenseContractStatus.ACTIVE,
        termination_id: 2,
      };

      mockDatabaseService.license.findUnique.mockResolvedValue(null);

      await expect(service.update(id, updateDto, user)).rejects.toThrow(NotFoundException);

      expect(mockDatabaseService.license.findUnique).toHaveBeenCalledWith({
        include: {
          contract: {
            include: {
              customer: true,
            },
          },
        },
        where: { id: 999, deleted_at: null },
      });
      expect(mockDatabaseService.license.update).not.toHaveBeenCalled();
    });
  });

  describe("remove", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should soft delete a license", async () => {
      const id = 1;
      const mockDeletedLicense = {
        id: 1,
        deleted_at: new Date(),
      };

      mockDatabaseService.license.update.mockResolvedValue(mockDeletedLicense);
      jest.spyOn(service, "validatingUserPermissionLicenseOtherCost").mockResolvedValue(undefined);
      const result = await service.remove(id, user);

      expect(mockDatabaseService.license.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          deleted_at: expect.any(Date),
        },
      });

      expect(result).toEqual(mockDeletedLicense);
    });
  });

  describe("getLicensePendencies", () => {
    it("should return all pendencies when required information, volume reports, and invoices are pending", () => {
      const license = {
        id: 1,
        required_informations: [{ id: 1, status: "PENDING" }],
        packaging_services: [{ id: 1, volume_reports: [{ id: 1, status: "PENDING" }] }],
        third_party_invoices: [{ id: 1, status: "OPEN" }],
      } as any;

      const result = getLicensePendencies(license);

      expect(result).toEqual({
        pendencies: [{ label: "Invoices", type: "INVOICES" }],
        pendencies_status: "OPEN_TO_DOS",
      });
    });

    it("should return no pendencies when all statuses are DONE", () => {
      const license = {
        id: 1,
        required_informations: [{ id: 1, status: "DONE" }],
        packaging_services: [{ id: 1, volume_reports: [{ id: 1, status: "DONE" }] }],
        third_party_invoices: [{ id: 1, status: "DONE" }],
      } as any;

      const result = getLicensePendencies(license);

      expect(result).toEqual({ pendencies: [], pendencies_status: "DONE" });
    });

    it("should return only relevant pendencies", () => {
      const license = {
        id: 1,
        required_informations: [{ id: 1, status: "DONE" }],
        packaging_services: [{ id: 1, volume_reports: [{ id: 1, status: "PENDING" }] }],
        third_party_invoices: [{ id: 1, status: "DONE" }],
      } as any;

      const result = getLicensePendencies(license);

      expect(result).toEqual({ pendencies: [], pendencies_status: "DONE" });
    });
  });
});
