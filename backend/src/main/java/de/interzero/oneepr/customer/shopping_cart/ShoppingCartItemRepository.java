package de.interzero.oneepr.customer.shopping_cart;

import de.interzero.oneepr.customer.contract.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ShoppingCartItemRepository extends JpaRepository<ShoppingCartItem, Integer> {

    /**
     * <p>Deletes all shopping cart items associated with the specified cart ID.</p>
     *
     * @param cartId the ID of the shopping cart whose items should be deleted
     */
    void deleteByShoppingCart_Id(String cartId);

    /**
     * <p>Finds all non-deleted items for the given shopping cart ID.</p>
     *
     * @param shoppingCartId the ID of the shopping cart
     * @return list of non-deleted {@link ShoppingCartItem}
     */
    List<ShoppingCartItem> findAllByShoppingCartIdAndDeletedAtIsNull(String shoppingCartId);

    /**
     * <p>Deletes all items by shopping cart ID.</p>
     *
     * @param shoppingCartId the ID of the shopping cart
     */
    void deleteByShoppingCartId(String shoppingCartId);

    /**
     * <p>Deletes shopping cart items by their IDs and shopping cart ID.</p>
     *
     * @param ids            the list of item IDs to delete
     * @param shoppingCartId the ID of the shopping cart
     */
    void deleteByIdInAndShoppingCartId(List<Integer> ids,
                                       String shoppingCartId);

    /**
     * <p>Finds the first shopping cart item by cart ID, year, service type, and country code.</p>
     *
     * @param shoppingCartId ID of the shopping cart
     * @param year           Year of the item
     * @param serviceType    Contract Type of service
     * @param countryCode    Country code
     * @return Optional containing the first matching ShoppingCartItem if exists, otherwise empty
     */
    Optional<ShoppingCartItem> findFirstByShoppingCartIdAndYearAndServiceTypeAndCountryCode(String shoppingCartId,
                                                                                            Integer year,
                                                                                            Contract.Type serviceType,
                                                                                            String countryCode);

    /**
     * <p>Fetch one item by ID and its parent cart by cart ID;</p>
     *
     * @param itemId         shopping cart item ID
     * @param shoppingCartId shopping cart ID
     * @return optional item with its cart and cart.customerCommitments (lazy collections may still initialize later)
     */
    Optional<ShoppingCartItem> findByIdAndShoppingCartId(Integer itemId,
                                                         String shoppingCartId);

    /**
     * <p>Find a cart item by ID with its parent cart.</p>
     *
     * @param itemId         the shopping cart item ID
     * @param shoppingCartId the parent shopping cart ID
     * @return optional item with joined cart
     */
    Optional<ShoppingCartItem> findByIdAndShoppingCartIdAndShoppingCartDeletedAtIsNull(Integer itemId,
                                                                                       String shoppingCartId);

    /**
     * <p>Delete a cart item by ID constrained to a specific cart.</p>
     *
     * @param itemId         the shopping cart item ID
     * @param shoppingCartId the parent shopping cart ID
     */
    @Modifying
    @Query("DELETE FROM ShoppingCartItem i WHERE i.id = :itemId AND i.shoppingCart.id = :shoppingCartId")
    void deleteByIdAndShoppingCartId(@Param("itemId") Integer itemId,
                                     @Param("shoppingCartId") String shoppingCartId);

}
