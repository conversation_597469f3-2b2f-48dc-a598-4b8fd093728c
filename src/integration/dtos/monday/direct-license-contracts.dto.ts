import { ApiProperty } from "@nestjs/swagger";

export class DirectLicenseContractsDto {
  @ApiProperty()
  contractVolumeValue: number;

  @ApiProperty()
  status?: string;

  @ApiProperty()
  activeYear: string;

  @ApiProperty()
  serviceType: string;

  @ApiProperty()
  startingYear: string;

  @ApiProperty()
  endingYear: string;

  @ApiProperty()
  terminationDate: string;

  @ApiProperty()
  customerId: string;

  @ApiProperty()
  country?: string;
}
