package de.interzero.oneepr.customer.termination;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.termination.dto.CreateTerminationDto;
import de.interzero.oneepr.customer.termination.dto.UpdateTerminationDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static de.interzero.oneepr.common.string.Role.*;

/**
 * REST controller for managing contract terminations.
 */
@RestController
@RequestMapping(Api.TERMINATION)
@Tag(name = "terminations")
@RequiredArgsConstructor
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
public class TerminationController {

    private final TerminationService terminationService;

    /**
     * Retrieves a specific termination by its ID.
     *
     * @param id The ID of the termination to retrieve.
     * @return The requested {@link Termination} object.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get a termination by ID")
    public ResponseEntity<Termination> findById(@PathVariable Integer id) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        Termination termination = terminationService.findById(id, user);
        return ResponseEntity.ok(termination);
    }

    /**
     * Creates a new termination request.
     *
     * @param createDto DTO containing the data for the new termination.
     * @return A map containing a success message.
     */
    @PostMapping
    @Operation(summary = "Create a termination")
    public ResponseEntity<Map<String, String>> create(@Valid @RequestBody CreateTerminationDto createDto) {
        Map<String, String> response = terminationService.create(createDto);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    /**
     * Updates an existing termination.
     *
     * @param id        The ID of the termination to update.
     * @param updateDto DTO containing the fields to update.
     * @return A map containing a success message.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update a termination")
    public ResponseEntity<Map<String, String>> update(@PathVariable Integer id,
                                                      @Valid @RequestBody UpdateTerminationDto updateDto) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        Map<String, String> response = terminationService.update(id, updateDto, user);
        return ResponseEntity.ok(response);
    }

    /**
     * Revokes a termination, reverting its status and the status of related entities.
     *
     * @param id The ID of the termination to revoke.
     * @return A map containing a success message.
     */
    @PostMapping("/{id}/revoke")
    @Operation(summary = "Revoke a termination")
    public ResponseEntity<Map<String, String>> revoke(@PathVariable Integer id) {
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        Map<String, String> response = terminationService.revoke(id, user);
        return ResponseEntity.ok(response);
    }
}
