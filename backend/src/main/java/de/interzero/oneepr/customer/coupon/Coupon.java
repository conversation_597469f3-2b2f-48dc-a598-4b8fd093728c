package de.interzero.oneepr.customer.coupon;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.commission.Commission;
import de.interzero.oneepr.customer.entity.CouponCustomer;
import de.interzero.oneepr.customer.entity.CouponPartner;
import de.interzero.oneepr.customer.purchase.CouponUses;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(
        name = "coupon",
        schema = "public"
)
public class Coupon {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "coupon_id_gen"
    )
    @SequenceGenerator(
            name = "coupon_id_gen",
            sequenceName = "coupon_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @Column(name = "buy_x_get_y")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("buy_x_get_y")
    private Map<String, Object> buyXGetY;

    @NotNull
    @Column(
            name = "code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("code")
    private String code;

    @Column(name = "commission_percentage")
    @JsonProperty("commission_percentage")
    private Integer commissionPercentage;

    @Column(
            name = "description",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "discount_type")
    @JsonProperty("discount_type")
    private DiscountType discountType;

    @Column(name = "elegible_products")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("elegible_products")
    private Map<String, Object> elegibleProducts;

    @NotNull
    @Column(
            name = "end_date",
            nullable = false
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @NotNull
    @Column(
            name = "is_active",
            nullable = false
    )
    @JsonProperty("is_active")
    private Boolean isActive = true;

    @Column(
            name = "link",
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("link")
    private String link;

    @Column(name = "max_amount")
    @JsonProperty("max_amount")
    private Integer maxAmount;

    @Column(name = "max_uses")
    @JsonProperty("max_uses")
    private Integer maxUses;

    @Column(name = "max_uses_per_customer")
    @JsonProperty("max_uses_per_customer")
    private Integer maxUsesPerCustomer;

    @Column(name = "min_amount")
    @JsonProperty("min_amount")
    private Integer minAmount;

    @Column(name = "min_products")
    @JsonProperty("min_products")
    private Integer minProducts;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "mode")
    @JsonProperty("mode")
    private Mode mode;

    @Column(
            name = "note",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("note")
    private String note;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "type")
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Column(
            name = "value",
            nullable = false
    )
    @JsonProperty("value")
    private Integer value;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @NotNull
    @Column(
            name = "redeemable_by_new_customers",
            nullable = false
    )
    @JsonProperty("redeemable_by_new_customers")
    private Boolean redeemableByNewCustomers = false;

    @NotNull
    @Column(
            name = "for_commission",
            nullable = false
    )
    @JsonProperty("for_commission")
    private Boolean forCommission = false;

    @NotNull
    @Column(
            name = "used_at",
            nullable = false
    )
    @JsonProperty("used_at")
    private Instant usedAt;

    @JsonIgnore
    @JsonProperty("commissions")
    @OneToMany(
            mappedBy = "coupon",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private Set<Commission> commissions;

    @JsonIgnore
    @JsonProperty("customers")
    @OneToMany(
            mappedBy = "coupon",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    private Set<CouponCustomer> customers;

    @JsonIgnore
    @JsonProperty("partners")
    @OneToMany(
            mappedBy = "coupon",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private Set<CouponPartner> partners;

    @JsonIgnore
    @JsonProperty("coupon_uses")
    @OneToMany(
            mappedBy = "coupon",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private Set<CouponUses> couponUses;

    @JsonIgnore
    @JsonProperty("shopping_carts")
    @OneToMany(
            mappedBy = "coupon",
            fetch = FetchType.LAZY,
            cascade = CascadeType.PERSIST
    )
    private Set<ShoppingCart> shoppingCarts;

    /**
     * Update coupons auditing fields.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    /**
     * Update coupons auditing fields on update.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum DiscountType {
        PERCENTAGE,
        ABSOLUTE,
        BUY_X_PRODUCTS_GET_Y_PRODUCTS,
        BUY_X_PRODUCTS_GET_Y_DISCOUNT
    }

    public enum Mode {
        GENERAL,
        INDIVIDUAL,
        GROUP_SEGMENT
    }

    public enum Type {
        SYSTEM,
        CUSTOMER
    }
}