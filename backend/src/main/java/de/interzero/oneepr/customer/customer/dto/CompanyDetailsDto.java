package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing the detailed view of a company.
 * <p>
 * This DTO is a direct translation of the {@code Company} model and its nested
 * relations as defined in the {@code companies} include block of the
 * {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CompanyDetailsDto {

    @Schema(description = "Unique identifier of the company.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "Name of the company.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "Description of the company.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "VAT identification number.")
    @JsonProperty("vat")
    private String vat;

    @Schema(description = "Tax identification number.")
    @JsonProperty("tin")
    private String tin;

    @Schema(description = "LUCID registration number.")
    @JsonProperty("lucid")
    private String lucid;

    @Schema(description = "Company start date.")
    @JsonProperty("starting")
    private Instant starting;

    @Schema(description = "Company website URL.")
    @JsonProperty("website")
    private String website;

    @Schema(description = "The company's industry sector.")
    @JsonProperty("industry_sector")
    private String industrySector;

    @Schema(description = "Name of the company owner.")
    @JsonProperty("owner_name")
    private String ownerName;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The company's primary address.")
    @JsonProperty("address")
    private CompanyAddressDto address;

    @Schema(description = "A list of emails associated with the company.")
    @JsonProperty("emails")
    private List<CompanyEmailDto> emails;

    @Schema(description = "The company's billing information.")
    @JsonProperty("billing")
    private CompanyBillingDto billing;
}