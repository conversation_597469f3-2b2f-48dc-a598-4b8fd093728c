import { ApiProperty } from "@nestjs/swagger";
import { TerminationStatus } from "@prisma/client";

export class CreateTerminationDto {
  @ApiProperty({
    description: "The contract ID",
    example: 1,
  })
  contract_id: number;

  @ApiProperty({
    description: "The termination status",
    example: TerminationStatus.PENDING,
  })
  status?: TerminationStatus;

  @ApiProperty({
    description: "The country codes",
    example: ["FR", "IT", "ES"],
  })
  country_codes?: string[];

  @ApiProperty({
    description: "The termination reason",
    example: [1, 2, 3],
  })
  reason_ids: number[];

  @ApiProperty({
    description: "The termination file ID",
    example: 1,
  })
  termination_file_id?: string;

  @ApiProperty({
    description: "The proof of termination file ID",
    example: 1,
  })
  proof_of_termination_file_id?: string;
}
