package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Create License Packaging Service DTO
 * Converted from TypeScript create-license-packaging-service.dto.ts
 * Maintains exact same structure, variable names, and property names.
 */
@Data
public class CreateLicensePackagingServiceDto {

    /**
     * The license ID
     * Equivalent to TypeScript: license_id: number;
     */
    @JsonProperty("license_id")
    @Schema(
            description = "The license ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer licenseId;

    /**
     * The setup packaging service ID
     * Equivalent to TypeScript: setup_packaging_service_id: number;
     */
    @JsonProperty("setup_packaging_service_id")
    @Schema(
            description = "The setup packaging service ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer setupPackagingServiceId;

    /**
     * The name of the packaging service
     * Equivalent to TypeScript: name: string;
     */
    @JsonProperty("name")
    @Schema(
            description = "The name of the packaging service",
            example = "Standard Packaging",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    /**
     * The description of the packaging service
     * Equivalent to TypeScript: description: string;
     */
    @JsonProperty("description")
    @Schema(
            description = "The description of the packaging service",
            example = "Basic packaging service with standard features",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String description;
}
