"use client";

import { createColumnHelper } from "@tanstack/react-table";
import { useEffect, useState } from "react";

import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { Elipse } from "@interzero/oneepr-react-ui/Icon";

interface CompanieProfileData {
  id: string;
  companyName: string;
  customerNumber: string;
  transferDate: string;
  orderNumber: string;
  registerNumber: string;
  year: string;

  glass: string;
  ppk: string;
  fe: string;
  alu: string;
  cb: string;
  sw: string;
  plstc: string;
  sm: string;
  statusCode: { status: string; dateCancelled?: string };
  certificate: string;
}

const mockData: CompanieProfileData[] = [
  {
    id: "1",
    companyName: "Lauter & Co.",
    customerNumber: "C-100021",
    transferDate: "25.02.2023",
    orderNumber: "O-1000365",
    registerNumber: "DE1714028249764",
    year: "2023",

    glass: "56kg",
    ppk: "56kg",
    fe: "56kg",
    alu: "56kg",
    cb: "56kg",
    sw: "56kg",
    plstc: "56kg",
    sm: "56kg",
    statusCode: { status: "Open" },
    certificate: "Valid",
  },
  {
    id: "2",
    companyName: "Lauter & Co.",
    customerNumber: "C-100021",
    transferDate: "25.02.2023",
    orderNumber: "O-1000365",
    registerNumber: "DE1714028249764",
    year: "2023",

    glass: "56kg",
    ppk: "56kg",
    fe: "56kg",
    alu: "56kg",
    cb: "56kg",
    sw: "56kg",
    plstc: "56kg",
    sm: "56kg",
    statusCode: { status: "Open" },
    certificate: "Valid",
  },
  {
    id: "3",
    companyName: "Lauter & Co.",
    customerNumber: "C-100021",
    transferDate: "25.02.2023",
    orderNumber: "O-1000365",
    registerNumber: "DE1714028249764",
    year: "2023",

    glass: "56kg",
    ppk: "56kg",
    fe: "56kg",
    alu: "56kg",
    cb: "56kg",
    sw: "56kg",
    plstc: "56kg",
    sm: "56kg",
    statusCode: { status: "Open" },
    certificate: "Valid",
  },
  {
    id: "4",
    companyName: "Lauter & Co.",
    customerNumber: "C-100021",
    transferDate: "25.02.2023",
    orderNumber: "O-1000365",
    registerNumber: "DE1714028249764",
    year: "2023",

    glass: "56kg",
    ppk: "56kg",
    fe: "56kg",
    alu: "56kg",
    cb: "56kg",
    sw: "56kg",
    plstc: "56kg",
    sm: "56kg",
    statusCode: { status: "Open" },
    certificate: "---",
  },
  {
    id: "5",
    companyName: "Lauter & Co.",
    customerNumber: "C-100021",
    transferDate: "25.02.2023",
    orderNumber: "O-1000365",
    registerNumber: "DE1714028249764",
    year: "2023",

    glass: "56 kg",
    ppk: "56 kg",
    fe: "56 kg",
    alu: "56 kg",
    cb: "56 kg",
    sw: "56 kg",
    plstc: "56 kg",
    sm: "56 kg",
    statusCode: { status: "Cancelled", dateCancelled: "25.02.2023" },
    certificate: "---",
  },
];

const columnHelper = createColumnHelper<CompanieProfileData>();

export function CompanyProfileTable() {
  const [data] = useState<CompanieProfileData[]>(mockData);
  const [filteredData, setFilteredData] = useState<CompanieProfileData[]>(data);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    let filtered = [...data];

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter((item) => item.companyName.toLowerCase().includes(term));
    }

    setFilteredData(filtered);
  }, [data, searchTerm]);

  const columns = [
    columnHelper.accessor("customerNumber", {
      header: "Customer. no.",
      cell: (info) => {
        return (
          <div className="w-[120px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("companyName", {
      header: "Company Name",
      cell: (info) => {
        return (
          <div className="w-[140px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("transferDate", {
      header: "Transfer date",
      cell: (info) => {
        return (
          <div className="w-[100px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("orderNumber", {
      header: "Order no.",
      cell: (info) => {
        return (
          <div className="w-[100px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("registerNumber", {
      header: "Register no.",
      cell: (info) => {
        return (
          <div className="w-[140px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("year", {
      header: "Year",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("glass", {
      header: "Glass",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("ppk", {
      header: "PPK",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("fe", {
      header: "Fe",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("alu", {
      header: "Alu",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("cb", {
      header: "Cb",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("sw", {
      header: "Sw",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("plstc", {
      header: "Plstc",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("sm", {
      header: "Sm",
      cell: (info) => {
        return (
          <div className="w-[80px]">
            <span className="text-primary">{info.getValue()}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor("statusCode", {
      header: "Status Code",
      cell: (info) => {
        const status = info.getValue().status;
        const dateCancelled = info.getValue().dateCancelled;
        const isCancelled = status === "Cancelled";
        const color = isCancelled ? "#CD2C19" : "#339933";

        return (
          <div className="w-[100px] flex flex-col gap-1 items-start">
            <div className="flex gap-1 items-center">
              <Elipse style={{ fill: color }} className="size-3" />
              <span style={{ color }} className="font-bold">
                {status}
              </span>
            </div>
            {isCancelled && <span className="text-tonal-dark-cream-40 text-xs">on {dateCancelled}</span>}
          </div>
        );
      },
    }),
    columnHelper.accessor("certificate", {
      header: "Certificate",
      cell: (info) => {
        const status = info.getValue();
        const isValid = status === "Valid";
        return (
          <div className="w-[100px]">
            {isValid ? (
              <span className="text-success font-bold">{info.getValue()}</span>
            ) : (
              <span className="text-primary font-bold">{info.getValue()}</span>
            )}
          </div>
        );
      },
    }),
  ];

  return (
    <div className="flex flex-col gap-10 bg-cream rounded-3xl">
      <p className="text-title-3 text-primary font-bold">Orders</p>
      <div className="flex items-center justify-between gap-4">
        <div className="relative flex-1 max-w-[320px]">
          <DatatableSearch onSearch={setSearchTerm} defaultValue={searchTerm} placeholder="Search by name" />
        </div>
        <p className="text-paragraph-regular text-tonal-dark-cream-40">{filteredData.length} results</p>
      </div>
      <div className="pb-8">
        <div className="bg-gray-50 rounded-xl overflow-hidden">
          <Datatable columns={columns} data={filteredData} />
        </div>
      </div>
    </div>
  );
}
