# Customer.io Email Gateway Configuration

## Overview
The CustomerIoEmailGateway supports configurable environment variables for better flexibility across different environments. By default, the application uses a **sandbox** email gateway for safety, requiring explicit configuration to enable Customer.io in production.

## Gateway Selection Strategy

### Default Configuration (Sandbox)
By default, the application uses the **sandbox** email gateway for safety:
```properties
mail.gateway=sandbox
```

This ensures that:
- No real emails are sent accidentally during development
- Testing can be done without Customer.io credentials
- Production email sending must be explicitly enabled

### Production Configuration (Customer.io)
To enable Customer.io in production, you must explicitly set the gateway via environment variables:

```bash
# Environment variables for production
MAIL_GATEWAY=customerio
CUSTOMERIO_API_KEY=production-api-key
CUSTOMERIO_REGION=eu  # Optional, defaults to 'eu'
```

## Region-Specific API Endpoints

The gateway automatically selects the correct API endpoint based on the configured region:

- **EU Region**: `https://api-eu.customer.io/v1/send/email`
- **US Region**: `https://api.customer.io/v1/send/email`

## Configuration Examples

### Application Properties with Environment Variable Support
```properties
# Default to sandbox, can be overridden by environment variable
mail.gateway=${MAIL_GATEWAY:sandbox}

# Customer.io configuration (only used when mail.gateway=customerio)
customerio.api-key=${CUSTOMERIO_API_KEY}
customerio.site-id=${CUSTOMERIO_SITE_ID:}
customerio.region=${CUSTOMERIO_REGION:eu}
```

### Local Development
```properties
# application-local.properties
mail.gateway=sandbox  # Safe default for local development
```