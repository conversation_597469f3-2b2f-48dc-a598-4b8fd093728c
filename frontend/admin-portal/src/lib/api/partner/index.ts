import { customerApi } from "..";
import { ApiEndpoints } from "../endpoints";
import {
  CreatePartnerParams,
  GetDiscountLinksUsageResponse,
  GetLicensesPurchasedOvertimeResponse,
  GetPartnerCommissionsParams,
  GetPartnerCommissionsResponse,
  GetPartnerCouponsPerformanceResponse,
  GetPartnerGlobalStatisticsResponse,
  GetPartnersPaginatedResponse,
  GetRevenuePlusEarningsOfTopFivePartnersResponse,
  Partner,
  PartnerStatus,
  UpdatePartnerContractParams,
  UpdatePartnerParams,
} from "./types";

type GetPartnersParams = {
  name?: string;
};

export async function getPartners(params: GetPartnersParams = {}) {
  const response = await customerApi.get<Partner[]>(ApiEndpoints.partner.findAll, { params });
  return response.data;
}

type GetPartnersPaginatedParams = {
  page: number;
  limit: number;
  name?: string;
  start_date?: string;
  end_date?: string;
  status?: PartnerStatus;
};

export async function getPartnersPaginated(params: GetPartnersPaginatedParams) {
  const response = await customerApi.get<GetPartnersPaginatedResponse>(ApiEndpoints.partner.findAllPaginated, {
    params,
  });
  return response.data;
}

export async function createPartner(params: CreatePartnerParams) {
  const response = await customerApi.postForm(ApiEndpoints.partner.create, params);
  return response.data;
}

export async function getPartner(partnerId: number) {
  const response = await customerApi.get<Partner>(ApiEndpoints.partner.findOne(partnerId));
  return response.data;
}

export async function updatePartner(partnerId: number, params: UpdatePartnerParams) {
  const response = await customerApi.patchForm(ApiEndpoints.partner.update(partnerId), params);
  return response.data;
}

export async function updatePartnerContract(
  partnerId: number,
  contractId: number,
  params: UpdatePartnerContractParams
) {
  const response = await customerApi.patchForm(ApiEndpoints.partner.updateContract(partnerId, contractId), params);
  return response.data;
}

export async function getPartnerCommissions(partnerId: number, params?: GetPartnerCommissionsParams) {
  const response = await customerApi.get<GetPartnerCommissionsResponse>(
    ApiEndpoints.partner.getPartnerCommissions(partnerId),
    { params }
  );
  return response.data;
}

export async function getPartnerGlobalStatistics(partnerId: number, year?: string) {
  const response = await customerApi.get<GetPartnerGlobalStatisticsResponse>(
    ApiEndpoints.partner.getPartnerGlobalStatistics(partnerId),
    { params: { year } }
  );
  return response.data;
}

export async function getRevenuePlusEarningsOfTopFivePartners(year?: string) {
  const response = await customerApi.get<GetRevenuePlusEarningsOfTopFivePartnersResponse>(
    ApiEndpoints.partner.getRevenuePlusEarningsOfTopFivePartners,
    {
      params: { year },
    }
  );
  return response.data;
}

export async function getDiscountLinksUsage(year?: string) {
  const response = await customerApi.get<GetDiscountLinksUsageResponse>(ApiEndpoints.partner.getDiscountLinksUsage, {
    params: { year },
  });
  return response.data;
}

export async function getPartnerCouponsPerformance(partnerId: number, groupBy: "quarter" | "year") {
  const response = await customerApi.get<GetPartnerCouponsPerformanceResponse>(
    ApiEndpoints.partner.getPartnerCouponsPerformance(partnerId),
    { params: { groupBy } }
  );
  return response.data;
}

export async function getLicensesPurchasedOvertime(partnerId: number, year: string) {
  const response = await customerApi.get<GetLicensesPurchasedOvertimeResponse>(
    ApiEndpoints.partner.getPartnerLicensePurchases(partnerId),
    { params: { year } }
  );
  return response.data;
}
