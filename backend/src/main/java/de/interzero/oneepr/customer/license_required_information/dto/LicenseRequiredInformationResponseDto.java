package de.interzero.oneepr.customer.license_required_information.dto;

import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.reason.DeclineReason;
import de.interzero.oneepr.customer.reason.Reason;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * A specialized DTO for responding to requests for LicenseRequiredInformation.
 * It transforms the nested decline reasons into a simplified list of reasons.
 *
 * @ts-legacy This DTO translates the custom mapping logic from the original `findAll` and `findOne` methods.
 */
@Data
@NoArgsConstructor
public class LicenseRequiredInformationResponseDto {

    private Integer id;

    private String name;

    private String description;

    private Instant createdAt;

    private Instant updatedAt;

    private String question;

    private String answer;

    private LicenseRequiredInformation.Type type;

    private LicenseRequiredInformation.Status status;

    private LicenseRequiredInformation.Kind kind;

    private License license;

    private List<File> answerFiles;

    private DeclineDto decline;

    @Data
    @NoArgsConstructor
    public static class DeclineDto {

        private Integer id;

        private String title;

        private List<Reason> declineReasons;
    }

    /**
     * Factory method to create a response DTO from an entity.
     *
     * @param entity The source LicenseRequiredInformation entity.
     * @return A new response DTO with the transformed decline reasons.
     */
    public static LicenseRequiredInformationResponseDto fromEntity(LicenseRequiredInformation entity) {
        LicenseRequiredInformationResponseDto dto = new LicenseRequiredInformationResponseDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setQuestion(entity.getQuestion());
        dto.setAnswer(entity.getAnswer());
        dto.setType(entity.getType());
        dto.setStatus(entity.getStatus());
        dto.setKind(entity.getKind());
        dto.setLicense(entity.getLicense());
        dto.setAnswerFiles(entity.getAnswerFiles());

        if (entity.getDecline() != null) {
            DeclineDto declineDto = new DeclineDto();
            declineDto.setId(entity.getDecline().getId());
            declineDto.setTitle(entity.getDecline().getTitle());
            declineDto.setDeclineReasons(entity.getDecline()
                                                 .getDeclineReasons()
                                                 .stream()
                                                 .map(DeclineReason::getReason)
                                                 .toList());
            dto.setDecline(declineDto);
        }

        return dto;
    }
}
