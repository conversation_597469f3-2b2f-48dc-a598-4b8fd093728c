package de.interzero.oneepr.admin.service_setup.report_set_frequency;

import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.CreateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.UpdateReportSetFrequencyDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Report Set Frequency entities.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.REPORT_SET_FREQUENCIES)
@Tag(name = "ReportSetFrequency")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetFrequencyController {

    private final ReportSetFrequencyService reportSetFrequencyService;

    /**
     * Creates a new report set frequency.
     *
     * @param data DTO containing the details for the new frequency.
     * @return The newly created ReportSetFrequency entity.
     */
    @PostMapping
    @Operation(summary = "Create a new report set frequency")
    @ApiResponse(
            responseCode = "200",
            description = "Report set frequency created successfully",
            content = @Content(schema = @Schema(implementation = ReportSetFrequency.class))
    )
    public ReportSetFrequency create(@RequestBody CreateReportSetFrequencyDto data) {
        return this.reportSetFrequencyService.create(data);
    }

    /**
     * Retrieves all active report set frequencies.
     *
     * @return A list of ReportSetFrequency entities.
     */
    @GetMapping
    @Operation(summary = "Get all report set frequencies")
    @ApiResponse(
            responseCode = "200",
            description = "Report set frequencies retrieved successfully"
    )
    public List<ReportSetFrequency> findAll() {
        return this.reportSetFrequencyService.findAll();
    }

    /**
     * Retrieves a single report set frequency by its ID.
     *
     * @param id The string representation of the frequency's ID.
     * @return The found ReportSetFrequency entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set frequency by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set frequency retrieved successfully",
            content = @Content(schema = @Schema(implementation = ReportSetFrequency.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set frequency not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set frequency ID"
    )
    public ReportSetFrequency findOne(@PathVariable String id) {
        try {
            return this.reportSetFrequencyService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set frequency ID format");
        }
    }

    /**
     * Updates an existing report set frequency by its ID.
     *
     * @param id   The string representation of the frequency's ID.
     * @param data DTO containing the fields to update.
     * @return The updated ReportSetFrequency entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update report set frequency by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set frequency updated successfully",
            content = @Content(schema = @Schema(implementation = ReportSetFrequency.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set frequency not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set frequency ID"
    )
    public ReportSetFrequency update(@PathVariable String id,
                                     @RequestBody UpdateReportSetFrequencyDto data) {
        try {
            return this.reportSetFrequencyService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set frequency ID format");
        }
    }

    /**
     * Soft-deletes a report set frequency by its ID.
     *
     * @param id The string representation of the frequency's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete report set frequency by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set frequency deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set frequency not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set frequency ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.reportSetFrequencyService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set frequency ID format");
        }
    }
}