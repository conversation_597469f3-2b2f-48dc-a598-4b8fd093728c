"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Controller, useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";

export function CommissionTerms() {
  const form = useFormContext<CreatePartnerParams>();

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
      <p className="text-2xl font-bold text-primary">
        Commission Terms
        {form.watch("step_1.noProvisionNegotiated") && (
          <span className="ml-4 text-base italic font-normal text-tonal-dark-cream-30">Optional</span>
        )}
      </p>
      <div className="grid grid-cols-2 gap-4">
        <Controller
          control={form.control}
          name="step_2.commissionTerms.payoutCicle"
          render={({ field, fieldState }) => (
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <SelectGroup>
                <SelectLabel className="text-primary text-base font-normal pl-0">Payout Cycle</SelectLabel>
                <SelectTrigger invalid={!!fieldState.error}>
                  <SelectValue placeholder="Select payout cycle" />
                </SelectTrigger>
              </SelectGroup>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
          )}
        />
        <Controller
          control={form.control}
          name="step_2.commissionTerms.commissionMode"
          render={({ field, fieldState }) => (
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <SelectGroup>
                <SelectLabel className="text-primary text-base font-normal pl-0">Commission Mode</SelectLabel>
                <SelectTrigger invalid={!!fieldState.error}>
                  <SelectValue placeholder="Select commission mode" />
                </SelectTrigger>
              </SelectGroup>
              <SelectContent>
                <SelectItem value="yearly">Yearly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
              </SelectContent>
            </Select>
          )}
        />
        {/* <Controller
          control={form.control}
          name="step_2.commissionTerms.commissionAmount"
          render={({ field, fieldState }) => (
            <Input
              {...field}
              type="number"
              label="Commission Amount"
              placeholder="In percentage"
              variant={fieldState.error ? "error" : "default"}
            />
          )}
        /> */}
      </div>
    </div>
  );
}
