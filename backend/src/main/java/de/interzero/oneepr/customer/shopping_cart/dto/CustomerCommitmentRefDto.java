package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "Customer commitment reference")
@Data
public class CustomerCommitmentRefDto extends BaseDto {

    @JsonProperty("customer_email")
    @Schema(
            description = "Customer email",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String customerEmail;
}