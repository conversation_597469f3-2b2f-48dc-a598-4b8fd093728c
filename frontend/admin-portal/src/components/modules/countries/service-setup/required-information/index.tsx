"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupRequiredInformations } from "@/lib/api/service-setups";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { RequiredInformationsForm } from "./required-information-form";
import React from "react";

export const REQUIRED_INFORMATION_TYPES = {
  DOCUMENT: {
    name: "Document",
    description: "Document to be filled out and/or signed and uploaded by the customer",
    icon: () => (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
          fill="#F1988D"
        />
        <path
          d="M17.1611 31.6001H20.0171C21.8411 31.6001 23.1011 32.5841 23.1011 34.3121C23.1011 36.0281 21.8411 37.0241 20.0171 37.0241H18.9611V40.0001H17.1611V31.6001ZM18.9611 33.2201V35.4281H20.0531C20.7731 35.4281 21.2891 35.0201 21.2891 34.3241C21.2891 33.6281 20.7731 33.2201 20.0531 33.2201H18.9611Z"
          fill="#F2F2F2"
        />
        <path
          d="M31.367 35.8001C31.367 38.1641 29.447 40.0001 27.095 40.0001H24.251V31.6001H27.095C29.447 31.6001 31.367 33.4361 31.367 35.8001ZM29.519 35.8001C29.519 34.4081 28.439 33.2921 27.047 33.2921H26.051V38.3081H27.047C28.439 38.3081 29.519 37.1921 29.519 35.8001Z"
          fill="#F2F2F2"
        />
        <path
          d="M34.5471 36.5801V40.0001H32.7471V31.6001H37.5591V33.2321H34.5471V34.9361H37.3191V36.5801H34.5471Z"
          fill="#F2F2F2"
        />
      </svg>
    ),
  },
  FILE: {
    name: "File request",
    description: "Request a file to be uploaded by the customer",
    icon: () => (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
          fill="#F1988D"
        />
        <path
          d="M17.1611 31.6001H20.0171C21.8411 31.6001 23.1011 32.5841 23.1011 34.3121C23.1011 36.0281 21.8411 37.0241 20.0171 37.0241H18.9611V40.0001H17.1611V31.6001ZM18.9611 33.2201V35.4281H20.0531C20.7731 35.4281 21.2891 35.0201 21.2891 34.3241C21.2891 33.6281 20.7731 33.2201 20.0531 33.2201H18.9611Z"
          fill="#F2F2F2"
        />
        <path
          d="M31.367 35.8001C31.367 38.1641 29.447 40.0001 27.095 40.0001H24.251V31.6001H27.095C29.447 31.6001 31.367 33.4361 31.367 35.8001ZM29.519 35.8001C29.519 34.4081 28.439 33.2921 27.047 33.2921H26.051V38.3081H27.047C28.439 38.3081 29.519 37.1921 29.519 35.8001Z"
          fill="#F2F2F2"
        />
        <path
          d="M34.5471 36.5801V40.0001H32.7471V31.6001H37.5591V33.2321H34.5471V34.9361H37.3191V36.5801H34.5471Z"
          fill="#F2F2F2"
        />
      </svg>
    ),
  },
  IMAGE: {
    name: "Image",
    description: "Request a image (png or jpeg) to be uploaded by the customer",
    icon: () => (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
          fill="#1F71FF"
        />
        <path
          d="M20.5416 37.3238C20.5416 39.0638 19.4616 40.1558 17.7456 40.1558C16.4736 40.1558 15.4776 39.4718 15.0576 38.3918L16.6176 37.5278C16.8336 38.0798 17.2296 38.4518 17.7456 38.4518C18.3816 38.4518 18.7176 38.0318 18.7176 37.2758V31.5998H20.5416V37.3238Z"
          fill="#F2F2F2"
        />
        <path
          d="M22.2118 31.5998H25.0678C26.8918 31.5998 28.1518 32.5838 28.1518 34.3118C28.1518 36.0278 26.8918 37.0238 25.0678 37.0238H24.0118V39.9998H22.2118V31.5998ZM24.0118 33.2198V35.4278H25.1038C25.8238 35.4278 26.3398 35.0198 26.3398 34.3238C26.3398 33.6278 25.8238 33.2198 25.1038 33.2198H24.0118Z"
          fill="#F2F2F2"
        />
        <path
          d="M37.2068 33.6158L35.5748 34.4078C35.1548 33.6038 34.3748 33.1478 33.4028 33.1478C31.9748 33.1478 30.8468 34.3238 30.8468 35.7998C30.8468 37.2758 31.9748 38.4638 33.4148 38.4638C34.5308 38.4638 35.4068 37.8038 35.6228 36.8438H33.1868V35.3318H37.5548V36.0038C37.5548 38.3438 35.8028 40.1558 33.4028 40.1558C30.9548 40.1558 29.0228 38.1998 29.0228 35.7998C29.0228 33.3998 31.0028 31.4438 33.4148 31.4438C35.0948 31.4438 36.5228 32.2718 37.2068 33.6158Z"
          fill="#F2F2F2"
        />
      </svg>
    ),
  },
  TEXT: {
    name: "Text Field",
    description: "Information inputed by the customer",
    icon: () => (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="40" height="40" rx="8" fill="#FF9E14" />
        <path
          d="M35.9163 17.5002V13.1668C35.9163 12.571 35.4288 12.0835 34.833 12.0835H30.4997C29.9038 12.0835 29.4163 12.571 29.4163 13.1668V14.2502H18.583V13.1668C18.583 12.571 18.0955 12.0835 17.4997 12.0835H13.1663C12.5705 12.0835 12.083 12.571 12.083 13.1668V17.5002C12.083 18.096 12.5705 18.5835 13.1663 18.5835H14.2497V29.4168H13.1663C12.5705 29.4168 12.083 29.9043 12.083 30.5002V34.8335C12.083 35.4293 12.5705 35.9168 13.1663 35.9168H17.4997C18.0955 35.9168 18.583 35.4293 18.583 34.8335V33.7502H29.4163V34.8335C29.4163 35.4293 29.9038 35.9168 30.4997 35.9168H34.833C35.4288 35.9168 35.9163 35.4293 35.9163 34.8335V30.5002C35.9163 29.9043 35.4288 29.4168 34.833 29.4168H33.7497V18.5835H34.833C35.4288 18.5835 35.9163 18.096 35.9163 17.5002ZM14.2497 14.2502H16.4163V16.4168H14.2497V14.2502ZM16.4163 33.7502H14.2497V31.5835H16.4163V33.7502ZM29.4163 31.5835H18.583V30.5002C18.583 29.9043 18.0955 29.4168 17.4997 29.4168H16.4163V18.5835H17.4997C18.0955 18.5835 18.583 18.096 18.583 17.5002V16.4168H29.4163V17.5002C29.4163 18.096 29.9038 18.5835 30.4997 18.5835H31.583V29.4168H30.4997C29.9038 29.4168 29.4163 29.9043 29.4163 30.5002V31.5835ZM33.7497 33.7502H31.583V31.5835H33.7497V33.7502ZM31.583 16.4168V14.2502H33.7497V16.4168H31.583ZM25.018 19.2877C24.8555 18.8652 24.4438 18.5835 23.9888 18.5835C23.5338 18.5835 23.1222 18.8652 22.9705 19.2877L19.9697 27.2285C19.7638 27.7593 20.1538 28.3335 20.728 28.3335C21.0747 28.3335 21.378 28.1168 21.4972 27.7918L22.093 26.1668H25.8738L26.4805 27.8027C26.5997 28.1168 26.903 28.3335 27.2497 28.3335H27.2605C27.8347 28.3335 28.2247 27.7593 28.0297 27.2285L25.018 19.2877ZM22.5805 24.8018L23.9997 20.6527L25.408 24.8018H22.5805Z"
          fill="#F5F5F5"
        />
      </svg>
    ),
  },
  NUMBER: {
    name: "Number Field",
    description: "Number inputed by the customer",
    icon: () => (
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="40" height="40" rx="8" fill="#002652" />
        <path
          d="M35.7155 19.9736L35.7284 19.8961C35.9351 19.0953 35.3409 18.3332 34.5272 18.3332H30.9622L31.8664 14.7294C32.0601 13.9286 31.4659 13.1665 30.6522 13.1665C30.0709 13.1665 29.5801 13.554 29.438 14.1094L28.3789 18.3332H23.2122L24.1164 14.7294C24.3101 13.9286 23.7159 13.1665 22.9022 13.1665C22.3209 13.1665 21.8301 13.554 21.688 14.1094L20.6289 18.3332H16.4439C15.8626 18.3332 15.3589 18.7207 15.2297 19.2761L15.2039 19.3536C15.0101 20.1544 15.6043 20.9165 16.418 20.9165H19.983L18.6914 26.0832H14.5064C13.9251 26.0832 13.4343 26.4707 13.2922 27.0261L13.2664 27.1036C13.0726 27.9044 13.6668 28.6665 14.4805 28.6665H18.0455L17.1414 32.2703C16.9476 33.0711 17.5418 33.8332 18.3555 33.8332C18.9368 33.8332 19.4276 33.4457 19.5697 32.8903L20.6289 28.6665H25.7955L24.8914 32.2703C24.6976 33.0711 25.2918 33.8332 26.1055 33.8332C26.6868 33.8332 27.1776 33.4457 27.3197 32.8903L28.3789 28.6665H32.5639C33.1451 28.6665 33.6359 28.279 33.778 27.7236L33.7909 27.6461C33.9847 26.8582 33.3905 26.0832 32.5768 26.0832H29.0247L30.3164 20.9165H34.5014C35.0826 20.9165 35.5864 20.529 35.7155 19.9736ZM26.4414 26.0832H21.2747L22.5664 20.9165H27.733L26.4414 26.0832Z"
          fill="white"
        />
      </svg>
    ),
  },
};

interface ServiceSetupRequiredInformationProps {}

export function ServiceSetupRequiredInformation({}: ServiceSetupRequiredInformationProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "required-informations";

  function handleOpenStep() {
    changeParam("step", "required-informations");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: requiredInformations, isFetching } = useQuery({
    queryKey: ["service-setup-required-informations", country.code],
    queryFn: () => getServiceSetupRequiredInformations(country.code),
  });

  const isComplete = requiredInformations && !!requiredInformations.length;

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="required-informations-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={handleOpenStep}
      id="required-informations-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">6. Required information</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>
      {isSelected && (
        <RequiredInformationsForm requiredInformations={requiredInformations || []} onCloseStep={handleCloseStep} />
      )}
    </div>
  );
}
