package de.interzero.oneepr.customer.termination;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.license.License;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link Termination} entities.
 */
@Repository
public interface TerminationRepository extends JpaRepository<Termination, Integer> {

    /**
     * Finds a single Termination by its ID, eagerly fetching all related entities required by the service.
     * This graph includes contracts, action guides, licenses, files, and nested reasons.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations, or empty if not found.
     */
    @Override
    @NonNull
    Optional<Termination> findById(@NonNull Integer id);

    /**
     * Finds a single Termination by its ID, fetching all relations needed for the update logic.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations loaded.
     */
    Optional<Termination> findWithAllUpdateRelationsById(Integer id);

    /**
     * Finds a single Termination by ID, fetching all relations needed for the revoke logic.
     *
     * @param id The ID of the termination to find.
     * @return An Optional containing the Termination with its relations.
     */
    Optional<Termination> findWithRevokeRelationsById(Integer id);

    /**
     * Finds all Terminations that are linked to any of the given Contracts
     * and have not been soft-deleted.
     * <p>
     * This query explicitly joins the Termination's 'contracts' collection
     * to filter based on the provided list of Contract entities.
     *
     * @param contracts A list of contracts to find related terminations for.
     * @return A list of matching, non-deleted Termination entities.
     */
    @Query("SELECT t FROM Termination t JOIN t.contracts c WHERE c IN :contracts AND t.deletedAt IS NULL")
    List<Termination> findByContractInAndDeletedAtIsNull(@Param("contracts") List<Contract> contracts);

    @Query("SELECT t FROM Termination t JOIN t.licenses l WHERE l IN :licenses AND t.deletedAt IS NULL")
    List<Termination> findByLicenseInAndDeletedAtIsNull(@Param("licenses") List<License> licenses);
}

