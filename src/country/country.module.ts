import { Module } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { CountryController } from "./country.controller";
import { CountryService } from "./country.service";
import { HttpApiModule } from "@/http/http.module";

@Module({
  imports: [DatabaseModule, HttpApiModule],
  controllers: [CountryController],
  providers: [CountryService],
})
export class CountryModule {}
