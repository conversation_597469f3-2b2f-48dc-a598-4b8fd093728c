import { LicenseReportSetRhythm } from "@prisma/client";

export type Criteria = {
  id: number;
  mode: "COMMITMENT" | "CALCULATOR";
  type: CriteriaType;
  title: string;
  help_text: string | null;
  input_type: "YES_NO";
  calculator_type: string | null;
  country_id: number;
  packaging_service_id: number | null;
  required_information_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  options: {
    id: number;
    option_value: string;
    option_to_value: string | null;
    value: string;
  }[];
};

type CriteriaType =
  | "PACKAGING_SERVICE"
  | "REPORT_SET"
  | "REPORT_FREQUENCY"
  | "AUTHORIZE_REPRESENTATIVE"
  | "REPRESENTATIVE_TIER"
  | "OTHER_COST"
  | "PRICE_LIST"
  | "REQUIRED_INFORMATION";

export type Commitment = (Omit<Criteria, "mode"> & { mode: "COMMITMENT" })[];

export type RepresentativeTier = {
  id: number;
  name: string;
  price: number;
};

export type OtherCost = {
  id: number;
  name: string;
  price: number;
};

export type PackagingService = {
  id: number;
  name: string;
  description: string;
  country_id: number;
  obliged: boolean;
};

export type ReportSet = {
  id: number;
  name: string;
  mode: string;
  type: string;
  fractions: {
    id: number;
    name: string;
    description: string;
    icon: string;
    is_active: boolean;
    report_set_id: number;
    parent_id: number | null;
    children: ReportSet["fractions"];
  }[];
  columns: {
    id: number;
    name: string;
    description: string;
    unit_type: string;
    report_set_id: number;
    parent_id: number | null;
    children: ReportSet["columns"];
  }[];
};

export type ReportSetFrequency = {
  id: number;
  rhythm: LicenseReportSetRhythm;
  frequency: {
    deadline: unknown;
    open: unknown;
  };
  packaging_service_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
};

export type RequiredInformation = {
  id: number;
  type: string;
  name: string;
  description: string;
  question: string | null;
  file_id: string | null;
};

type BasePriceList = {
  id: string;
  name: string;
  service_type: "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";
  description: string;
  condition_type: "LICENSE_YEAR";
  condition_type_value: string;
  start_date: string;
  end_date: string;
};

export type LicensePriceList = BasePriceList & {
  service_type: "EU_LICENSE";
  basic_price: number;
  minimum_price: number;
  registration_fee: number;
  handling_fee: number;
  variable_handling_fee: number;
};

export type ActionGuidePriceList = BasePriceList & {
  service_type: "ACTION_GUIDE";
  price: number;
};

export type CustomerServiceSetup = {
  country: {
    id: number;
    name: string;
    code: string;
    flag_url: string;
    authorize_representative_obligated: boolean;
    other_costs_obligated: boolean;
  };
  year: string;
  packaging_services: (PackagingService & {
    report_set: ReportSet;
    report_set_frequency: ReportSetFrequency;
  })[];
  authorize_representative_obligated: boolean;
  representative_tier: RepresentativeTier;
  other_costs_obligated: boolean;
  other_costs: OtherCost[];
  required_informations: RequiredInformation[];
  price_list: LicensePriceList;
};

export interface CommitmentSubmissionResponse {
  year: number;
  commitment: (Omit<Criteria, "mode"> & { mode: "COMMITMENT"; answer: string; to_answer?: string })[];
  setup: CustomerServiceSetup;
}

export interface CustomerCommitment {
  id: number;
  year: number;
  commitment: (Omit<Criteria, "mode"> & { mode: "COMMITMENT"; answer: string; to_answer?: string })[];
  setup: CustomerServiceSetup;
}
