package de.interzero.oneepr.admin.mail;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.util.Map;

@AllArgsConstructor
@Data
public class EmailMessage {

    @Nullable
    private String transactionalMessageId;

    private String to;

    private String from;

    @Nullable
    private String recipientName;

    @Nullable
    private String subject;

    @Nullable
    private Map<String, Object> messageData;

    /**
     * Constructor without recipientName. Use setRecipientName to set it.
     */
    public EmailMessage(@Nullable String transactionalMessageId,
                        String to,
                        String from,
                        @Nullable String subject,
                        @Nullable Map<String, Object> messageData) {
        this.transactionalMessageId = transactionalMessageId;
        this.to = to;
        this.from = from;
        this.subject = subject;
        this.messageData = messageData;
    }

    /**
     * Sets recipientName by joining two strings with a space.
     *
     * @param firstName first name
     * @param lastName  last name
     */
    public void setRecipientName(String firstName,
                                 String lastName) {
        if (firstName == null || lastName == null) {
            throw new IllegalArgumentException("User name not found: firstName or lastName is null");
        }
        this.recipientName = firstName + ", " + lastName;
    }

}
