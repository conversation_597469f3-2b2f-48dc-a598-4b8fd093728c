export interface ReportSetPriceList {
  id: number;
  report_set_id: number;
  title: string;
  start_date: string;
  end_date: string;
  type: ReportSetPriceListType;
  fixed_price: number | null;
  base_price: number | null;
  minimum_fee: number | null;
  items: { id?: number; fraction_code: string; price: number }[];
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export type ReportSetPriceListType =
  | "FIXED_PRICE"
  | "PRICE_PER_CATEGORY"
  | "PRICE_PER_VOLUME_BASE_PRICE"
  | "PRICE_PER_VOLUME_MINIMUM_FEE";

export type CreateReportSetPriceList = Omit<ReportSetPriceList, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateReportSetPriceList = Partial<
  Omit<ReportSetPriceList, "id" | "report_set_id" | "created_at" | "updated_at" | "deleted_at">
>;
