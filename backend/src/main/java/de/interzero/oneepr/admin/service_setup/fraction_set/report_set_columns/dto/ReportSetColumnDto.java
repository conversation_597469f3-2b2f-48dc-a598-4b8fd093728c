package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;

/**
 * This DTO is a direct translation of the ReportSetColumn interface from the NestJS source.
 * It is designed to be the serializable response object for a single ReportSetColumn.
 */
@Data
public class ReportSetColumnDto {

    @Schema(description = "The unique identifier of the column.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The ID of the parent column, if this is a child column.")
    @JsonProperty("parent_id")
    private Integer parentId;

    @Schema(description = "A unique code for the column, used for linking.")
    @JsonProperty("code")
    private String code;

    @Schema(description = "The code of the parent column, if this is a child column.")
    @JsonProperty("parent_code")
    private String parentCode;

    @Schema(description = "The display name of the column.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "A detailed description of the column's purpose.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "The unit of measurement for data in this column.", implementation = ReportSetColumn.UnitType.class)
    @JsonProperty("unit_type")
    private ReportSetColumn.UnitType unitType;

    @Schema(description = "The ID of the report set this column belongs to.")
    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @Schema(description = "The nesting level of the column in the hierarchy (1 for root).")
    @JsonProperty("level")
    private Integer level;

    @Schema(description = "The display order of the column among its siblings.")
    @JsonProperty("order")
    private Integer order;

    @Schema(description = "The timestamp when the column was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp when the column was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The timestamp when the column was soft-deleted.")
    @JsonProperty("deleted_at")
    private Instant deletedAt;
}