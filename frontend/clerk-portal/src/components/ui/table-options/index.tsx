import * as React from "react";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { MdMoreVert } from "react-icons/md";

const cn = (...classes: (string | undefined)[]) => classes.filter(Boolean).join(" ");

type DropdownProps = React.ComponentPropsWithoutRef<typeof DropdownMenu.Root>;
const Dropdown = ({ children, ...props }: DropdownProps) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <DropdownMenu.Root open={isOpen} onOpenChange={setIsOpen} {...props}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === DropdownTrigger) {
          return React.cloneElement(child, { isOpen } as any);
        }
        return child;
      })}
    </DropdownMenu.Root>
  );
};

type DropdownTriggerProps = React.ComponentPropsWithoutRef<typeof DropdownMenu.Trigger> & {
  isOpen?: boolean;
};

const DropdownTrigger = React.forwardRef<HTMLButtonElement, DropdownTriggerProps>(
  ({ className, isOpen, ...props }, ref) => (
    <DropdownMenu.Trigger asChild {...props} ref={ref}>
      <button className={cn("p-2 focus:outline-none", className)}>
        <MdMoreVert className={`text-2xl rounded-full hover:bg-surface-01 ${isOpen ? "bg-surface-01" : ""}`} />
      </button>
    </DropdownMenu.Trigger>
  )
);

DropdownTrigger.displayName = "DropdownTrigger";

const DropdownContent = React.forwardRef<HTMLDivElement, React.ComponentPropsWithoutRef<typeof DropdownMenu.Content>>(
  ({ className, children, ...props }, ref) => (
    <DropdownMenu.Content
      {...props}
      ref={ref}
      className={cn("bg-white shadow-elevation-04-1 shadow-lg rounded-2xl overflow-hidden", className)}
    >
      {children}
    </DropdownMenu.Content>
  )
);
DropdownContent.displayName = "DropdownContent";

const DropdownItem = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<typeof DropdownMenu.Item> & { children: React.ReactNode }
>(({ className, children, ...props }, ref) => (
  <DropdownMenu.Item
    {...props}
    ref={ref}
    className={cn(
      "flex items-center group py-5 px-4 text-base focus:outline-none cursor-pointer gap-4 hover:bg-[#009DD31F] w-fit md:w-[160px] max-w-full",
      className
    )}
  >
    {children}
  </DropdownMenu.Item>
));
DropdownItem.displayName = "DropdownItem";

export { Dropdown, DropdownTrigger, DropdownContent, DropdownItem };
