import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { FileCopy } from "@arthursenno/lizenzero-ui-react/Icon";
import { enqueueSnackbar } from "notistack";

interface ITextCopy {
  text: string;
  className?: string;
}

export function TextCopy({ text, className }: ITextCopy) {
  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        enqueueSnackbar("Text copied to clipboard.", {
          variant: "default",
        });
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.error("Error copying text: ", err);
      });
  };

  return (
    <div
      className={cn(
        "bg-background rounded-[60px] py-2 pr-4 pl-5 flex flex-row border border-tonal-dark-cream-80 gap-4 items-center w-min justify-between",
        className
      )}
    >
      <p className="text-primary text-sm">{text}</p>

      <Button
        leadingIcon={<FileCopy className="fill-primary" />}
        variant="filled"
        color="yellow"
        size="iconXSmall"
        onClick={copyToClipboard}
      />
    </div>
  );
}
