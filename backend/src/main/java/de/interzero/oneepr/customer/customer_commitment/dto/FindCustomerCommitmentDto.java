package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for finding customer commitments based on specified criteria.
 * This DTO is typically used to filter customer commitments, for example, via query parameters in a GET request.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindCustomerCommitmentDto {

    @Schema(
            description = "The customer email to filter by.",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_email")
    private String customerEmail;

    @Schema(
            description = "The year to filter by.",
            example = "2025",
            type = "integer"
    )
    @JsonProperty("year")
    private Integer year;

    @Schema(
            description = "The country code to filter by.",
            example = "ID",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("country_code")
    private String countryCode;
}
