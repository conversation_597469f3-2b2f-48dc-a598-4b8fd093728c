"use client";

import { useEffect, useState } from "react";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { z } from "zod";

import { useMutation } from "@tanstack/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";
import { FractionInput } from "@/components/ui/fraction-input";
import { Textarea } from "@/components/ui/textarea";

const transferFundsFormSchema = z.object({
  amount: z.number(),
  iban: z.string(),
  bic: z.string(),
  full_name: z.string(),
  address: z.string(),
  payment_reason: z.string(),
});

type TransferFundsFormData = z.infer<typeof transferFundsFormSchema>;

interface TransferFundsDialogDialogProps {
  invoiceId: string | number;
  children: React.ReactNode;
}

export function TransferFundsDialog({ invoiceId, children }: TransferFundsDialogDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<"submit" | "done">("submit");

  const { formState, ...form } = useForm<TransferFundsFormData>({
    resolver: zodResolver(transferFundsFormSchema),
    // defaultValues,
  });

  const { mutateAsync } = useMutation({
    mutationFn: (data: TransferFundsFormData) => {
      // transferFunds(invoiceId, data)

      // eslint-disable-next-line no-console
      console.log("data", data);
      return new Promise((resolve) => setTimeout(resolve, 2000));
    },
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ["invoice-orders", invoiceId] });
      setStep("done");
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  useEffect(() => {
    if (!isOpen) {
      setStep("submit");
      form.reset();
    }

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  async function handleSubmit(data: TransferFundsFormData) {
    await mutateAsync(data);
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-3xl">
        {step === "submit" && (
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem] text-primary">Transfer payment</DialogTitle>
            <DialogDescription className="text-base text-tonal-dark-cream-20">
              To request a refound please provide this informations:
            </DialogDescription>
          </DialogHeader>
        )}
        {step === "done" && (
          <DialogHeader className="mb-0">
            <div className="flex items-center gap-2 mb-10">
              <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
              <h3 className="font-bold text-[1.75rem] text-primary">Transfer complete</h3>
            </div>
          </DialogHeader>
        )}
        {step === "submit" && (
          <form onSubmit={form.handleSubmit(handleSubmit)} className="w-full grid md:grid-cols-2 md:items-start gap-6">
            <Controller
              name="amount"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="col-span-full">
                  <FractionInput
                    label="Amount to be paid back"
                    type="currency"
                    value={field.value || undefined}
                    onChange={field.onChange}
                    error={error?.message}
                    className="text-left"
                  />
                </div>
              )}
            />
            <Controller
              name="iban"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="IBAN (International Bank Account Number)"
                    placeholder="IBAN"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="bic"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="BIC (Bank Identifier Code)"
                    placeholder="BIC"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="full_name"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="Full Name"
                    placeholder="Full Name"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="address"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="md:col-span-1">
                  <Input
                    label="Address"
                    placeholder="Address"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <Controller
              name="payment_reason"
              control={form.control}
              render={({ field, fieldState: { error } }) => (
                <div className="col-span-full px-1">
                  <Textarea
                    label="Payment reason"
                    placeholder="Payment reason"
                    errorMessage={error?.message}
                    {...field}
                  />
                </div>
              )}
            />
            <DialogFooter className="col-span-full mt-6 w-full flex items-center justify-end">
              <Button variant="filled" color="dark-blue" size="medium" disabled={formState.isSubmitting}>
                {formState.isSubmitting ? "Saving..." : "Continue"}
              </Button>
            </DialogFooter>
          </form>
        )}
        {step === "done" && (
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="filled" color="yellow" size="medium">
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
}
