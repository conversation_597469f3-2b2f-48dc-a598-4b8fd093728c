package de.interzero.oneepr.admin.admin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "Fields required to create a new admin user")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateAdminDto extends BaseDto {

    @JsonProperty("email")
    @Schema(
            description = "Email address of the admin",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String email;

    @JsonProperty("name")
    @Schema(
            description = "Full name of the admin",
            example = "John Admin",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @JsonProperty("password")
    @Schema(
            description = "Password for the admin user",
            example = "strong_password_123",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String password;

    @JsonProperty("role_id")
    @Schema(
            description = "Role ID assigned to the admin",
            example = "2",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer roleId;
}
