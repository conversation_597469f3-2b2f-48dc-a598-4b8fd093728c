package de.interzero.oneepr.customer.license.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license.License;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
/**
 * Update License DTO
 * Converted from TypeScript update-license.dto.ts
 * Maintains exact same structure, variable names, and property names.
 * All fields are optional for partial updates.
 */
@Data
public class UpdateLicenseDto {

    /**
     * The registration status of the contract (optional for updates)
     * Equivalent to TypeScript: registration_status: LicenseRegistrationStatus;
     */
    @JsonProperty("registration_status")
    @Schema(
            description = "The registration status of the contract",
            implementation = License.RegistrationStatus.class,
            example = "DONE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private License.RegistrationStatus registrationStatus;

    /**
     * The clerk control status of the contract (optional for updates)
     * Equivalent to TypeScript: clerk_control_status: LicenseClerkControlStatus;
     */
    @JsonProperty("clerk_control_status")
    @Schema(
            description = "The clerk control status of the contract",
            implementation = License.ClerkControlStatus.class,
            example = "DONE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private License.ClerkControlStatus clerkControlStatus;

    /**
     * The contract status of the contract (optional for updates)
     * Equivalent to TypeScript: contract_status: LicenseContractStatus;
     */
    @JsonProperty("contract_status")
    @Schema(
            description = "The contract status of the contract",
            implementation = License.ContractStatus.class,
            example = "ACTIVE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private License.ContractStatus contractStatus;

    /**
     * The termination ID of the contract (optional for updates)
     * Equivalent to TypeScript: termination_id: number;
     */
    @JsonProperty("termination_id")
    @Schema(
            description = "The termination ID of the contract",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer terminationId;
}
