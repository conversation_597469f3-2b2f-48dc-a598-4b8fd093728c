import { Datatable } from "@/components/common/datatable";
import { AdsClick, LocalOffer } from "@interzero/oneepr-react-ui/Icon";
import { createColumnHelper } from "@tanstack/react-table";

interface CustomersManagerTableData {
  id: string;
  recruitingCustomerName: string;
  customerAcquiredDate: string;
  commissionDate: string;
  payedAmount: string;
  product: string;
  commission: string;
  netValue: string;
  linkCode: string;
  leadOrigin: string;
  // actions: string;
}

const data: CustomersManagerTableData[] = [
  {
    id: "1",
    recruitingCustomerName: "<PERSON>",
    customerAcquiredDate: "25.03.23",
    commissionDate: "21.05.2023",
    payedAmount: "515.00",
    product: "EU License",
    commission: "30.00",
    netValue: "6565656",
    leadOrigin: "Coupon",
    linkCode: "WELCOM10",
    // actions: "Edit",
  },
  {
    id: "2",
    recruitingCustomerName: "<PERSON>",
    customerAcquiredDate: "25.03.23",
    commissionDate: "21.05.2023",
    payedAmount: "515.00",
    product: "EU License",
    commission: "30.00",
    netValue: "6565656",
    leadOrigin: "Link",
    linkCode: "WELCOM10",
    // actions: "Edit",
  },
  {
    id: "3",
    recruitingCustomerName: "Michael Pherman",
    customerAcquiredDate: "25.03.23",
    commissionDate: "21.05.2023",
    payedAmount: "515.00",
    product: "EU License",
    commission: "30.00",
    netValue: "6565656",
    leadOrigin: "Coupon",
    linkCode: "WELCOM10",
    // actions: "Edit",
  },
  {
    id: "4",
    recruitingCustomerName: "Michael Pherman",
    customerAcquiredDate: "25.03.23",
    commissionDate: "21.05.2023",
    payedAmount: "515.00",
    product: "EU License",
    commission: "30.00",
    netValue: "6565656",
    leadOrigin: "Coupon",
    linkCode: "WELCOM10",
    // actions: "Edit",
  },
  {
    id: "5",
    recruitingCustomerName: "Michael Pherman",
    customerAcquiredDate: "25.03.23",
    commissionDate: "21.05.2023",
    payedAmount: "515.00",
    product: "EU License",
    commission: "30.00",
    netValue: "6565656",
    leadOrigin: "Coupon",
    linkCode: "WELCOM10",
    // actions: "Edit",
  },
];

const columnHelper = createColumnHelper<CustomersManagerTableData>();

const columns = [
  // columnHelper.accessor("recruitingCustomerName", {
  //     header: "Recruiting Customer",
  //     cell: (info) => (
  //         <div className="flex flex-col gap-1">
  //             <p className="text-sm">{info.getValue()}</p>
  //             <span className="text-xs text-tonal-dark-cream-30"><EMAIL></span>
  //         </div>
  //     ),
  // }),
  columnHelper.accessor("recruitingCustomerName", {
    header: "Name of recruited customer",
    cell: (info) => <p className="text-sm ">{info.getValue()}</p>,
  }),
  columnHelper.accessor("customerAcquiredDate", {
    header: "Customers acquired",
    cell: (info) => <p className="text-sm max-w-24">{info.getValue()}</p>,
  }),
  columnHelper.accessor("commissionDate", {
    header: "Commission Date",
    cell: (info) => <p className="text-sm max-w-24">{info.getValue()}</p>,
  }),
  columnHelper.accessor("payedAmount", {
    header: "Payed Amount",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("product", {
    header: "Product",
    cell: (info) => <p className="text-sm w-20">{info.getValue()}</p>,
  }),
  columnHelper.accessor("commission", {
    header: "Commission (€)",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("netValue", {
    header: "Net value (€)",
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor("leadOrigin", {
    header: "Lead Origin ",
    cell: (info) => {
      const value = info.getValue();
      const icon =
        value === "Coupon" ? (
          <LocalOffer className="size-4 fill-on-secondary" />
        ) : value === "Link" ? (
          <AdsClick className="size-4 fill-on-secondary" />
        ) : null;
      return (
        <span
          className={`text-small-paragraph-regular text-on-secondary font-bold p-1 rounded bg-[#A9C8FF] flex items-center`}
        >
          {icon}
          <span className="ml-2">{value}</span>
        </span>
      );
    },
  }),
  columnHelper.accessor("linkCode", {
    header: "Link/Code",
    cell: (info) => info.getValue(),
  }),
  // columnHelper.display({
  //     id: "actions",
  //     header: " ",
  //     cell: () => (
  //         <div className="w-4 flex items-center mr-4">
  //             <Dropdown
  //                 trigger={
  //                     <button className="rounded-full p-1 hover:bg-secondary/30">
  //                         <EllipsisVertical className="size-6" />
  //                     </button>
  //                 }
  //             >
  //                 <DropdownItem asChild className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer">
  //                     <div className="flex items-center">
  //                         <Pencil className="size-4 mr-5 text-primary" /> Edit
  //                     </div>
  //                 </DropdownItem>
  //                 <DropdownItem className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer">
  //                     <div className="flex items-center">
  //                         <FileCopy className="size-4 mr-5 fill-primary" /> Duplicate
  //                     </div>
  //                 </DropdownItem>
  //                 <DropdownItem className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer">
  //                     <div className="flex items-center">
  //                         <Delete className="size-4 mr-5 fill-primary" /> Delete
  //                     </div>
  //                 </DropdownItem>
  //             </Dropdown>
  //         </div>
  //     ),
  // }),
];

export function CustomersManagerTable() {
  return (
    <div className="w-full">
      <Datatable data={data} columns={columns} />
    </div>
  );
}
