package de.interzero.oneepr.customer.file;

import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping a {@link File} entity to its detailed DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This is a simple, direct mapping as the 'File' is treated as a leaf node
 * in the context of the Contract query's include structure.
 */
@Mapper(componentModel = "spring")
public interface FileMapper {

    /**
     * Maps a {@link File} entity to a {@link FileDetailsDto}.
     * <p>
     * MapStruct will automatically map all fields with matching names.
     * The various parent-link fields in the entity (e.g., contract, license)
     * are correctly ignored as they do not exist in the DTO.
     *
     * @param file The source File entity.
     * @return The mapped {@link FileDetailsDto}.
     */
    FileDetailsDto toDetailsDto(File file);
}