import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { LicenseReportSetDto, ReportSet } from "./types";

export async function getReportSets({ countryCode }: { countryCode: string }) {
  try {
    const response = await api.get<ReportSet[]>(ApiEndpoints.reportSet.get(countryCode));

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function updateReportSets(id: string, data: LicenseReportSetDto) {
  try {
    const response = await api.patch<ReportSet[]>(ApiEndpoints.reportSet.put(id), data);

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}
