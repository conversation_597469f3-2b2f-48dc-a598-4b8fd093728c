import { ReportSet } from "@/lib/api/commitment/types";

export type ItemPackagingServiceReportSetFraction = {
  id: number;
  name: string;
  description: string;
  icon: string;
  isActive: boolean;
  reportSetId: number;
  parentId: number | null;
  value?: number;
  price: number;
  children: ItemPackagingServiceReportSetFraction[];
};

export type ItemPackagingServiceReportSetColumn = {
  id: number;
  name: string;
  description: string;
  reportSetId: number;
  parentId: number | null;
  unitType: string;
  children: ItemPackagingServiceReportSetColumn[];
};

export function setupFractionToCartFraction(
  fraction: ReportSet["fractions"][number]
): ItemPackagingServiceReportSetFraction {
  return {
    id: fraction.id,
    name: fraction.name,
    description: fraction.description,
    icon: fraction.icon,
    isActive: fraction.is_active,
    reportSetId: fraction.report_set_id,
    parentId: fraction.parent_id,
    value: undefined,
    price: 99,
    ...(fraction.children && { children: fraction.children.map((child) => setupFractionToCartFraction(child)) }),
  };
}

export function setupColumnToCartColumn(column: ReportSet["columns"][number]): ItemPackagingServiceReportSetColumn {
  return {
    id: column.id,
    name: column.name,
    description: column.description,
    reportSetId: column.report_set_id,
    parentId: column.parent_id,
    unitType: column.unit_type,
    ...(column.children && { children: column.children.map((child) => setupColumnToCartColumn(child)) }),
  };
}

export function cartFractionToCartFraction(
  fraction: ItemPackagingServiceReportSetFraction
): ReportSet["fractions"][number] {
  return {
    id: fraction.id,
    name: fraction.name,
    description: fraction.description,
    icon: fraction.icon,
    is_active: fraction.isActive,
    report_set_id: fraction.reportSetId,
    parent_id: fraction.parentId,
    ...(fraction.children && { children: fraction.children.map((child) => cartFractionToCartFraction(child)) }),
  };
}

export function cartColumnToCartColumn(column: ItemPackagingServiceReportSetColumn): ReportSet["columns"][number] {
  return {
    id: column.id,
    name: column.name,
    description: column.description,
    report_set_id: column.reportSetId,
    parent_id: column.parentId,
    unit_type: column.unitType,
    ...(column.children && { children: column.children.map((child) => cartColumnToCartColumn(child)) }),
  };
}
