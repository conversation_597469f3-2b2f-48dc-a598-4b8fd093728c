import { Modu<PERSON> } from "@nestjs/common";
import { BrokerCompaniesService } from "./broker-companies.service";
import { BrokerCompaniesController } from "./broker-companies.controller";
import { DatabaseModule } from "../database/database.module";
import { UploadDataService } from "../upload-data/upload-data.service";

@Module({
  imports: [DatabaseModule],
  controllers: [BrokerCompaniesController],
  providers: [BrokerCompaniesService, UploadDataService],
})
export class BrokerCompaniesModule {}
