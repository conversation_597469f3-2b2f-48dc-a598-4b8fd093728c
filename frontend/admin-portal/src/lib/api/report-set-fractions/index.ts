import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import {
  CreateReportSetFraction,
  ReportSetFraction,
  UpdateReportSetFraction,
} from "@/types/service-setup/report-set-fraction";

export async function getReportSetFractions(reportSetId: number) {
  const response = await adminApi.get<ReportSetFraction[]>(ApiEndpoints.reportSetFractions.findAll, {
    params: {
      reportSetId,
    },
  });

  return response.data;
}

export async function createReportSetFraction(reportSetFraction: CreateReportSetFraction) {
  const response = await adminApi.post<ReportSetFraction>(ApiEndpoints.reportSetFractions.create, reportSetFraction);

  return response.data;
}

export async function updateReportSetFraction(reportSetFractionId: number, reportSetFraction: UpdateReportSetFraction) {
  const response = await adminApi.put<ReportSetFraction>(
    ApiEndpoints.reportSetFractions.update(reportSetFractionId),
    reportSetFraction
  );

  return response.data;
}

export async function deleteReportSetFraction(reportSetFractionId: number) {
  await adminApi.delete(ApiEndpoints.reportSetFractions.delete(reportSetFractionId));
}
