package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.auth.role.Role;
import de.interzero.oneepr.auth.user.User.Status;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDateTime;

@Data
@Builder
@Schema(description = "DTO for returning user information")
public class UserResponseDto {

    @JsonProperty("id")
    @Schema(
            description = "Unique identifier of the user",
            example = "42"
    )
    private Integer id;

    @JsonProperty("first_name")
    @Schema(
            description = "First name of the user",
            example = "John",
            nullable = true
    )
    private String firstName;

    @JsonProperty("last_name")
    @Schema(
            description = "Last name of the user",
            example = "Doe",
            nullable = true
    )
    private String lastName;

    @JsonProperty("name")
    @Schema(
            description = "Username used for login",
            example = "john.doe"
    )
    private String name;

    @JsonProperty("email")
    @Schema(
            description = "Email address of the user",
            example = "<EMAIL>"
    )
    private String email;

    @JsonProperty("is_active")
    @Schema(
            description = "Whether the user is active",
            example = "true"
    )
    private Boolean isActive;

    @JsonProperty("role_id")
    @Schema(
            description = "ID of the role assigned to the user",
            example = "1",
            nullable = true
    )
    private Integer roleId;

    @JsonProperty("role")
    @Schema(
            description = "Role assigned to the user",
            implementation = Role.class
    )
    private Role role;

    @JsonProperty("token_verify")
    @Schema(
            description = "Token used to verify email",
            example = "a1b2c3d4",
            nullable = true
    )
    private String tokenVerify;

    @JsonProperty("token_expiration")
    @Schema(
            description = "Expiration date of the email verification token",
            example = "2025-12-31T23:59:59",
            nullable = true
    )
    private LocalDateTime tokenExpiration;

    @JsonProperty("token_magic_link")
    @Schema(
            description = "Token used for magic link login",
            example = "xyz-123-abc",
            nullable = true
    )
    private String tokenMagicLink;

    @JsonProperty("token_create_password")
    @Schema(
            description = "Token used to create a password",
            example = "pwd-456-def",
            nullable = true
    )
    private String tokenCreatePassword;

    @JsonProperty("token_attempts")
    @Schema(
            description = "Number of failed token attempts",
            example = "0"
    )
    private Integer tokenAttempts;

    @JsonProperty("block_time")
    @Schema(
            description = "Time until which the user is blocked",
            example = "2025-07-22T15:00:00",
            nullable = true
    )
    private LocalDateTime blockTime;

    @JsonProperty("status")
    @Schema(
            description = "Account verification status",
            example = "COMPLETE"
    )
    private Status status;

    @JsonProperty("created_at")
    @Schema(
            description = "User creation timestamp",
            example = "2025-07-22T10:00:00Z"
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(
            description = "User last updated timestamp",
            example = "2025-07-22T12:00:00Z"
    )
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Schema(
            description = "User deletion timestamp (if deleted)",
            example = "2025-07-23T10:00:00Z",
            nullable = true
    )
    private Instant deletedAt;
}
