package de.interzero.oneepr.customer.commission.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.commission.Commission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for finding all commissions with filtering options.
 * Converted from TypeScript find-all-comissions.dto.ts with exact same structure and variable names.
 */
@Getter
@Setter
public class FindAllCommissionsDto {

    @Schema(description = "User ID")
    @JsonProperty("user_id")
    private String userId;

    @Schema(description = "Service type")
    @JsonProperty("service_type")
    private Commission.ServiceType serviceType;

    @Schema(description = "Commission type")
    @JsonProperty("type")
    private Commission.Type type;

    @Schema(description = "Client name")
    @JsonProperty("client_name")
    private String clientName;

}
