import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsInt, Min } from "class-validator";
import { Type } from "class-transformer";

export class FindAllQueryDto {
  @ApiPropertyOptional({ description: "Number of results per page", example: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number;

  @ApiPropertyOptional({ description: "Page number", example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: "Search by name, address, or contact", example: "<PERSON>" })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: "Broker ID", example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  brokerId?: number;
}
