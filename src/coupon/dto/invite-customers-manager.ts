export type InviteCustomersManager = {
  isLinkEnabled: boolean;
  linkPattern?: string;
  isVoucherEnabled: boolean;
  couponPrefix?: string;
  minimumOrderValue: number;
  validity: {
    value?: number;
    type?: "month" | "year" | "weeks";
  };
  maximumReferrals: number;
  minimumOrderPeriod: {
    value?: number;
    type?: "month" | "year" | "weeks";
  };
  euLicenseDiscountType: string;
  directLicenseDiscountType: string;
  actionGuideDiscountType: string;
  carbonOffset?: {
    percentage: {
      value?: number;
      serviceType?: "month" | "year" | "weeks";
      amount?: number;
    };
    absoluteValue: {
      value?: number;
      serviceType?: "month" | "year" | "weeks";
      amount?: number;
    };
  };
};
