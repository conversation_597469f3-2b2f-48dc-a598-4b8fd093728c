package de.interzero.oneepr.admin.report_set;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.fraction_icon.FractionIcon;
import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetController;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetPriceListItemRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.*;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.ReportSetColumnFractionRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.admin.service_setup.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetController}.
 * This class validates the full HTTP request-response cycle for the report set module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private ReportSetFractionRepository reportSetFractionRepository;

    @Autowired
    private ReportSetColumnRepository reportSetColumnRepository;

    @Autowired
    private ReportSetColumnFractionRepository reportColumnFractionRepository;

    @Autowired
    private ReportSetPriceListRepository reportSetPriceListRepository;

    @Autowired
    private ReportSetPriceListItemRepository reportSetPriceListItemRepository;

    @Autowired
    private FilesRepository filesRepository;

    @Autowired
    private FractionIconRepository fractionIconRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private ReportSet testReportSet;
    private PackagingService testPackagingService;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency to avoid constraint violations
        reportSetColumnRepository.deleteAll();
        reportSetFractionRepository.deleteAll();
        reportColumnFractionRepository.deleteAll();
        reportSetPriceListRepository.deleteAll();
        reportSetPriceListItemRepository.deleteAll();
        reportSetRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();
        filesRepository.deleteAll();
        fractionIconRepository.deleteAll();

        // Create the required chain of entities
        Country country = createAndSaveTestCountry();
        testPackagingService = createAndSaveTestPackagingService(country);
        testReportSet = createAndSaveTestReportSet(testPackagingService);
    }

    /**
     * Verifies that a POST request creates a new report set successfully.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateNewReportSet() throws Exception {
        ReportSetCreateDto createDto = new ReportSetCreateDto();
        createDto.setPackagingServiceId(testPackagingService.getId());
        createDto.setName("New Test Report Set");
        createDto.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        createDto.setType(ReportSet.ReportSetType.FRACTIONS);

        mockMvc.perform(post(Api.REPORT_SETS)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Test Report Set")))
                .andExpect(jsonPath("$.mode", is("ON_PLATAFORM")))
                .andExpect(jsonPath("$.type", is("FRACTIONS")))
                .andExpect(jsonPath("$.packaging_service_id", is(testPackagingService.getId())));
    }

    /**
     * Verifies that a GET request returns all active report sets.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_shouldReturnListOfReportSets() throws Exception {
        ReportSetFindAllDto findAllDto = new ReportSetFindAllDto();
        findAllDto.setPackagingServiceId(testPackagingService.getId());

        mockMvc.perform(get(Api.REPORT_SETS)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(findAllDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testReportSet.getId())))
                .andExpect(jsonPath("$[0].name", is(testReportSet.getName())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_shouldReturnCorrectReportSet() throws Exception {
        mockMvc.perform(get(Api.REPORT_SETS + "/{id}", testReportSet.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testReportSet.getId())))
                .andExpect(jsonPath("$.name", is(testReportSet.getName())))
                .andExpect(jsonPath("$.mode", is(testReportSet.getMode().toString())))
                .andExpect(jsonPath("$.type", is(testReportSet.getType().toString())));
    }

    /**
     * Verifies that a GET request with an invalid ID format returns a 400 error.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_shouldReturn400ForInvalidId() throws Exception {
        mockMvc.perform(get(Api.REPORT_SETS + "/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Verifies that a PUT request updates a report set successfully.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldUpdateReportSet() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated Report Set Name");
        updateDto.setSheetFileDescription("Updated description");
        updateDto.setSheetFileId("123e4567-e89b-12d3-a456-426614174000");

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testReportSet.getId())))
                .andExpect(jsonPath("$.name", is("Updated Report Set Name")))
                .andExpect(jsonPath("$.sheet_file_description", is("Updated description")));
    }

    /**
     * Verifies that a PUT request with an invalid ID format returns a 400 error.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldReturn400ForInvalidId() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated Name");

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", "invalid-id")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Verifies that a POST request to duplicate endpoint creates a copy of the report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldDuplicateReportSet() throws Exception {
        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", testReportSet.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok", is(true)));
    }

    /**
     * Verifies that a POST request to duplicate with an invalid ID format returns a 400 error.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldReturn400ForInvalidId() throws Exception {
        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", "invalid-id"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes a report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_shouldSoftDeleteReportSet() throws Exception {
        mockMvc.perform(delete(Api.REPORT_SETS + "/{id}", testReportSet.getId()))
                .andExpect(status().isOk());

        // Verify it's gone from the "active" list via the API
        ReportSetFindAllDto findAllDto = new ReportSetFindAllDto();
        findAllDto.setPackagingServiceId(testPackagingService.getId());

        mockMvc.perform(get(Api.REPORT_SETS)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(findAllDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));

        // Verify in the database that the deleted_at field is now set
        ReportSet deletedReportSet = reportSetRepository.findById(testReportSet.getId())
                .orElseThrow();
        assertNotNull(deletedReportSet.getDeletedAt());
    }

    /**
     * Verifies that a DELETE request with an invalid ID format returns a 400 error.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_shouldReturn400ForInvalidId() throws Exception {
        mockMvc.perform(delete(Api.REPORT_SETS + "/{id}", "invalid-id"))
                .andExpect(status().isBadRequest());
    }

    /**
     * Verifies that update handles null collections gracefully.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandleNullCollections() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Null Collections Test");
        updateDto.setSheetFileId("non-existent-file-id");
        updateDto.setFractions(null);
        updateDto.setColumns(null);
        updateDto.setPriceLists(null);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Null Collections Test")));
    }


    /**
     * Verifies that update returns 404 for non-existent report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldReturn404ForNonExistentReportSet() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Non-existent Update");

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", 99999)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isNotFound());
    }

    /**
     * Verifies that duplicate works for a basic report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldWorkForBasicReportSet() throws Exception {
        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", testReportSet.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok", is(true)));

        // Verify that a new report set was created
        ReportSetFindAllDto findAllDto = new ReportSetFindAllDto();
        findAllDto.setPackagingServiceId(testPackagingService.getId());

        mockMvc.perform(get(Api.REPORT_SETS)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(findAllDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2))); // Original + duplicated
    }

    /**
     * Verifies that duplicate returns 404 for non-existent report set.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldReturn404ForNonExistentReportSet() throws Exception {
        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", 99999))
                .andExpect(status().isNotFound());
    }

    /**
     * Verifies that duplicate works with complex report set containing fractions and columns.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldWorkWithComplexReportSet() throws Exception {
        // Create test data
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();
        ReportSetFraction fraction = createAndSaveTestFraction("Test Fraction", testReportSet, fractionIcon);
        ReportSetColumn column = createAndSaveTestColumn("Test Column", testReportSet);
        ReportSetPriceList priceList = createAndSaveTestPriceList(testReportSet);
        testReportSet.getFractions().add(fraction);
        testReportSet.getColumns().add(column);
        testReportSet.getPriceLists().add(priceList);
        reportSetRepository.save(testReportSet);

        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", testReportSet.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok", is(true)));

        // Verify that fractions and columns were duplicated
        List<ReportSetFraction> allFractions = reportSetFractionRepository.findAll();
        List<ReportSetColumn> allColumns = reportSetColumnRepository.findAll();

        // Should have original + duplicated
        assert (allFractions.size() >= 2);
        assert (allColumns.size() >= 2);
    }

    /**
     * Verifies that update works with fractions containing valid data.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldWorkWithValidFractions() throws Exception {
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();

        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Fractions");

        // Create a fraction for the update
        ReportSetFractionCreateDto fraction = new ReportSetFractionCreateDto();
        fraction.setName("New Fraction");
        fraction.setDescription("New fraction description");
        fraction.setCode(UUID.randomUUID().toString());
        fraction.setFractionIconId(fractionIcon.getId());
        fraction.setLevel(1);
        fraction.setOrder(1);
        fraction.setIsActive(true);
        fraction.setIcon("test-icon");
        fraction.setHasSecondLevel(false);
        fraction.setHasThirdLevel(false);

        List<ReportSetFractionCreateDto> fractions = new ArrayList<>();
        fractions.add(fraction);
        updateDto.setFractions(fractions);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Fractions")));
    }

    /**
     * Verifies that update works with columns containing valid data.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldWorkWithValidColumns() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Columns");

        // Create a column for the update
        ReportSetColumnCreateDto column = new ReportSetColumnCreateDto();
        column.setName("New Column");
        column.setDescription("New column description");
        column.setCode(UUID.randomUUID().toString());
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setLevel(1);
        column.setOrder(1);

        List<ReportSetColumnCreateDto> columns = new ArrayList<>();
        columns.add(column);
        updateDto.setColumns(columns);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Columns")));
    }

    /**
     * Verifies that update handles invalid sheet file ID gracefully.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandleInvalidSheetFileId() throws Exception {
        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Invalid Sheet File Test");
        updateDto.setSheetFileId("non-existent-file-id");

        // This should not fail - invalid file ID should be handled gracefully
        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Invalid Sheet File Test")));
    }

    /**
     * Verifies that duplicate works with report set that has soft-deleted related entities.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void duplicate_shouldFilterOutSoftDeletedEntities() throws Exception {
        // Create test data with some soft-deleted entities
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();
        ReportSetFraction activeFraction = createAndSaveTestFraction("Active Fraction", testReportSet, fractionIcon);
        ReportSetFraction deletedFraction = createAndSaveTestFraction("Deleted Fraction", testReportSet, fractionIcon);

        testReportSet.getFractions().add(activeFraction);
        testReportSet.getFractions().add(deletedFraction);

        reportSetRepository.save(testReportSet);
        // Soft delete one fraction
        deletedFraction.setDeletedAt(Instant.now());
        reportSetFractionRepository.save(deletedFraction);

        mockMvc.perform(post(Api.REPORT_SETS + "/{id}/duplicate", testReportSet.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok", is(true)));

        // Verify that only active entities were duplicated
        List<ReportSetFraction> allFractions = reportSetFractionRepository.findAll();
        long activeFractions = allFractions.stream()
                .filter(f -> f.getDeletedAt() == null)
                .count();

        // Should have: original active + duplicated active (deleted should not be duplicated)
        assert (activeFractions == 2);
    }

    // --- Tests for Price List Coverage (!priceListIds.isEmpty() condition) ---

    /**
     * Verifies that update works with price lists that have IDs (covers !priceListIds.isEmpty() condition).
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandlePriceListsWithIds() throws Exception {
        // Create test data
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();
        ReportSetFraction fraction = createAndSaveTestFraction("Test Fraction", testReportSet, fractionIcon);
        ReportSetPriceList priceList = createAndSaveTestPriceList(testReportSet);

        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Price Lists");

        // Create fractions for the update
        ReportSetFractionCreateDto fractionDto = new ReportSetFractionCreateDto();
        fractionDto.setCode(fraction.getCode());
        fractionDto.setName("Updated Fraction");
        fractionDto.setDescription("Updated description");
        fractionDto.setFractionIconId(fractionIcon.getId());
        fractionDto.setLevel(1);
        fractionDto.setOrder(1);
        fractionDto.setIsActive(true);
        fractionDto.setIcon("test-icon");
        fractionDto.setHasSecondLevel(false);
        fractionDto.setHasThirdLevel(false);

        List<ReportSetFractionCreateDto> fractions = new ArrayList<>();
        fractions.add(fractionDto);
        updateDto.setFractions(fractions);

        // Create price lists with IDs (this triggers the !priceListIds.isEmpty() condition)
        ReportSetPriceListDto priceListDto = new ReportSetPriceListDto();
        priceListDto.setId(priceList.getId()); // This is the key - having an ID
        priceListDto.setTitle("Updated Price List");
        priceListDto.setType(ReportSetPriceList.Type.FIXED_PRICE);
        priceListDto.setStartDate(LocalDate.now());
        priceListDto.setEndDate(LocalDate.now().plusYears(1));
        priceListDto.setFixedPrice(1000);

        List<ReportSetPriceListDto> priceLists = new ArrayList<>();
        priceLists.add(priceListDto);
        updateDto.setPriceLists(priceLists);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Price Lists")));
    }

    /**
     * Verifies that update works with columns that have fractions (covers column fractions condition).
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandleColumnsWithFractions() throws Exception {
        // Create test data
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();
        ReportSetFraction fraction = createAndSaveTestFraction("Test Fraction", testReportSet, fractionIcon);
        ReportSetColumn column = createAndSaveTestColumn("Test Column", testReportSet);

        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Column Fractions");

        // Create fractions for the update
        ReportSetFractionCreateDto fractionDto = new ReportSetFractionCreateDto();
        fractionDto.setCode(fraction.getCode());
        fractionDto.setName("Updated Fraction");
        fractionDto.setDescription("Updated description");
        fractionDto.setFractionIconId(fractionIcon.getId());
        fractionDto.setLevel(1);
        fractionDto.setOrder(1);
        fractionDto.setIsActive(true);
        fractionDto.setIcon("test-icon");
        fractionDto.setHasSecondLevel(false);
        fractionDto.setHasThirdLevel(false);

        List<ReportSetFractionCreateDto> fractions = new ArrayList<>();
        fractions.add(fractionDto);
        updateDto.setFractions(fractions);

        // Create columns with fractions
        ReportSetColumnCreateDto columnDto = new ReportSetColumnCreateDto();
        columnDto.setCode(column.getCode());
        columnDto.setName("Updated Column");
        columnDto.setDescription("Updated description");
        columnDto.setUnitType(ReportSetColumn.UnitType.KG);
        columnDto.setLevel(1);
        columnDto.setOrder(1);

        // Create column fractions
        ReportSetColumnFractionDetailDto columnFraction = new ReportSetColumnFractionDetailDto();
        columnFraction.setColumnCode(column.getCode());
        columnFraction.setFractionCode(fraction.getCode());

        List<ReportSetColumnFractionDetailDto> columnFractions = new ArrayList<>();
        columnFractions.add(columnFraction);
        columnDto.setFractions(columnFractions);

        List<ReportSetColumnCreateDto> columns = new ArrayList<>();
        columns.add(columnDto);
        updateDto.setColumns(columns);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Column Fractions")));
    }

    /**
     * Verifies that update handles fractions with parent IDs (covers parent fraction condition).
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandleFractionsWithParentIds() throws Exception {
        // Create test data
        FractionIcon fractionIcon = createAndSaveTestFractionIcon();
        ReportSetFraction parentFraction = createAndSaveTestFraction("Parent Fraction", testReportSet, fractionIcon);
        ReportSetFraction childFraction = createAndSaveTestFraction("Child Fraction", testReportSet, fractionIcon);
        childFraction.setParentId(parentFraction.getId());
        childFraction = reportSetFractionRepository.save(childFraction);

        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Parent Fractions");

        // Create fractions with parent IDs
        ReportSetFractionCreateDto parentDto = new ReportSetFractionCreateDto();
        parentDto.setCode(parentFraction.getCode());
        parentDto.setName("Updated Parent");
        parentDto.setDescription("Updated parent description");
        parentDto.setFractionIconId(fractionIcon.getId());
        parentDto.setLevel(1);
        parentDto.setOrder(1);
        parentDto.setIsActive(true);
        parentDto.setIcon("test-icon");
        parentDto.setHasSecondLevel(true);
        parentDto.setHasThirdLevel(false);

        ReportSetFractionCreateDto childDto = new ReportSetFractionCreateDto();
        childDto.setCode(childFraction.getCode());
        childDto.setName("Updated Child");
        childDto.setDescription("Updated child description");
        childDto.setFractionIconId(fractionIcon.getId());
        childDto.setParentId(parentFraction.getId()); // This covers the parent ID condition
        childDto.setLevel(2);
        childDto.setOrder(1);
        childDto.setIsActive(true);
        childDto.setIcon("test-icon");
        childDto.setHasSecondLevel(false);
        childDto.setHasThirdLevel(false);

        List<ReportSetFractionCreateDto> fractions = new ArrayList<>();
        fractions.add(parentDto);
        fractions.add(childDto);
        updateDto.setFractions(fractions);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Parent Fractions")));
    }

    /**
     * Verifies that update handles columns with parent IDs (covers parent column condition).
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldHandleColumnsWithParentIds() throws Exception {
        // Create test data
        ReportSetColumn parentColumn = createAndSaveTestColumn("Parent Column", testReportSet);
        ReportSetColumn childColumn = createAndSaveTestColumn("Child Column", testReportSet);
        childColumn.setParentId(parentColumn.getId());
        childColumn = reportSetColumnRepository.save(childColumn);

        UpdateReportSetDto updateDto = new UpdateReportSetDto();
        updateDto.setName("Updated with Parent Columns");

        // Create columns with parent IDs
        ReportSetColumnCreateDto parentDto = new ReportSetColumnCreateDto();
        parentDto.setCode(parentColumn.getCode());
        parentDto.setName("Updated Parent Column");
        parentDto.setDescription("Updated parent column description");
        parentDto.setUnitType(ReportSetColumn.UnitType.KG);
        parentDto.setLevel(1);
        parentDto.setOrder(1);

        ReportSetColumnCreateDto childDto = new ReportSetColumnCreateDto();
        childDto.setCode(childColumn.getCode());
        childDto.setName("Updated Child Column");
        childDto.setDescription("Updated child column description");
        childDto.setUnitType(ReportSetColumn.UnitType.KG);
        childDto.setParentId(parentColumn.getId()); // This covers the parent ID condition
        childDto.setLevel(2);
        childDto.setOrder(1);

        List<ReportSetColumnCreateDto> columns = new ArrayList<>();
        columns.add(parentDto);
        columns.add(childDto);
        updateDto.setColumns(columns);

        mockMvc.perform(put(Api.REPORT_SETS + "/{id}", testReportSet.getId())
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Updated with Parent Columns")));
    }


    // --- Helper Methods for Test Setup ---

    /**
     * Creates and saves a test country entity.
     */
    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        return countryRepository.saveAndFlush(country);
    }

    /**
     * Creates and saves a test packaging service entity.
     */
    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Packaging Service");
        service.setDescription("A test packaging service for report sets");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    /**
     * Creates and saves a test report set entity.
     */
    private ReportSet createAndSaveTestReportSet(PackagingService packagingService) {
        ReportSet reportSet = new ReportSet();
        reportSet.setName("Test Report Set");
        reportSet.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        reportSet.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSet.setPackagingService(packagingService);
        reportSet.setCreatedAt(Instant.now());
        reportSet.setUpdatedAt(Instant.now());
        return reportSetRepository.saveAndFlush(reportSet);
    }

    /**
     * Creates and saves a test fraction icon entity.
     */
    private FractionIcon createAndSaveTestFractionIcon() {
        // First create a test file
        Files file = new Files();
        file.setId(UUID.randomUUID().toString());
        file.setName("test-icon.svg");
        file.setOriginalName("test-icon.svg");
        file.setExtension("svg");
        file.setSize("1024");
        file.setCreatorType("SYSTEM");
        file.setDocumentType("ICON");
        file.setCreatedAt(Instant.now());
        file = filesRepository.saveAndFlush(file);

        // Create fraction icon
        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setFile(file);
        fractionIcon.setImageUrl("www.test.com");
        return fractionIconRepository.saveAndFlush(fractionIcon);
    }

    /**
     * Creates and saves a test report set fraction entity.
     */
    private ReportSetFraction createAndSaveTestFraction(String name, ReportSet reportSet, FractionIcon fractionIcon) {
        ReportSetFraction fraction = new ReportSetFraction();
        fraction.setName(name);
        fraction.setDescription("Description for " + name);
        fraction.setCode(UUID.randomUUID().toString());
        fraction.setReportSet(reportSet);
        fraction.setFractionIcon(fractionIcon);
        fraction.setLevel(1);
        fraction.setOrder(1);
        fraction.setIsActive(true);
        fraction.setIcon("test-icon");
        fraction.setHasSecondLevel(false);
        fraction.setHasThirdLevel(false);
        fraction.setCreatedAt(Instant.now());
        fraction.setUpdatedAt(Instant.now());
        return reportSetFractionRepository.saveAndFlush(fraction);
    }

    /**
     * Creates and saves a test report set column entity.
     */
    private ReportSetColumn createAndSaveTestColumn(String name, ReportSet reportSet) {
        ReportSetColumn column = new ReportSetColumn();
        column.setName(name);
        column.setDescription("Description for " + name);
        column.setCode(UUID.randomUUID().toString());
        column.setReportSet(reportSet);
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setLevel(1);
        column.setOrder(1);
        column.setCreatedAt(Instant.now());
        column.setUpdatedAt(Instant.now());
        return reportSetColumnRepository.saveAndFlush(column);
    }

    /**
     * Creates and saves a test report set price list entity.
     */
    private ReportSetPriceList createAndSaveTestPriceList(ReportSet reportSet) {
        ReportSetPriceList priceList = new ReportSetPriceList();
        priceList.setTitle("Test Price List");
        priceList.setType(ReportSetPriceList.Type.FIXED_PRICE);
        priceList.setReportSet(reportSet);
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plusSeconds(86400));
        priceList.setFixedPrice(1000);
        priceList.setCreatedAt(Instant.now());
        priceList.setUpdatedAt(Instant.now());
        return reportSetPriceListRepository.saveAndFlush(priceList);
    }

}
