import { Test, TestingModule } from "@nestjs/testing";
import { ConsentService } from "./consent.service";
import { DatabaseService } from "@/database/database.service";
import { CreateConsentDto } from "./dto/create-consent";
import { UpdateConsentDto } from "./dto/update-consent";
import { Consent, ConsentType } from "@prisma/client";
import { NotFoundException } from "@nestjs/common";

describe("ConsentService", () => {
  let service: ConsentService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    consent: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConsentService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<ConsentService>(ConsentService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("create", () => {
    it("Should create a new consent", async () => {
      const createDto: CreateConsentDto = {
        name: "Terms of Use",
        type: ConsentType.ACCOUNT,
        description: "Platform Terms of Use",
      };

      const expectedResult: Consent = {
        id: 1,
        ...createDto,
        version: 1,
        name: "name",
        description: "description",
        type: ConsentType.ACCOUNT,
        deleted_at: null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockDatabaseService.consent.create.mockResolvedValue(expectedResult);

      const result = await service.create(createDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.consent.create).toHaveBeenCalledWith({
        data: {
          ...createDto,
          version: 1,
          created_at: expect.any(Date),
          updated_at: expect.any(Date),
        },
      });
    });
  });

  describe("findAll", () => {
    it("Should return all consents", async () => {
      const expectedConsents: Consent[] = [
        {
          id: 1,
          name: "Terms of use",
          type: ConsentType.ACCOUNT,
          description: "Platform Terms of Use",
          version: 1,
          created_at: new Date(),
          deleted_at: null,
          updated_at: new Date(),
        },
        {
          id: 2,
          name: "Privacy Policy",
          type: ConsentType.PURCHASE,
          description: "Platform Privacy Policy",
          version: 1,
          created_at: new Date(),
          deleted_at: null,
          updated_at: new Date(),
        },
      ];

      mockDatabaseService.consent.findMany.mockResolvedValue(expectedConsents);

      const result = await service.findAll();

      expect(result).toEqual(expectedConsents);
      expect(mockDatabaseService.consent.findMany).toHaveBeenCalled();
    });
  });

  describe("findOne", () => {
    it("Should return specific consent", async () => {
      const expectedConsent: Consent = {
        id: 1,
        name: "Terms of use",
        type: ConsentType.ACCOUNT,
        description: "Platform Terms of Use",
        version: 1,
        deleted_at: null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockDatabaseService.consent.findUnique.mockResolvedValue(expectedConsent);

      const result = await service.findOne(1);

      expect(result).toEqual(expectedConsent);
      expect(mockDatabaseService.consent.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });
  });

  describe("update", () => {
    it("Should update a consent and increment the version", async () => {
      const updateDto: UpdateConsentDto = {
        name: "Updated Terms of Use",
        type: ConsentType.ACCOUNT,
        description: "Platform Terms of Use",
      };

      const currentConsent = {
        version: 1,
      };

      const expectedResult: Consent = {
        id: 1,
        ...updateDto,
        version: 2,
        type: ConsentType.ACCOUNT,
        deleted_at: null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockDatabaseService.consent.findUnique.mockResolvedValue(currentConsent);
      mockDatabaseService.consent.update.mockResolvedValue(expectedResult);

      const result = await service.update(1, updateDto);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.consent.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          ...updateDto,
          version: 2,
          updated_at: expect.any(Date),
        },
      });
    });

    it("Should throw NotFoundException when consent does not exist", async () => {
      mockDatabaseService.consent.findUnique.mockResolvedValue(null);

      const updateDto: UpdateConsentDto = {
        name: "Updated Terms of Use",
        type: ConsentType.ACCOUNT,
        description: "Updated platform terms of use",
      };

      await expect(service.update(999, updateDto)).rejects.toThrow(
        new NotFoundException("Consent with ID 999 not found")
      );
    });
  });

  describe("remove", () => {
    it("Should remove a consent", async () => {
      const expectedResult: Consent = {
        id: 1,
        name: "Terms of use",
        type: ConsentType.ACCOUNT,
        description: "Platform Terms of Use",
        version: 1,
        deleted_at: null,
        created_at: new Date(),
        updated_at: new Date(),
      };

      mockDatabaseService.consent.delete.mockResolvedValue(expectedResult);

      const result = await service.remove(1);

      expect(result).toEqual(expectedResult);
      expect(mockDatabaseService.consent.delete).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });
  });

  describe("findManyByType", () => {
    it("Should return consents by type", async () => {
      const expectedConsents: Consent[] = [
        {
          id: 1,
          name: "Terms of use",
          type: ConsentType.ACCOUNT,
          description: "Platform Terms of Use",
          version: 1,
          deleted_at: null,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      mockDatabaseService.consent.findMany.mockResolvedValue(expectedConsents);

      const result = await service.findManyByType(ConsentType.ACCOUNT);

      expect(result).toEqual(expectedConsents);
      expect(mockDatabaseService.consent.findMany).toHaveBeenCalledWith({
        where: { type: ConsentType.ACCOUNT },
      });
    });
  });
});
