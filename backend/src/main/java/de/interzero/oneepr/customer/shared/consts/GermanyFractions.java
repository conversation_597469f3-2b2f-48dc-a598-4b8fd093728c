package de.interzero.oneepr.customer.shared.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public final class GermanyFractions {

    private GermanyFractions() {
        throw new IllegalStateException("Utility class");
    }

    @Getter
    @AllArgsConstructor
    public static class FractionData {

        private String key;

        private String code;

        private String name;

        private BigDecimal resourceSavingFactor;

        private BigDecimal greenhouseGasesFactor;
    }

    private static final List<FractionData> FRACTIONS = List.of(
            new FractionData("GLASS", "A7B2X", "Glass", new BigDecimal("2.09"), new BigDecimal("0.00027")),
            new FractionData(
                    "PAPER",
                    "K9P4M",
                    "Paper / Paperboard / Cardboard (PPK)",
                    new BigDecimal("3.68"),
                    new BigDecimal("0.00029")),
            new FractionData(
                    "FERROUS_METALS",
                    "W3N8L",
                    "Ferrous Metals",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")),
            new FractionData(
                    "ALUMINIUM",
                    "R5T9V",
                    "Aluminium incl. Composites",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")),
            new FractionData(
                    "LIQUID_COMPOSITES",
                    "H6Y4Z",
                    "Liquid Composites",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")),
            new FractionData(
                    "OTHER_COMPOSITES_BASED_ON_PPK",
                    "Q2C7D",
                    "Other Composites based on PPK",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")),
            new FractionData(
                    "PLASTICS_INCL_COMPOSITES",
                    "J8F3S",
                    "Plastics incl. Composites",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")),
            new FractionData(
                    "OTHER_MATERIALS",
                    "M1G5B",
                    "Other Materials",
                    new BigDecimal("5"),
                    new BigDecimal("0.00067")));

    public static Optional<FractionData> findByCode(String code) {
        return FRACTIONS.stream().filter(f -> f.getCode().equals(code)).findFirst();
    }
}