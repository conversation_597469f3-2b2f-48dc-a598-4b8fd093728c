package de.interzero.oneepr.customer.recommend_country;

import de.interzero.oneepr.customer.recommend_country.dto.OriginalCountDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RecommendCountryRepository extends JpaRepository<RecommendedCountry, Integer> {

    List<RecommendedCountry> findByCustomer_Id(Integer customerId);

    /**
     * Counts recommendations grouped by country name.
     * This query constructs {@link OriginalCountDto} objects directly from the query results.
     *
     * @return A list of {@link OriginalCountDto} where each DTO contains a country name
     * and the corresponding count of recommendations.
     */
    @Query(
            "SELECT new de.interzero.oneepr.customer.recommend_country.dto.OriginalCountDto(rc.name, COUNT(rc)) " + "FROM RecommendedCountry rc " + "GROUP BY rc.name "
    )
    List<OriginalCountDto> countRecommendationsGroupByName();
}
