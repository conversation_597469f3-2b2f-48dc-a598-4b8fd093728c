package de.interzero.oneepr.customer.customer_consent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Fields required to create a new customer consent")
@Data
public class CreateCustomerConsentDto {

    @JsonProperty("customer_id")
    @Schema(description = "The unique identifier of the customer")
    private Integer customerId;

    @JsonProperty("consent_id")
    @Schema(description = "The unique identifier of the consent definition")
    private Integer consentId;

    @JsonProperty("given")
    @Schema(description = "Flag indicating whether the consent was given by the customer")
    private Boolean given;
}
