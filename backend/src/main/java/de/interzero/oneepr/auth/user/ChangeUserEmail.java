package de.interzero.oneepr.auth.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(
        name = "change_user_email",
        schema = "public"
)
public class ChangeUserEmail {

    @JsonProperty("id")
    @Id
    @Column(
            name = "id",
            columnDefinition = "uuid"
    )
    private UUID id = UUID.randomUUID();

    @JsonProperty("new_email")
    @Column(
            name = "new_email",
            nullable = false
    )
    private String newEmail;

    @JsonProperty("token")
    @Column(
            name = "token",
            nullable = false
    )
    private String token;

    @JsonProperty("user")
    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "user_id",
            nullable = false
    )
    private User user;

    @Transient
    @JsonProperty("user_id")
    public Integer getUserId() {
        return user != null ? user.getId() : null;
    }

    @JsonProperty("created_at")
    @Column(name = "created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Column(name = "updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    @Column(name = "deleted_at")
    private Instant deletedAt;

    @PrePersist
    public void prePersist() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }
}
