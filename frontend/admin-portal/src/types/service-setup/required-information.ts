export interface RequiredInformation {
  id: number;
  country_id: number;
  type: RequiredInformationType;
  name: string;
  description: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  file_id: string | null;
  has_criteria?: boolean;
  packaging_service_ids: number[];
}

export type RequiredInformationType = "TEXT" | "NUMBER" | "DOCUMENT" | "FILE" | "IMAGE";

export type CreateRequiredInformation = Omit<RequiredInformation, "id" | "created_at" | "updated_at" | "deleted_at">;

export type UpdateRequiredInformation = Partial<
  Omit<RequiredInformation, "id" | "country_id" | "created_at" | "updated_at" | "deleted_at">
>;
