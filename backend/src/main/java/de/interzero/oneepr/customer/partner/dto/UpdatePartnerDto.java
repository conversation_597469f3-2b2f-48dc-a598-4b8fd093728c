package de.interzero.oneepr.customer.partner.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Schema(description = "Fields required to update a partner")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePartnerDto extends BaseDto {

    @Schema(description = "First name of the partner")
    @Size(min = 1)
    @JsonProperty("partner_firstname")
    private String partnerFirstname;

    @Schema(description = "Last name of the partner")
    @Size(min = 1)
    @JsonProperty("partner_lastname")
    private String partnerLastname;

    @Schema(description = "Email address of the partner")
    @Size(min = 1)
    @JsonProperty("partner_email")
    private String partnerEmail;

    @Schema(description = "Partner banking information")
    @JsonProperty("banking")
    private CreatePartnerBankingDto banking;

    @Schema(description = "Partner company data")
    @JsonProperty("company")
    private CreatePartnerCompanyDto company;

    @Schema(description = "No provision negotiated flag")
    @JsonProperty("no_provision_negotiated")
    private Boolean noProvisionNegotiated;

    @Schema(description = "Payout cycle")
    @JsonProperty("payout_cycle")
    private String payoutCycle;

    @Schema(description = "Commission mode")
    @JsonProperty("commission_mode")
    private String commissionMode;

    @Schema(description = "List of coupon IDs")
    @JsonProperty("coupons")
    private List<Integer> coupons;

    @Schema(description = "Marketing material ID")
    @JsonProperty("marketing_material_id")
    private Integer marketingMaterialId;

    @Schema(description = "Marketing material name")
    @JsonProperty("new_marketing_material_name")
    private String newMarketingMaterialName;

    @Schema(description = "Contract file")
    @JsonProperty("contract_file")
    private MultipartFile contractFile;

    @Schema(description = "Marketing material files")
    @JsonProperty("new_marketing_material_files")
    private List<MultipartFile> newMarketingMaterialFiles;
}
