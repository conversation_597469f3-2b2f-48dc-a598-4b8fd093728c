import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FractionIcon } from "@/components/ui/fraction-icon";
import { FractionInput } from "@/components/ui/fraction-input";
import { Skeleton } from "@/components/ui/skeleton";
import { getServiceSetupReportSet } from "@/lib/api/service-setups";
import { Question } from "@interzero/oneepr-react-ui";
import { useQuery } from "@tanstack/react-query";
import { AlertCircle, X } from "lucide-react";

interface PreviewCalculatorDialogProps {
  countryCode: string;
  reportSetId: number;
  children: React.ReactNode;
}

export function PreviewCalculatorDialog({ countryCode, reportSetId, children }: PreviewCalculatorDialogProps) {
  const { data: reportSet, isLoading } = useQuery({
    queryKey: ["service-setup-report-set", countryCode, reportSetId],
    queryFn: () => getServiceSetupReportSet(countryCode, reportSetId),
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className="bg-surface-01 py-9 max-w-2xl"
        closeIcon={<X className="size-7 stroke-[2.5px] p-1 rounded-3xl bg-white stroke-primary" />}
      >
        <DialogHeader className="mb-10">
          <DialogTitle>Preview calculator</DialogTitle>
          <DialogDescription className="!mt-4">
            Check how the questions will be set for the user to answer
          </DialogDescription>
        </DialogHeader>
        <div className="bg-surface-03 rounded-4xl p-8 flex flex-col gap-5">
          <p className="text-primary text-2xl font-bold flex flex-row items-center gap-5">
            {reportSet?.packaging_service.name}
            <Question className="size-4" />
          </p>
          <span className="text-sm text-tonal-dark-cream-30">
            Please provide the estimate quantity so we are able to estimate the future costs
          </span>
          {isLoading && (
            <div className="rounded-[20px] bg-tonal-dark-cream-80 overflow-hidden space-y-[1px]">
              {isLoading &&
                [...Array(5)].map((_, index) => (
                  <div key={index} className="bg-background px-5 py-4 flex items-center gap-4">
                    <Skeleton className="w-5 h-5 rounded-full" />
                    <Skeleton className="w-8 h-8" />
                    <Skeleton className="w-24 h-6" />
                    <Skeleton className="w-20 h-8" />
                    <Skeleton className="w-6 h-6" />
                  </div>
                ))}
            </div>
          )}
          {reportSet && !!reportSet.fractions.length && (
            <div className="rounded-[20px] bg-tonal-dark-cream-80 overflow-hidden space-y-[1px]">
              {reportSet.fractions.map((fraction) => (
                <div key={fraction.id} className="bg-background px-5 py-4 flex items-center gap-4">
                  <Question className="size-4" />
                  <FractionIcon size="medium" iconUrl={fraction.fraction_icon.image_url} />
                  <p className="text-primary font-bold flex-1">{fraction.name}</p>
                  <FractionInput type="weight" suffix="" placeholder="0.000,00" />
                  <span className="text-primary">kg</span>
                </div>
              ))}
            </div>
          )}
          {!isLoading && !reportSet?.fractions.length && (
            <div className="py-4 flex items-center gap-2">
              <AlertCircle className="size-5 text-error stroke-white" />
              <p className="text-error  text-sm">No fractions registered yet!</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
