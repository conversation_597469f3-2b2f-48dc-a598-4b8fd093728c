import { ApiProperty } from "@nestjs/swagger";

export class VatIdDto {
  @ApiProperty()
  vat_id: string;
  @ApiProperty()
  country_code: string;
  @ApiProperty()
  company_name: string;
  @ApiProperty()
  company_zipcode: string;
  @ApiProperty()
  company_city: string;
  @ApiProperty()
  company_street: string;
}

export interface ICheckCountryGermany {
  valid: boolean;
  countryCode: string;
  vatNumber: string;
  name: string;
  address: string;
  requestDate: string;
  requestIdentifier: string;
  traderName: string;
  traderStreet: string;
  traderPostalCode: string;
  traderCity: string;
  traderCompanyType: string;
  traderNameMatch: string;
  traderStreetMatch: string;
  traderPostalCodeMatch: string;
  traderCityMatch: string;
  traderCompanyTypeMatch: string;
}
