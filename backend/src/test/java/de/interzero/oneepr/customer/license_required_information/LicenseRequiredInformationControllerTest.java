package de.interzero.oneepr.customer.license_required_information;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.decline.DeclineRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_required_information.dto.CreateLicenseRequiredInformationDto;
import de.interzero.oneepr.customer.license_required_information.dto.DeclineLicenseRequiredInformationDto;
import de.interzero.oneepr.customer.license_required_information.dto.UpdateLicenseRequiredInformationDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link LicenseRequiredInformationController}, focusing on successful "happy path" scenarios.
 * This test class validates the full HTTP request-response cycle.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class LicenseRequiredInformationControllerTest {

    private static final String API_URL = Api.REQUIRED_INFORMATIONS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private LicenseRequiredInformationRepository licenseRequiredInformationRepository;

    @Autowired
    private DeclineRepository declineRepository;

    private License testLicense;

    private Reason testReason;

    private Contract testContract;

    private Customer testCustomer;

    private LicenseRequiredInformation testInfo;

    @MockBean
    private EmailOutboxGateway emailOutboxGateway;

    // An ArgumentCaptor to capture the object sent to the mock bean
    private ArgumentCaptor<EmailMessage> emailMessageCaptor = ArgumentCaptor.forClass(EmailMessage.class);

    /**
     * Sets up a consistent database state before each test method runs.
     * This method creates all necessary entities for the tests.
     */
    @BeforeEach
    void setUp() {
        Customer customer = new Customer();
        customer.setUserId(101);
        customer.setFirstName("Test");
        customer.setLastName("User");
        customer.setEmail("testuser." + Instant.now().toEpochMilli() + "@example.com");
        customer.setCreatedAt(Instant.now());
        customer.setUpdatedAt(Instant.now());
        customer = customerRepository.save(customer);
        testCustomer = customer;

        testContract = new Contract();
        testContract.setCustomer(customer);
        testContract.setTitle("Test Contract");
        testContract.setStartDate(Instant.now());
        testContract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract = contractRepository.save(testContract);

        testLicense = new License();
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setRegistrationNumber("REG-" + System.currentTimeMillis());
        testLicense.setCountryCode("DE");
        testLicense.setCountryFlag("🇩🇪");
        testLicense.setCountryId(1);
        testLicense.setYear(java.time.Year.now().getValue());
        testLicense.setStartDate(java.time.Instant.now());
        testLicense.setCreatedAt(java.time.Instant.now());
        testLicense.setUpdatedAt(java.time.Instant.now());
        testLicense = licenseRepository.save(testLicense);

        testReason = new Reason();
        testReason.setTitle("Test Decline Reason");
        testReason.setValue("test_decline");
        testReason.setType(Reason.Type.LICENSE_INFORMATION);
        testReason.setCreatedAt(Instant.now());
        testReason.setUpdatedAt(Instant.now());
        testReason = reasonRepository.save(testReason);

        testInfo = new LicenseRequiredInformation();
        testInfo.setName("Test Information");
        testInfo.setDescription("A test description.");
        testInfo.setLicense(testLicense);
        testInfo.setContract(testContract);
        testInfo.setKind(LicenseRequiredInformation.Kind.REQUIRED_INFORMATION);
        testInfo.setStatus(LicenseRequiredInformation.Status.OPEN);
        testInfo.setType(LicenseRequiredInformation.Type.TEXT);
        testInfo.setCreatedAt(Instant.now());
        testInfo.setUpdatedAt(Instant.now());
        testInfo = licenseRequiredInformationRepository.save(testInfo);
    }

    /**
     * Verifies that all required information for a specific license can be retrieved.
     */
    @Test
    @WithMockUser
    void findAll_byLicenseId_shouldReturnInformationList() throws Exception {
        mockMvc.perform(get(API_URL + "?license_id=" + testLicense.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testInfo.getId())));
    }

    /**
     * Verifies that a specific required information record can be retrieved by its ID.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnRequiredInformation() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", testInfo.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testInfo.getId())))
                .andExpect(jsonPath("$.name", is("Test Information")));
    }

    /**
     * Verifies that a new required information record can be successfully created.
     */
    @Test
    @WithMockUser
    void create_shouldCreateNewInformation() throws Exception {
        CreateLicenseRequiredInformationDto createDto = new CreateLicenseRequiredInformationDto();
        createDto.setName("New Required Info");
        createDto.setDescription("Details for the new info.");
        createDto.setLicenseId(testLicense.getId());
        createDto.setSetupRequiredInformationId(123);
        createDto.setKind(LicenseRequiredInformation.Kind.REQUIRED_INFORMATION);
        createDto.setStatus(LicenseRequiredInformation.Status.NEW);
        createDto.setType(LicenseRequiredInformation.Type.TEXT);

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Required Info")));
    }

    /**
     * Verifies that an existing required information record can be successfully updated.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingInformation() throws Exception {
        UpdateLicenseRequiredInformationDto updateDto = new UpdateLicenseRequiredInformationDto();
        updateDto.setAnswer("This is the answer.");
        updateDto.setStatus(LicenseRequiredInformation.Status.DONE);

        mockMvc.perform(put(API_URL + "/{id}", testInfo.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testInfo.getId())))
                .andExpect(jsonPath("$.answer", is("This is the answer.")))
                .andExpect(jsonPath("$.status", is("DONE")));
    }

    /**
     * Verifies that a required information record can be successfully declined,
     * creating a decline record and triggering an email notification.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void decline_shouldUpdateStatusAndTriggerEmail() throws Exception {
        // --- Arrange ---
        DeclineLicenseRequiredInformationDto declineDto = new DeclineLicenseRequiredInformationDto();
        declineDto.setTitle("Not Applicable");
        declineDto.setReasonIds(List.of(testReason.getId()));

        // --- Act & Assert ---
        mockMvc.perform(post(API_URL + "/{id}/decline", testInfo.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isOk())
                // Enhance assertions to check the new, deeper response structure
                .andExpect(jsonPath("$.id", is(testInfo.getId())))
                .andExpect(jsonPath("$.status", is("DECLINED")))
                .andExpect(jsonPath("$.contract_id", is(testContract.getId())))
                .andExpect(jsonPath("$.decline").exists());

        // --- Verify ---
        // 1. Verify that the email gateway was called exactly once.
        verify(emailOutboxGateway, times(1)).sendEmail(emailMessageCaptor.capture());

        // 2. Inspect the captured email message to ensure its contents are correct.
        EmailMessage sentEmail = emailMessageCaptor.getValue();
        assertEquals("10", sentEmail.getTransactionalMessageId());
        assertEquals(testCustomer.getEmail(), sentEmail.getTo());
        assertEquals("Document declined", sentEmail.getSubject());
        assertEquals(testInfo.getName(), sentEmail.getMessageData().get("required_information_title"));

        // 3. Verify that a Decline entity was created and linked in the database.
        assertTrue(declineRepository.findByLicenseRequiredInformation_Id(testInfo.getId()).isPresent());
    }

    /**
     * Verifies that a required information record can be successfully soft-deleted.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void remove_shouldSoftDeleteInformation() throws Exception {
        mockMvc.perform(delete(API_URL + "/{id}", testInfo.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deleted_at").exists());

        // Verify in the database that the entity is soft-deleted
        LicenseRequiredInformation deletedInfo = licenseRequiredInformationRepository.findById(testInfo.getId())
                .orElseThrow();
        assertNotNull(deletedInfo.getDeletedAt());
    }
}
