package de.interzero.oneepr.customer.company_email;

import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.company_email.dto.CreateCompanyEmailDto;
import de.interzero.oneepr.customer.company_email.dto.UpdateCompanyEmailDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(Api.COMPANY_EMAIL)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class CompanyEmailController {

    private final CompanyEmailService companyEmailService;

    @Operation(summary = "Create a new CompanyEmail")
    @PostMapping
    @ApiResponses(
            value = {@ApiResponse(
                    responseCode = "201",
                    description = "The CompanyEmail has been successfully created.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CompanyEmail.class)
                    )
            )}
    )
    @ResponseStatus(HttpStatus.CREATED)
    public CompanyEmail create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateCompanyEmailDto.class)
            )
    ) @RequestBody CreateCompanyEmailDto dto) {
        return companyEmailService.create(dto);
    }


    @Operation(summary = "Get all Company Emails")
    @ApiResponses(
            value = {@ApiResponse(
                    responseCode = "200",
                    description = "List of Company Emails"
            )}
    )
    @GetMapping
    @PreAuthorize("permitAll()")
    public List<CompanyEmail> findAll() {
        return companyEmailService.findAll();
    }


    @Operation(summary = "Get CompanyEmail by id")
    @ApiResponses(
            value = {@ApiResponse(
                    responseCode = "200",
                    description = "The Company Email details",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CompanyEmail.class)
                    )
            )}
    )
    @GetMapping("{id}")
    public CompanyEmail findOne(@Parameter(description = "ID of consent to get") @PathVariable String id) {
        return companyEmailService.findOne(Integer.parseInt(id));
    }


    @Operation(summary = "Update Company Email by id")
    @ApiResponses(
            value = {@ApiResponse(
                    responseCode = "200",
                    description = "The Company Email has been successfully updated.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CompanyEmail.class)
                    )
            )}
    )
    @PutMapping("{id}")
    public CompanyEmail update(@Parameter(description = "ID of companyEmail to update") @PathVariable String id,
                               @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                       required = true,
                                       content = @Content(
                                               mediaType = "application/json",
                                               schema = @Schema(implementation = UpdateCompanyEmailDto.class)
                                       )
                               ) @RequestBody UpdateCompanyEmailDto dto) {
        return companyEmailService.update(Integer.parseInt(id), dto);
    }

    @Operation(summary = "Delete CompanyEmail by id")
    @ApiResponses(
            value = {@ApiResponse(
                    responseCode = "200",
                    description = "The CompanyEmail has been successfully deleted."
            )}
    )
    @DeleteMapping("{id}")
    public CompanyEmail remove(@Parameter(description = "ID of companyEmail to delete") @PathVariable String id) {
        return companyEmailService.remove(Integer.parseInt(id));
    }

}
