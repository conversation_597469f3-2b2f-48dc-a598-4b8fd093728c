import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { ReportSetPriceListType } from "@prisma/client";
import { CreateReportSetPriceListDto } from "./dto/report-set-price-list-create.dto";
import { UpdateReportSetPriceListDto } from "./dto/report-set-price-list-update.dto";
import { ReportSetPriceListService } from "./report-set-price-list.service";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("ReportSetPriceLists")
@Controller("report-set-price-lists")
export class ReportSetPriceListController {
  constructor(private readonly reportSetPriceListService: ReportSetPriceListService) {}

  @Post()
  @ApiOperation({ summary: "Create a new report set price list" })
  @ApiResponse({
    status: 200,
    description: "Report set price list created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "string", enum: Object.values(ReportSetPriceListType) },
        title: { type: "string" },
        report_set_id: { type: "number" },
        license_year: { type: "number" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        fixed_price: { type: "number", nullable: true },
        base_price: { type: "number", nullable: true },
        minimum_fee: { type: "number", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  create(@Body() data: CreateReportSetPriceListDto) {
    return this.reportSetPriceListService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all report set price lists" })
  @ApiResponse({
    status: 200,
    description: "Report set price lists retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          type: { type: "string", enum: Object.values(ReportSetPriceListType) },
          title: { type: "string" },
          report_set_id: { type: "number" },
          license_year: { type: "number" },
          start_date: { type: "string", format: "date-time" },
          end_date: { type: "string", format: "date-time" },
          fixed_price: { type: "number", nullable: true },
          base_price: { type: "number", nullable: true },
          minimum_fee: { type: "number", nullable: true },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
    },
  })
  findAll() {
    return this.reportSetPriceListService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get report set price list by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set price list retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "string", enum: Object.values(ReportSetPriceListType) },
        title: { type: "string" },
        report_set_id: { type: "number" },
        license_year: { type: "number" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        fixed_price: { type: "number", nullable: true },
        base_price: { type: "number", nullable: true },
        minimum_fee: { type: "number", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Report set price list not found" })
  @ApiResponse({ status: 400, description: "Invalid report set price list ID" })
  findOne(@Param("id") id: string) {
    return this.reportSetPriceListService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update report set price list by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set price list updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        type: { type: "string", enum: Object.values(ReportSetPriceListType) },
        title: { type: "string" },
        report_set_id: { type: "number" },
        license_year: { type: "number" },
        start_date: { type: "string", format: "date-time" },
        end_date: { type: "string", format: "date-time" },
        fixed_price: { type: "number", nullable: true },
        base_price: { type: "number", nullable: true },
        minimum_fee: { type: "number", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Report set price list not found" })
  @ApiResponse({ status: 400, description: "Invalid report set price list ID" })
  update(@Param("id") id: string, @Body() data: UpdateReportSetPriceListDto) {
    return this.reportSetPriceListService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete report set price list by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set price list deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Report set price list not found" })
  @ApiResponse({ status: 400, description: "Invalid report set price list ID" })
  remove(@Param("id") id: string) {
    return this.reportSetPriceListService.remove(+id);
  }
}
