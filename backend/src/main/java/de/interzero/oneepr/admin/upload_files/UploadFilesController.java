package de.interzero.oneepr.admin.upload_files;

import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.shared.dto.CustomHeadersDto;
import de.interzero.oneepr.admin.upload_files.dto.CreateFileDto;
import de.interzero.oneepr.admin.upload_files.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.util.Map;

import static de.interzero.oneepr.common.string.Api.UPLOAD_FILES;

@Tag(name = "Upload files to s3")
@RestController
@RequestMapping(UPLOAD_FILES)
@RequiredArgsConstructor
public class UploadFilesController {

    private final UploadFilesService uploadFilesService;

    private static final String EXAMPLE = """
                id: "1",
                original_name: "example.pdf",
                name: "example",
                extension: "pdf",
                size: "100",
                creator_type: "user",
                document_type: "contract",
                user_id: "1",
                created_at: "2021-01-01T00:00:00.000Z",
                updated_at: "2021-01-01T00:00:00.000Z",
                country_id: 1,
            """;

    /**
     * Get the file by id
     *
     * @param fileId File Id in the form of a string
     */
    @PermitAll()
    @GetMapping("/{id}")
    @Operation(summary = "Get file by ID")
    @ApiResponse(
            responseCode = "200",
            description = "File retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = Files.class),
                    examples = @ExampleObject(value = EXAMPLE)
            )
    )
    @ApiResponse(
            responseCode = "404",
            description = "File not found"
    )
    @ApiResponse(
            responseCode = "403",
            description = "User must be authenticated"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Error downloading PDF file"
    )
    public void getFile(@PathVariable("id") String fileId,
                        HttpServletRequest req,
                        HttpServletResponse res) throws ResponseStatusException, IOException {
        CustomHeadersDto customHeadersDto = new CustomHeadersDto(
                req.getHeader("x-user-id"),
                                                                 req.getHeader("x-user-role"));

        if (customHeadersDto.getUserId() == null || customHeadersDto.getUserRole() == null) {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN, "User must be authenticated");
        }

        Map<String, Object> result = uploadFilesService.getFile(fileId);
        Files file = (Files) result.get("file");
        byte[] buffer = (byte[]) result.get("buffer");

        res.setContentType("application/pdf");
        res.setHeader("Content-Disposition", "attachment; filename=contract-" + file.getOriginalName() + ".pdf");
        res.getOutputStream().write(buffer);
    }

    /**
     * Request presigned url
     *
     * @param requestPresignedUrlDto the request body the filename and fileType
     * @return the presigned url, result of the lambda function
     */
    @PermitAll()
    @PostMapping("/request-presigned-url")
    @Operation(summary = "Request to presigned url")
    public LambdaPresignedResponseDto requestUrl(@RequestBody RequestPresignedUrlDto requestPresignedUrlDto) {
        return uploadFilesService.requestUrl(requestPresignedUrlDto);
    }

    /**
     * save file to s3 and database, in the original code, the request is a json string, but in the new code, we use
     * form-data
     *
     * @param documentTypeStr and @param country_id are wrapped in the CreateFileDto in JS
     * @param file            upload file,MultipartFile type
     * @return The found Files entity.
     * @ts-legacy the original code uses @UploadedFile() file: Express.Multer.File, but I use MultipartFile instead
     * @ts-legacy the original code uses @Body() createFileDto: CreateFileDto, but I use @RequestParam to receive them separately
     */
    @PostMapping
    @Operation(summary = "Save file")
    @ApiResponse(
            responseCode = "200",
            description = "File created successfully",
            content = @Content(
                    mediaType = MediaType.MULTIPART_FORM_DATA_VALUE,
                    schema = @Schema(implementation = Files.class),
                    examples = @ExampleObject(
                            value = EXAMPLE
                    )
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Presigned url or Fields not found!"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid country ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public Files create(@RequestParam("document_type") String documentTypeStr,
                        @RequestParam("country_id") Integer countryId,
                        HttpServletRequest req,
                        @RequestParam("file") MultipartFile file) {
        // Create DTO from request parameters
        CreateFileDto createFileDto = new CreateFileDto();
        createFileDto.setDocumentType(CreateFileDto.DocumentType.valueOf(documentTypeStr));
        createFileDto.setCountryId(countryId);

        CustomHeadersDto customHeadersDto = new CustomHeadersDto(req.getHeader("x-user-id"),
                                                                 req.getHeader("x-user-role"));

        return uploadFilesService.uploadFile(createFileDto, customHeadersDto, file);
    }
}
