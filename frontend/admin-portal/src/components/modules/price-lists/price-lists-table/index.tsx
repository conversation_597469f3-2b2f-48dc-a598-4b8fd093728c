import { Datatable } from "@/components/common/datatable";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { deletePriceList, getPriceLists } from "@/lib/api/price-lists";
import { queryClient } from "@/lib/react-query";
import { PriceList } from "@/types/service-setup/price-list";
import { formatCurrency } from "@/utils/format-currency";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { createColumnHelper } from "@tanstack/react-table";
import { EllipsisV<PERSON>ical, <PERSON><PERSON><PERSON>, Trash } from "lucide-react";
import Link from "next/link";
import { useMemo } from "react";
import { NEAR_YEARS } from "../price-list-form/price-list-form-provider";

const columnHelper = createColumnHelper<PriceList>();

const SERVICE_TYPE_FILTERS = [
  { label: "EU License", value: "EU_LICENSE" },
  { label: "Direct License", value: "DIRECT_LICENSE" },
  { label: "Action Guide", value: "ACTION_GUIDE" },
  { label: "Workshop", value: "WORKSHOP" },
];

const LICENSE_YEAR_FILTERS = [
  { label: "All Years", value: "ALL" },
  ...NEAR_YEARS.map((year) => ({ label: year.toString(), value: year.toString() })),
];

export function PriceListsTable() {
  const { paramValues, changeParam } = useQueryFilter(["search", "service_type", "license_year"]);

  const searchParamValue = paramValues["search"];
  const serviceTypeParamValue = paramValues["service_type"] || "EU_LICENSE";
  const licenseYearParamValue = paramValues["license_year"];

  const { data: priceLists, isLoading } = useQuery({
    queryKey: ["price-lists", searchParamValue, serviceTypeParamValue, licenseYearParamValue],
    queryFn: () =>
      getPriceLists({
        search: searchParamValue || undefined,
        license_year: licenseYearParamValue && licenseYearParamValue !== "ALL" ? licenseYearParamValue : undefined,
        service_type: serviceTypeParamValue,
      }),
  });

  const selectedServiceType = useMemo(() => {
    return SERVICE_TYPE_FILTERS.find((filter) => filter.value === serviceTypeParamValue) || SERVICE_TYPE_FILTERS[0];
  }, [serviceTypeParamValue]);

  function handleChangeServiceType(newServiceType: (typeof SERVICE_TYPE_FILTERS)[number]) {
    changeParam("service_type", newServiceType.value);
  }

  const selectedLicenseYear = useMemo(() => {
    return LICENSE_YEAR_FILTERS.find((filter) => filter.value === licenseYearParamValue) || LICENSE_YEAR_FILTERS[0];
  }, [licenseYearParamValue]);

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    changeParam("license_year", newOrder.value);
  }

  function handleSearch(search: string) {
    changeParam("search", search);
  }

  const columns = useMemo(() => {
    const columns = [
      columnHelper.accessor("name", {
        header: "Name",
        cell: (info) => <p className="text-sm text-nowrap text-ellipsis overflow-hidden max-w-40">{info.getValue()}</p>,
      }),
      columnHelper.accessor("type", {
        header: "Service",
        cell: (info) => (
          <p className="text-sm text-nowrap">
            {SERVICE_TYPE_FILTERS.find((filter) => filter.value === info.getValue())?.label}
          </p>
        ),
      }),
      columnHelper.display({
        id: "licenseYear",
        header: "License Year",
        cell: (info) => `${info.row.original.condition_type_value}`,
      }),
      columnHelper.display({
        id: "start-end-date",
        header: "Start-End Date",
        cell: (info) => (
          <p className="text-xs text-nowrap">
            {new Date(info.row.original.start_date).toLocaleDateString()}
            {" - "}
            {new Date(info.row.original.end_date).toLocaleDateString()}
          </p>
        ),
      }),
    ];

    if (selectedServiceType.value === "EU_LICENSE") {
      columns.push(
        ...[
          columnHelper.display({
            id: "registration_fee",
            header: "Registration Fee (€)",
            cell: (info) => formatCurrency(info.row.original.registration_fee || 0),
          }),
          columnHelper.display({
            id: "handling_fee",
            header: "Handling Fee (€)",
            cell: (info) => formatCurrency(info.row.original.handling_fee || 0),
          }),
          columnHelper.display({
            id: "variable_handling_fee",
            header: "Variable Handling Fee (%)",
            cell: (info) => `${info.row.original.variable_handling_fee || 0}%`,
          }),
        ]
      );
    }

    if (selectedServiceType.value === "DIRECT_LICENSE") {
      columns.push(
        ...[
          columnHelper.display({
            id: "basic_price",
            header: "Basic Price (€)",
            cell: (info) => formatCurrency(info.row.original.basic_price || 0),
          }),
          columnHelper.display({
            id: "minimum_price",
            header: "Minimum Price (€)",
            cell: (info) => formatCurrency(info.row.original.minimum_price || 0),
          }),
        ]
      );
    }

    if (selectedServiceType.value === "ACTION_GUIDE" || selectedServiceType.value === "WORKSHOP") {
      columns.push(
        columnHelper.display({
          id: "price",
          header: "Price (€)",
          cell: (info) => formatCurrency(info.row.original.price || 0),
        })
      );
    }

    columns.push(
      columnHelper.display({
        id: "actions",
        header: "",
        cell: (info) => (
          <div className="w-20 flex justify-end">
            <Dropdown
              trigger={
                <button className="rounded-full p-1 hover:bg-secondary/30">
                  <EllipsisVertical className="size-4" />
                </button>
              }
            >
              <DropdownItem
                asChild
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                <Link href={`/price-lists/${info.row.original.id}`} className="flex items-center">
                  <Pencil className="size-4 mr-5 stroke-primary" /> Edit List
                </Link>
              </DropdownItem>
              <DropdownItem
                onClick={(e) => e.preventDefault()}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                <DeletePriceListAction priceListId={Number(info.row.original.id)} />
              </DropdownItem>
            </Dropdown>
          </div>
        ),
      })
    );

    return columns;
  }, [selectedServiceType]);

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-12">
        <DatatableSearch onSearch={handleSearch} defaultValue={searchParamValue || ""} />
        <div className="flex items-center gap-4">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedServiceType.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {SERVICE_TYPE_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeServiceType(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>
      <Datatable data={priceLists || []} columns={columns} loading={isLoading} />
    </div>
  );
}

function DeletePriceListAction({ priceListId }: { priceListId: number }) {
  const { mutate: deletePrice, isPending: isDeletingPriceList } = useMutation({
    mutationFn: deletePriceList,
  });

  function handleDeletePriceList(e: React.MouseEvent<HTMLButtonElement>) {
    e.preventDefault();
    deletePrice(priceListId, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["price-lists"] });

        const closeButton = document.querySelector("[data-radix-alert-dialog-close]") as HTMLButtonElement | null;
        if (closeButton) closeButton.click();
      },
    });
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <button className="flex items-center">
          <Trash className="size-4 mr-5 stroke-primary" /> Delete List
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete price list?</AlertDialogTitle>
          <AlertDialogDescription>
            By clicking on ”confirm” you are deleting this price list. All the fractions set linked to this price list
            will be affected.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeletingPriceList}>Back</AlertDialogCancel>
          <AlertDialogAction disabled={isDeletingPriceList} onClick={handleDeletePriceList}>
            {isDeletingPriceList ? "Deleting..." : "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
