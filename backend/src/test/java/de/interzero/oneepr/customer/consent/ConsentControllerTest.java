package de.interzero.oneepr.customer.consent;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.consent.dto.CreateConsentDto;
import de.interzero.oneepr.customer.consent.dto.UpdateConsentDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for {@link ConsentController}
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ConsentControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ConsentRepository consentRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        consentRepository.deleteAll();
    }

    /**
     * {@link ConsentController#create(CreateConsentDto)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldCreateNewConsent() throws Exception {
        // create DTO
        CreateConsentDto dto = new CreateConsentDto();
        dto.setName("Terms of Use");
        dto.setType(Consent.Type.ACCOUNT);
        dto.setDescription("Platform Terms");

        // hit endpoint
        ResultActions resultActions = mockMvc.perform(post(Api.CONSENT).contentType(MediaType.APPLICATION_JSON)
                                                              .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isCreated());

        // assert
        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        Consent createdConsent = objectMapper.readValue(jsonResponse, Consent.class);
        assertEquals(dto.getName(), createdConsent.getName());
        assertEquals(dto.getType(), createdConsent.getType());
        assertEquals(dto.getDescription(), createdConsent.getDescription());
    }

    /**
     * {@link ConsentController#findAll()}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldReturnAllConsents() throws Exception {

        // create consents
        Consent consent1 = new Consent();
        consent1.setName("Terms");
        consent1.setType(Consent.Type.ACCOUNT);
        consent1.setDescription("Use");
        consent1.setVersion(1);
        consent1.setCreatedAt(Instant.now());
        consent1.setUpdatedAt(Instant.now());
        consentRepository.save(consent1);

        Consent consent2 = new Consent();
        consent2.setName("Privacy");
        consent2.setType(Consent.Type.PURCHASE);
        consent2.setDescription("Policy");
        consent2.setVersion(1);
        consent2.setCreatedAt(Instant.now());
        consent2.setUpdatedAt(Instant.now());
        consentRepository.save(consent2);

        // hit endoint
        ResultActions resultActions = mockMvc.perform(get(Api.CONSENT).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // assert
        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        List<Consent> consents = objectMapper.readValue(
                jsonResponse, new TypeReference<>() {
                });
        assertEquals(2, consents.size());
    }

    /**
     * {@link ConsentController#findOne(Integer)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldReturnSpecificConsent() throws Exception {
        // create consent
        Consent consent = new Consent();
        consent.setName("Terms");
        consent.setType(Consent.Type.ACCOUNT);
        consent.setDescription("Use");
        consent.setVersion(1);
        consent.setCreatedAt(Instant.now());
        consent.setUpdatedAt(Instant.now());
        consentRepository.save(consent);

        // hit endpoint
        ResultActions resultActions = mockMvc.perform(get(Api.CONSENT + "/" + consent.getId()).contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // assert
        String jsonResponse = resultActions.andReturn().getResponse().getContentAsString();
        Consent foundConsent = objectMapper.readValue(jsonResponse, Consent.class);
        assertEquals(consent.getId(), foundConsent.getId());
        assertEquals(consent.getName(), foundConsent.getName());
        assertEquals(consent.getType(), foundConsent.getType());
        assertEquals(consent.getDescription(), foundConsent.getDescription());
        assertEquals(consent.getVersion(), foundConsent.getVersion());
    }

    /**
     * {@link ConsentController#update(Integer, UpdateConsentDto)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldUpdateConsentAndIncrementVersion() throws Exception {
        // create consent
        Consent original = new Consent();
        original.setName("Old");
        original.setType(Consent.Type.ACCOUNT);
        original.setDescription("Old");
        original.setVersion(1);
        original.setCreatedAt(Instant.now());
        original.setUpdatedAt(Instant.now());
        consentRepository.save(original);

        // dto
        UpdateConsentDto dto = new UpdateConsentDto();
        dto.setName("Updated");
        dto.setType(Consent.Type.PURCHASE);
        dto.setDescription("Updated");

        // hit endpoint
        ResultActions result = mockMvc.perform(patch(Api.CONSENT + "/" + original.getId()).contentType(MediaType.APPLICATION_JSON)
                                                       .content(objectMapper.writeValueAsString(dto)))
                .andExpect(status().isOk());

        // assert
        String jsonResponse = result.andReturn().getResponse().getContentAsString();
        Consent updated = objectMapper.readValue(jsonResponse, Consent.class);

        assertEquals(2, updated.getVersion());
        assertEquals(dto.getName(), updated.getName());
        assertEquals(dto.getType(), updated.getType());
        assertEquals(dto.getDescription(), updated.getDescription());
    }

    /**
     * {@link ConsentController#update(Integer, UpdateConsentDto)} should return NOT FOUND if consent does not exist
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldThrowWhenUpdatingMissingConsent() throws Exception {
        UpdateConsentDto dto = new UpdateConsentDto();
        dto.setName("Updated");
        dto.setType(Consent.Type.PURCHASE);
        dto.setDescription("Updated");

        mockMvc.perform(get(Api.CONSENT + "/999").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(dto))).andExpect(status().isNotFound());
    }

    /**
     * {@link ConsentController#remove(Integer)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldDeleteConsent() throws Exception {
        // create consent
        Consent saved = new Consent();
        saved.setName("Terms");
        saved.setType(Consent.Type.ACCOUNT);
        saved.setDescription("Use");
        saved.setVersion(1);
        saved.setCreatedAt(Instant.now());
        saved.setUpdatedAt(Instant.now());
        consentRepository.save(saved);

        // hit endpoint
        mockMvc.perform(delete(Api.CONSENT + "/" + saved.getId())).andExpect(status().isOk());

        // assert
        assertTrue(consentRepository.findById(saved.getId()).isEmpty());
    }

    /**
     * {@link ConsentController#findManyByType(Consent.Type)}
     *
     * @throws Exception any exception
     */
    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void shouldReturnByType() throws Exception {
        // create consents
        Instant now = Instant.now();

        Consent consentAccount = new Consent();
        consentAccount.setName("Terms");
        consentAccount.setType(Consent.Type.ACCOUNT);
        consentAccount.setDescription("Use");
        consentAccount.setVersion(1);
        consentAccount.setCreatedAt(now);
        consentAccount.setUpdatedAt(now);
        consentRepository.save(consentAccount);

        Consent consentPurchase = new Consent();
        consentPurchase.setName("Privacy");
        consentPurchase.setType(Consent.Type.PURCHASE);
        consentPurchase.setDescription("Policy");
        consentPurchase.setVersion(1);
        consentPurchase.setCreatedAt(now);
        consentPurchase.setUpdatedAt(now);
        consentRepository.save(consentPurchase);

        assertEquals(2, consentRepository.count());

        // hit endpoint and expect only 1
        mockMvc.perform(get(Api.CONSENT + "/type/" + Consent.Type.ACCOUNT.name()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }
}
