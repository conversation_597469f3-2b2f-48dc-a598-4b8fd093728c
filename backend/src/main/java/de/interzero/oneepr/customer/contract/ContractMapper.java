package de.interzero.oneepr.customer.contract;

import de.interzero.oneepr.action_guide.ActionGuideMapper;
import de.interzero.oneepr.customer.contract.dto.ContractResponseDto;
import de.interzero.oneepr.customer.customer.CustomerMapper;
import de.interzero.oneepr.customer.file.FileMapper;
import de.interzero.oneepr.customer.license.LicenseMapper;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformationMapper;
import de.interzero.oneepr.customer.termination.TerminationMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

/**
 * A MapStruct interface for mapping a {@link Contract} entity to its detailed
 * {@link ContractResponseDto}. The implementation is generated automatically at compile time.
 * <p>
 * This mapper uses an {@link AfterMapping} method to apply custom filtering logic,
 * ensuring that the final DTO's collections only contain non-deleted items,
 * precisely mimicking the original Prisma query's `where: { deleted_at: null }` clauses.
 */
@Mapper(
        componentModel = "spring",
        uses = {CustomerMapper.class, ActionGuideMapper.class, LicenseMapper.class, LicenseRequiredInformationMapper.class, FileMapper.class, TerminationMapper.class}
)
public interface ContractMapper {

    /**
     * Maps a {@link Contract} entity to a {@link ContractResponseDto}.
     * <p>
     * MapStruct will handle the mapping of all fields with matching names and will
     * use the mappers declared in the `uses` clause for nested objects. The initial
     * collections will be unfiltered. The filtering is then applied in the
     * {@link #applyFiltering(Contract, ContractResponseDto)} method.
     *
     * @param contract The source Contract entity.
     * @return The mapped {@link ContractResponseDto}, ready for filtering.
     */
    ContractResponseDto toDto(Contract contract);

    /**
     * A post-processing method that applies critical filtering logic after the initial
     * mapping is complete. This method is automatically called by MapStruct after `toDto`.
     * <p>
     * It iterates through the DTO's collections and removes any items that have a
     * non-null `deletedAt` field, thus replicating the `where: { deleted_at: null }`
     * logic from Prisma.
     *
     * @param contract The source entity (can be used for more complex logic if needed, unused here).
     * @param dto      The destination DTO, annotated with {@link MappingTarget}, which will be modified in place.
     */
    @AfterMapping
    default void applyFiltering(Contract contract,
                                @MappingTarget ContractResponseDto dto) {
        if (dto.getActionGuides() != null) {
            dto.setActionGuides(dto.getActionGuides().stream().filter(ag -> ag.getDeletedAt() == null).toList());
        }

        if (dto.getLicenses() != null) {
            dto.setLicenses(dto.getLicenses().stream().filter(l -> l.getDeletedAt() == null).toList());
        }

        if (dto.getGeneralInformations() != null) {
            dto.setGeneralInformations(dto.getGeneralInformations()
                                               .stream()
                                               .filter(gi -> gi.getDeletedAt() == null)
                                               .toList());
        }

        if (dto.getFiles() != null) {
            dto.setFiles(dto.getFiles().stream().filter(f -> f.getDeletedAt() == null).toList());
        }

    }
}