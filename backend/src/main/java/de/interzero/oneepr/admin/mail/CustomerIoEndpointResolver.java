package de.interzero.oneepr.admin.mail;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Resolves Customer.io API endpoints based on configured region.
 * Single source of truth for endpoint construction to avoid brittleness in tests and callers.
 */
@Getter
@Component
public class CustomerIoEndpointResolver {

    /**
     * -- GETTER --
     * Returns the configured region (e.g., "us", "eu").
     */
    private final String region;

    public CustomerIoEndpointResolver(@Value("${customerio.region:eu}") String region) {
        this.region = region;
    }

    /**
     * Builds the full URL for sending transactional emails.
     * Regions supported:
     * - US: api.customer.io
     * - EU: api-eu.customer.io
     *
     * @return complete send email endpoint URL
     */
    public String buildSendEmailUrl() {
        String baseUrl = resolveBaseUrl();
        return baseUrl + "/v1/send/email";
    }

    /**
     * Resolves base API URL based on the configured region.
     */
    private String resolveBaseUrl() {
        if ("us".equalsIgnoreCase(region)) {
            return "https://api.customer.io";
        }
        // default to EU
        return "https://api-eu.customer.io";
    }
}
