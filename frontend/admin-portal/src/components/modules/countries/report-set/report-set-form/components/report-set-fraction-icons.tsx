import { useMutation, useQuery } from "@tanstack/react-query";
import { createFractionIcon, deleteFractionIcon, getFractionIcons } from "@/lib/api/fraction-icons";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, KeyboardArrowLeft, Upload } from "@interzero/oneepr-react-ui/Icon";
import { Skeleton } from "@/components/ui/skeleton";
import { uploadFile } from "@/lib/api/upload-files";
import { Role } from "@/utils/user";
import { enqueueSnackbar } from "notistack";
import { queryClient } from "@/lib/react-query";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useWatch } from "react-hook-form";
import { Loader } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { FractionIcon } from "@/components/ui/fraction-icon";
import { useServiceSetup } from "@/hooks/use-service-setup";
interface ReportSetFractionIconsProps {
  onClose: () => void;
  onAddFractionIcon: (data: AddFractionIconSchema) => void;
  defaultFractionIconId?: number;
}

const addFractionIconSchema = z.object({
  fraction_icon_id: z.number(),
  fraction_icon_image_url: z.string().optional().default(""),
});

export type AddFractionIconSchema = z.infer<typeof addFractionIconSchema>;

export function ReportSetFractionIcons({
  onClose,
  onAddFractionIcon,
  defaultFractionIconId,
}: ReportSetFractionIconsProps) {
  const { register, setValue, control, handleSubmit, reset } = useForm<AddFractionIconSchema>({
    resolver: zodResolver(addFractionIconSchema),
  });
  const { country } = useServiceSetup();

  const { data: fractionIcons, isPending } = useQuery({
    queryKey: ["fraction-icons"],
    queryFn: async () => {
      const fractionIcons = await getFractionIcons();

      if (!fractionIcons.length) return fractionIcons;

      const defaultFractionIcon = fractionIcons.find((fractionIcon) => fractionIcon.id === defaultFractionIconId);

      setValue("fraction_icon_id", defaultFractionIcon?.id || fractionIcons[0].id);

      return fractionIcons;
    },
  });

  const { mutate: uploadFractionIcon, isPending: isUploadingFractionIcon } = useMutation({
    mutationFn: async (file: File) => {
      const uploadedFile = await uploadFile({
        user_id: 0,
        user_role: Role.ADMIN,
        file,
        document_type: "FRACTION_ICON",
        country_id: `${country.id}`,
      });

      await createFractionIcon({ file_id: uploadedFile.id });
    },
  });

  const { mutate: removeFractionIcon, isPending: isRemovingFractionIcon } = useMutation({
    mutationFn: deleteFractionIcon,
  });

  async function handleOnInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];
    if (!file) return;

    handleUploadFractionIcon(file);
  }

  async function handleUploadFractionIcon(file: File) {
    if (!file) return;

    uploadFractionIcon(file, {
      onSuccess: () => {
        enqueueSnackbar("Fraction icon uploaded successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["fraction-icons"] });
      },
      onError: () => {
        enqueueSnackbar("Error uploading fraction icon", { variant: "error" });
      },
    });
  }

  async function handleAddFractionIcon(data: AddFractionIconSchema) {
    const fractionIcon = fractionIcons?.find((fractionIcon) => fractionIcon.id === data.fraction_icon_id);

    if (!fractionIcon) return;

    reset();
    onAddFractionIcon({ ...data, fraction_icon_image_url: fractionIcon.image_url });
  }

  async function handleRemoveFractionIcon(fractionIconId: number) {
    removeFractionIcon(fractionIconId, {
      onSuccess: () => {
        enqueueSnackbar("Fraction icon removed successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["fraction-icons"] });
      },
      onError: () => {
        enqueueSnackbar("Error removing fraction icon", { variant: "error" });
      },
    });
  }

  const fractionIconId = useWatch({ control, name: "fraction_icon_id" });

  const isFractionIconsLoading = isPending || isRemovingFractionIcon;

  return (
    <div className="w-full space-y-10">
      <div>
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<KeyboardArrowLeft />}
          onClick={onClose}
        >
          Back
        </Button>
      </div>
      <div className="space-y-6">
        <div className="w-full grid grid-cols-7 gap-3">
          {isFractionIconsLoading && <FractionIconsSkeleton />}
          {!isFractionIconsLoading &&
            !!fractionIcons &&
            fractionIcons?.map((fractionIcon) => (
              <div
                key={fractionIcon.id}
                data-selected={fractionIconId === fractionIcon.id}
                onClick={() => {
                  if (isPending) return;
                  setValue("fraction_icon_id", fractionIcon.id);
                }}
                className="relative overflow-hidden size-16 flex-none cursor-pointer border-[1.5px] border-white hover:border-primary data-[selected=true]:border-primary rounded-md transition-all duration-100"
              >
                <input type="radio" className="hidden" {...register("fraction_icon_id")} value={fractionIcon.id} />
                <FractionIcon iconUrl={fractionIcon.image_url} size="large" />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <button className="z-20 hover:bg-support-blue/40 p-[1px] absolute bottom-1 right-1 rounded-full flex items-center justify-center">
                      <Delete className="size-4 fill-support-blue" />
                    </button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                      <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleRemoveFractionIcon(fractionIcon.id)}
                        disabled={isRemovingFractionIcon}
                      >
                        {isRemovingFractionIcon ? "Removing..." : "Remove"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            ))}
          {!isPending && (
            <label
              htmlFor="fraction-icon-input"
              className="flex items-center justify-center flex-col size-16 rounded-md border-[1.5px] bg-tonal-dark-blue-90 border-dashed border-support-blue cursor-pointer hover:opacity-80"
            >
              <input
                disabled={isUploadingFractionIcon || isPending}
                id="fraction-icon-input"
                type="file"
                className="hidden"
                onChange={handleOnInputChange}
                accept=".png, .jpg, .jpeg"
              />
              {isUploadingFractionIcon ? (
                <Loader className="size-4 animate-spin fill-support-blue text-support-blue" />
              ) : (
                <Upload className="size-5 fill-support-blue" />
              )}
            </label>
          )}
        </div>
        <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
      </div>
      <div className="flex justify-end">
        <Button
          type="button"
          variant="filled"
          color="yellow"
          size="medium"
          onClick={handleSubmit(handleAddFractionIcon)}
        >
          Add fraction
        </Button>
      </div>
    </div>
  );
}

function FractionIconsSkeleton() {
  return Array.from({ length: 7 }).map((_, index) => <Skeleton key={index} className="size-16 p-2 rounded-md" />);
}
