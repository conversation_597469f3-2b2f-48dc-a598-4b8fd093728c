package de.interzero.oneepr.customer.customer_commitment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * DTO for creating a new customer commitment.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateCustomerCommitmentDto {

    @Schema(
            description = "The customer email",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_email")
    private String customerEmail;

    @Schema(
            description = "The country code",
            example = "ID",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("country_code")
    @NotBlank(message = "Country code cannot be blank.")
    private String countryCode;

    @Schema(
            description = "The year",
            example = "2025",
            type = "integer",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("year")
    @NotNull(message = "Year cannot be null.")
    private Integer year;

    @Schema(
            description = "The shopping cart id",
            example = "123",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("shopping_cart_id")
    private String shoppingCartId;

    @Schema(
            description = "The commitment answers provided by the customer.",
            example = "[{\"criteria_id\": 1, \"answer\": \"OBLIGED\"}, {\"criteria_id\": 2, \"answer\": \"REQUEST\", \"to_answer\": \"ADDITIONAL_INFO\"}]",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("commitment_answers")
    @NotNull(message = "Commitment answers cannot be null.")
    @Valid
    private List<CommitmentAnswerDto> commitmentAnswers;

    @Getter
    @Setter
    public static class CommitmentAnswerDto {

        @Schema(
                description = "The criteria ID",
                example = "1",
                type = "integer",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @JsonProperty("criteria_id")
        @NotNull(message = "Criteria ID cannot be null.")
        private Integer criteriaId;

        @Schema(
                description = "The answer provided for the criteria",
                example = "OBLIGED",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        @JsonProperty("answer")
        @NotBlank(message = "Answer cannot be blank.")
        private String answer;

        @Schema(
                description = "An optional secondary answer or clarification",
                example = "ADDITIONAL_INFO",
                requiredMode = Schema.RequiredMode.NOT_REQUIRED
        )
        @JsonProperty("to_answer")
        private String toAnswer;
    }
}