package de.interzero.oneepr.customer.company_email.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "Fields required to create a new company-email.")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateCompanyEmailDto extends BaseDto {

    @JsonProperty("company_email")
    @Schema(description = "Company Email")
    private String companyEmail;

    @JsonProperty("company_id")
    @Schema(description = "Company ID")
    private Integer companyId;
}
