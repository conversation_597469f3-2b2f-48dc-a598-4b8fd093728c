"use client";

import { useSidebar } from "@/hooks/use-sidebar";

export function HeaderSidebarToggle() {
  const sidebar = useSidebar();

  return (
    <button
      onClick={() => sidebar.setOpen(!sidebar.open)}
      className="lg:hidden flex-none size-6 flex items-center justify-center group"
    >
      <div className="w-full space-y-1">
        <div className="h-1 w-full bg-primary group-hover:bg-support-blue transition-all duration-300"></div>
        <div className="h-1 w-full bg-primary group-hover:bg-support-blue transition-all duration-300"></div>
        <div className="h-1 w-full bg-primary group-hover:bg-support-blue transition-all duration-300"></div>
      </div>
    </button>
  );
}
