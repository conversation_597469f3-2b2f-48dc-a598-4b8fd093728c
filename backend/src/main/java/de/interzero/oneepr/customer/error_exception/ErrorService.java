package de.interzero.oneepr.customer.error_exception;

import de.interzero.oneepr.customer.customer_interface.ErrorInterfaceEnUstIdErrorCode;
import de.interzero.oneepr.customer.customer_interface.ErrorInterfaceUstIdErrorCode;
import org.springframework.stereotype.Service;

/**
 * ErrorService is a container class for VAT ID (USt-IdNr) error handling services.
 * It provides services to interpret error codes related to VAT ID validation,
 * offering results in both German (UstIdErrorService) and English (EnUstIdErrorService).
 * <p>
 * To use these services, inject the specific static inner service class into your Spring components.
 * For example, to use the German error service:
 * <pre>{@code
 * @Autowired
 * private ErrorService.UstIdErrorService ustIdErrorService;
 *
 * // ... in some method ...
 * String errorCodeFromApi = "201";
 * ErrorService.ErrorResult<ErrorInterfaceUstIdErrorCode> result = ustIdErrorService.getErrorMessageByCode(errorCodeFromApi);
 * System.out.println("Code: " + result.code() + ", Valid: " + result.valid() + ", Description: " + result.code().getDescription());
 * }</pre>
 */
public class ErrorService { // Outer class

    public record ErrorResult<T>(T code, boolean valid) {

    }

    @Service
    public static class UstIdErrorService { // Made static

        /**
         * Retrieves an error result based on the provided error code string.
         * The result includes the corresponding ErrorInterfaceUstIdErrorCode enum and a boolean indicating validity.
         *
         * @param code The string representation of the error code.
         * @return An ErrorResult containing the ErrorInterfaceUstIdErrorCode and its validity.
         */
        public ErrorResult<ErrorInterfaceUstIdErrorCode> getErrorMessageByCode(String code) { // Changed ErrorInterfaceUstIdErrorCode to ErrorInterfaceUstIdErrorCode
            if (code == null) {
                return new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code999, false);
            }
            return switch (code) {
                case "200" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code200, true);
                case "201" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code201, false);
                case "202" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code202, false);
                case "203" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code203, false);
                case "204" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code204, false);
                case "205" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code205, false);
                case "206" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code206, false);
                case "208" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code208, false);
                case "209" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code209, false);
                case "210" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code210, false);
                case "211" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code211, false);
                case "212" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code212, false);
                case "213" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code213, false);
                case "214" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code214, false);
                case "215" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code215, false);
                case "216" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code216, false);
                case "217" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code217, false);
                case "218" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code218, true);
                case "219" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code219, true);
                case "221" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code221, false);
                case "223" -> new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code223, true);
                default -> // Handles both "999" explicitly and any other unknown codes
                        new ErrorResult<>(ErrorInterfaceUstIdErrorCode.Code999, false);
            };
        }
    }

    @Service
    public static class EnUstIdErrorService { // Made static

        /**
         * Retrieves an English error result based on the provided error code string.
         * The result includes the corresponding ErrorInterfaceEnUstIdErrorCode enum and a boolean indicating validity.
         *
         * @param code The string representation of the error code.
         * @return An ErrorResult containing the ErrorInterfaceEnUstIdErrorCode and its validity.
         */
        public ErrorResult<ErrorInterfaceEnUstIdErrorCode> getErrorMessageByCode(String code) { // Changed ErrorInterfaceEnUstIdErrorCode to ErrorInterfaceEnUstIdErrorCode
            if (code == null) {
                return new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code999, false);
            }
            return switch (code) {
                case "200" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code200, true);
                case "201" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code201, false);
                case "202" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code202, false);
                case "203" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code203, false);
                case "204" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code204, false);
                case "205" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code205, false);
                case "206" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code206, false);
                case "208" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code208, false);
                case "209" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code209, false);
                case "210" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code210, false);
                case "211" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code211, false);
                case "212" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code212, false);
                case "213" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code213, false);
                case "214" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code214, false);
                case "215" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code215, false);
                case "216" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code216, false);
                case "217" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code217, false);
                case "218" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code218, true);
                case "219" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code219, true);
                case "221" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code221, false);
                case "223" -> new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code223, true);
                default -> // Handles both "999" explicitly and any other unknown codes
                        new ErrorResult<>(ErrorInterfaceEnUstIdErrorCode.Code999, false);
            };
        }
    }
}