"use client";
import { ModuleContent } from "../../../common/module-content";
import { ModuleTitle } from "../../../common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CreateMyCompanyForm } from "./form";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export function CreateMyCompanyModule() {
  const router = useRouter();
  return (
    <ModuleContent>
      <ModuleTitle
        title="Add company"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit."
      />

      <Button
        variant="text"
        color="light-blue"
        size="medium"
        leadingIcon={<ChevronLeft />}
        onClick={() => router.back()}
      >
        Back to My Companies
      </Button>
      <CreateMyCompanyForm />
    </ModuleContent>
  );
}
