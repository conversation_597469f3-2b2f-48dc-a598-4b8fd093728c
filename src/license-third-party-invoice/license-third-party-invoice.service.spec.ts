import { Test, TestingModule } from "@nestjs/testing";
import { LicenseThirdPartyInvoiceService } from "./license-third-party-invoice.service";
import { DatabaseService } from "@/database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { LicenseThirdPartyInvoiceIssuer, LicenseThirdPartyInvoiceStatus } from "@prisma/client";
import axios from "axios";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { Role } from "@/shared/auth/role.enum";

jest.mock("axios");
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe("LicenseThirdPartyInvoiceService", () => {
  let service: LicenseThirdPartyInvoiceService;
  let databaseService: DatabaseService;

  const mockDatabaseService = {
    licenseThirdPartyInvoice: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    },
    file: {
      update: jest.fn(),
    },
    $transaction: jest.fn((callback) => callback(mockDatabaseService)),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LicenseThirdPartyInvoiceService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<LicenseThirdPartyInvoiceService>(LicenseThirdPartyInvoiceService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all non-deleted invoices with filters", async () => {
      const mockInvoices = [
        { id: 1, title: "Invoice 1" },
        { id: 2, title: "Invoice 2" },
      ];

      const findAllDto = {
        license_id: 1,
        from_date: "2024-01-01",
        to_date: "2024-12-31",
      };

      mockDatabaseService.licenseThirdPartyInvoice.findMany.mockResolvedValue(mockInvoices);

      const result = await service.findAll(findAllDto);

      expect(result).toEqual(mockInvoices);
    });

    it("should return all non-deleted invoices without filters", async () => {
      const mockInvoices = [
        { id: 1, title: "Invoice 1" },
        { id: 2, title: "Invoice 2" },
      ];

      const findAllDto = {
        license_id: null,
        from_date: null,
        to_date: null,
      };

      mockDatabaseService.licenseThirdPartyInvoice.findMany.mockResolvedValue(mockInvoices);

      const result = await service.findAll(findAllDto);

      expect(result).toEqual(mockInvoices);
      expect(mockDatabaseService.licenseThirdPartyInvoice.findMany).toHaveBeenCalledWith({
        where: {
          deleted_at: null,
        },
        include: {
          files: {
            where: {
              deleted_at: null,
            },
          },
          license: {
            include: {
              files: {
                where: {
                  deleted_at: null,
                },
              },
            },
          },
        },
        orderBy: {
          id: "desc",
        },
      });
    });
  });

  describe("findOne", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };
    it("should throw BadRequestException for invalid ID", async () => {
      await expect(service.findOne(NaN, user)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException when invoice not found", async () => {
      mockDatabaseService.licenseThirdPartyInvoice.findUnique.mockResolvedValue(null);

      await expect(service.findOne(1, user)).rejects.toThrow(NotFoundException);
    });

    it("should return invoice when found", async () => {
      const mockInvoice = {
        id: 1,
        title: "Test Invoice",
        license: { id: 1, contract: { customer_id: 1, customer: { id: 1 } } },
      };
      mockDatabaseService.licenseThirdPartyInvoice.findUnique.mockResolvedValue(mockInvoice);

      const result = await service.findOne(1, user);

      expect(result).toEqual(mockInvoice);
    });
  });

  describe("create", () => {
    it("should create a new invoice and update Monday.com", async () => {
      const createDto = {
        license_id: 1,
        title: "New Invoice",
        status: LicenseThirdPartyInvoiceStatus.OPEN,
        price: 1000,
        issuer: LicenseThirdPartyInvoiceIssuer.OTHER_THIRD_PARTY,
        issued_at: "2024-01-01",
        due_date: "2024-02-01",
        file_id: "file123",
      };

      const mockCreatedInvoice = {
        id: 1,
        ...createDto,
        license: {
          country_name: "US",
          contract: {
            customer_id: 123,
          },
        },
      };

      process.env.CRM_API_URL = "http://crm-api";
      mockDatabaseService.licenseThirdPartyInvoice.create.mockResolvedValue(mockCreatedInvoice);
      mockedAxios.post.mockResolvedValue({ data: "monday-item-123" });

      const result = await service.create(createDto);

      expect(result).toEqual(mockCreatedInvoice);
      expect(mockedAxios.post).toHaveBeenCalledWith(
        "http://crm-api/third-party-invoices",
        {
          country: "US",
          customerId: "123",
          timeline: "2024-02-01",
        },
        {
          headers: {
            "x-system-api-key": "2d1dd774-2d59-46f7-be47-7b685cee7141",
            "x-user-role": "system",
          },
        }
      );

      expect(mockDatabaseService.file.update).toHaveBeenCalledWith({
        where: { id: "file123" },
        data: { third_party_invoice_id: 1 },
      });
    });
  });

  describe("update", () => {
    const user: AuthenticatedUser = {
      email: "<EMAIL>",
      id: "1",
      role: Role.ADMIN,
    };

    it("should throw BadRequestException for invalid ID", async () => {
      await expect(service.update(NaN, {}, user)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException when invoice not found", async () => {
      mockDatabaseService.licenseThirdPartyInvoice.findUnique.mockResolvedValue(null);

      await expect(service.update(1, {}, user)).rejects.toThrow(NotFoundException);
    });
  });
});
