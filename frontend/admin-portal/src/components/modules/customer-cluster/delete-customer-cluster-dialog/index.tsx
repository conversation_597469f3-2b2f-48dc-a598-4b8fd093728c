"use client";

import { useEffect, useState } from "react";
import { enqueueSnackbar } from "notistack";
// import { useRouter } from "next/navigation";
import Image from "next/image";

import { useMutation } from "@tanstack/react-query";
import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";
import { deleteCustomersCluster } from "@/lib/api/customer-cluster";
import { useRouter } from "next/navigation";

interface DeleteCustomerClusterDialogProps {
  customerClusterId: string | number;
  children: React.ReactNode;
  refetch?: () => void;
}

export function DeleteCustomerClusterDialog({
  customerClusterId,
  children,
  refetch,
}: DeleteCustomerClusterDialogProps) {
  const router = useRouter();

  const [isOpen, setIsOpen] = useState(false);
  const [isDone, setIsDone] = useState(false);

  useEffect(() => {
    if (!isOpen && isDone) {
      setIsDone(false);
      router.push("/customer-clusters");
    }

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, isDone]);

  const { mutate, isPending } = useMutation({
    mutationFn: () => {
      deleteCustomersCluster(Number(customerClusterId));

      // eslint-disable-next-line no-console
      console.log("customerClusterId", customerClusterId);
      return new Promise((resolve) => setTimeout(resolve, 3000));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["customer-clusters"] });
      setIsDone(true);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleDelete() {
    mutate();
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>

      {!isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="font-bold sm:text-[1.75rem]">Delete client of the cluster</DialogTitle>
            <DialogDescription className="text-tonal-dark-cream-20 font-normal">
              By clicking on ”confirm” you are excluding this cliente from this cluster.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outlined" color="dark-blue" size="medium">
                Back
              </Button>
            </DialogClose>
            <Button variant="filled" color="red" size="medium" onClick={handleDelete} disabled={isPending}>
              {isPending ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      )}

      {isDone && (
        <DialogContent className="bg-surface-02 py-9 max-w-2xl">
          <div className="flex items-center gap-2 mb-10">
            <Image src="/assets/images/leaf-seal.svg" alt="leaf seal" width={36} height={36} className="size-9" />
            <h3 className="font-bold text-[1.75rem] text-primary">
              Client deleted from cluster with <br /> success
            </h3>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="filled" color="yellow" size="medium" onClick={() => refetch && refetch()}>
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      )}
    </Dialog>
  );
}
