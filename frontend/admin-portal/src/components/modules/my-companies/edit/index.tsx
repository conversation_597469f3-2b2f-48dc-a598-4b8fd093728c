"use client";
import { MapsHomeWork } from "@interzero/oneepr-react-ui/Icon";
import { ModuleContent } from "../../../common/module-content";
import { ModuleTitle } from "../../../common/module-title";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { MyCompanyForm, MyCompanyFormModes } from "../company-form";
import { useState } from "react";
import { OrdersTable } from "./orders-table";

const mockData = {
  id: "1",
  companyName: "Test Company",
  registrationNumber: "*********",
  vatNumber: "*********",
  tax: "*********",
  email: "<EMAIL>",
  phoneNumber: "+45 12345678",
  address: "Test Address",
  city: "Test City",
  country: "Test Country",
  contactName: "Test Contact",
};

export function EditMyCompanyModule() {
  const router = useRouter();
  const [formMode, setFormMode] = useState(MyCompanyFormModes.VIEW);
  return (
    <ModuleContent>
      <ModuleTitle
        icon={MapsHomeWork}
        title="My Companies"
        description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit."
      />
      <Button
        variant="text"
        color="light-blue"
        size="medium"
        leadingIcon={<ChevronLeft />}
        onClick={() => router.back()}
      >
        Back to My Companies
      </Button>
      <MyCompanyForm
        mode={formMode}
        company={mockData}
        handleEditButton={() => setFormMode(MyCompanyFormModes.UPDATE)}
        handleCancelButton={() => setFormMode(MyCompanyFormModes.VIEW)}
        // eslint-disable-next-line no-console
        onSubmit={(values) => console.log(values)}
      />

      <OrdersTable />
    </ModuleContent>
  );
}
