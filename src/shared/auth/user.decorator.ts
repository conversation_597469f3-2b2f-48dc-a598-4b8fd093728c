import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { Role, RoleType } from "./role.enum";
import { HEADER_USER_EMAIL, HEADER_USER_ID, HEADER_USER_ROLE } from "./const";

export interface AuthenticatedUser {
  id: string;
  role: RoleType;
  email: string;
}

export const User = createParamDecorator((_: unknown, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();

  if (request.headers[HEADER_USER_ROLE] === Role.SYSTEM) {
    return { role: Role.SYSTEM } as AuthenticatedUser;
  }

  if (!request.headers[HEADER_USER_ID]) return null;

  return {
    id: request.headers[HEADER_USER_ID],
    role: request.headers[HEADER_USER_ROLE],
    email: request.headers[HEADER_USER_EMAIL],
  } as AuthenticatedUser;
});
