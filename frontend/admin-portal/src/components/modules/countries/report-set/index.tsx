"use client";

import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { getServiceSetupReportSet } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useServiceSetup, withServiceSetupProvider } from "@/hooks/use-service-setup";
import { useRouter } from "next/navigation";
import { CountryContent } from "../service-setup/country-content";
import { PreviewCalculatorDialog } from "@/components/common/preview/preview-calculator-dialog";
import { PreviewReportTableDialog } from "@/components/common/preview/preview-report-table-dialog";
import { ReportSetExcelForm } from "./report-set-form/excel";
import { ReportSetFormProvider } from "./report-set-form/components/report-set-form-provider";
import { ReportSetPlataformForm } from "./report-set-form/plataform";
import { memo, useState } from "react";
import { ReportSetMode } from "@/types/service-setup/report-set";
import { ReportSetNoReportingForm } from "./report-set-form/no-reporting";
import { UnsavedChangesBackButton } from "@/components/common/unsaved-changes-back-button";
import { useUnsavedChanges } from "@/hooks/use-unsaved-changes";

interface CountryServiceSetupReportSetProps {
  countryCode: string;
  reportSetId: number;
}

function CountryServiceSetupReportSet({ countryCode, reportSetId }: CountryServiceSetupReportSetProps) {
  const router = useRouter();
  const { country } = useServiceSetup();
  const { data: reportSet, isLoading } = useQuery({
    queryKey: ["service-setup-report-set", countryCode, reportSetId],
    queryFn: () => getServiceSetupReportSet(countryCode, reportSetId),
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  useUnsavedChanges(hasUnsavedChanges);
  return (
    <>
      <CountryContent country={country} description="Edit this country's services" />
      <ModuleContent containerClassName="flex-1 bg-surface-03 pt-6">
        <div className="space-y-10">
          <UnsavedChangesBackButton hasUnsavedChanges={hasUnsavedChanges} onBack={() => router.back()} />
          <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
            <div className="space-y-2">
              <h3 className="text-primary text-xl">3. Fraction sets</h3>
              <h2 className="text-primary text-3xl font-bold">
                {reportSet?.mode
                  ? {
                      [ReportSetMode.ON_PLATAFORM]: "Reporting via platform",
                      [ReportSetMode.BY_EXCEL]: "Reporting via Excel file",
                      [ReportSetMode.NO_REPORTING]: "No reporting (i.e. flat rates)",
                      [ReportSetMode.SALES_PACKAGING]: "Sales packaging (DE)",
                    }[reportSet?.mode]
                  : ""}
              </h2>
              {reportSet && (
                <h3 className="text-tonal-dark-cream-40 text-xl">
                  EPR Compliance Packaging - {reportSet?.packaging_service?.name}
                </h3>
              )}
            </div>
            {!!reportSet && reportSet.mode !== ReportSetMode.NO_REPORTING && (
              <ReportSetPreviewLinks mode={reportSet.mode} countryCode={countryCode} id={reportSet.id} />
            )}
          </div>
          {isLoading && (
            <div className="space-y-6">
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
              <div className="bg-background rounded-[20px] p-8 space-y-4">
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-20" />
                </div>
                <div className="flex items-center justify-between gap-4">
                  <Skeleton className="h-10 flex-1" />
                  <Skeleton className="h-10 w-60" />
                </div>
              </div>
            </div>
          )}
          {!!reportSet && (
            <ReportSetFormProvider reportSet={reportSet}>
              {reportSet.mode === ReportSetMode.ON_PLATAFORM && (
                <ReportSetPlataformForm
                  countryCode={countryCode}
                  reportSet={reportSet}
                  onFormChange={setHasUnsavedChanges}
                />
              )}
              {reportSet.mode === ReportSetMode.BY_EXCEL && (
                <ReportSetExcelForm countryCode={countryCode} reportSet={reportSet} />
              )}
              {reportSet.mode === ReportSetMode.NO_REPORTING && (
                <ReportSetNoReportingForm
                  countryCode={countryCode}
                  reportSet={reportSet}
                  onFormChange={setHasUnsavedChanges}
                />
              )}
            </ReportSetFormProvider>
          )}
        </div>
      </ModuleContent>
    </>
  );
}

export const CountryServiceSetupReportSetModule = withServiceSetupProvider(CountryServiceSetupReportSet);

interface ReportSetPreviewLinksProps {
  mode: ReportSetMode;
  countryCode: string;
  id: number;
  color?: "dark-blue" | "red" | "light-blue" | "gray";
}
export const ReportSetPreviewLinks = memo(function PreviewLinks(props: ReportSetPreviewLinksProps) {
  const { mode, countryCode, id, color = "light-blue" } = props;
  return (
    <div className="flex items-center gap-1 text-primary">
      Preview:
      {mode === ReportSetMode.ON_PLATAFORM && (
        <>
          <PreviewReportTableDialog countryCode={countryCode} reportSetId={id}>
            <div>
              <Button className="underline" variant="text" color={color} size="small">
                Report Table
              </Button>
            </div>
          </PreviewReportTableDialog>
          <span>|</span>
        </>
      )}
      <PreviewCalculatorDialog countryCode={countryCode} reportSetId={id}>
        <div>
          <Button className="underline" variant="text" color={color} size="small">
            Calculator
          </Button>
        </div>
      </PreviewCalculatorDialog>
    </div>
  );
});
