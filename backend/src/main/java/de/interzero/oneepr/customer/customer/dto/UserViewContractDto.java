package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing a detailed view of a Contract for the user profile.
 * <p>
 * This DTO is a direct translation of the {@code Contract} model and its nested
 * relations as defined in the {@code contracts} include block of the
 * {@code customer.service.ts#findByUserId} method. It includes a full representation
 * of the contract and its related, filtered entities.
 */
@Data
@NoArgsConstructor
public class UserViewContractDto {

    @Schema(description = "Unique identifier of the contract.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The type of the contract.")
    @JsonProperty("type")
    private Contract.Type type;

    @Schema(description = "The status of the contract.")
    @JsonProperty("status")
    private Contract.Status status;

    @Schema(description = "The title of the contract.")
    @JsonProperty("title")
    private String title;

    @Schema(description = "The start date of the contract.")
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(description = "The end date of the contract.")
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of non-deleted action guides associated with this contract.")
    @JsonProperty("action_guides")
    private List<UserViewActionGuideDto> actionGuides;

    @Schema(description = "List of non-deleted licenses and their full details associated with this contract.")
    @JsonProperty("licenses")
    private List<UserViewLicenseDto> licenses;

    @Schema(description = "List of non-deleted general information items associated with this contract.")
    @JsonProperty("general_informations")
    private List<UserViewGeneralInformationDto> generalInformations;

    @Schema(description = "List of non-deleted files directly associated with this contract.")
    @JsonProperty("files")
    private List<FileDetailsDto> files;

    @Schema(description = "The associated termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;
}