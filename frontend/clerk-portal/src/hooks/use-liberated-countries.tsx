import { api } from "@/lib/api";
import { useQuery } from "@tanstack/react-query";

export interface PublishedCountry {
  id: number;
  name: string;
  code: string;
  flag_url: string;
  authorize_representative_obligated: boolean;
  other_costs_obligated: boolean;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

export function useLiberatedCountries() {
  const {
    data: liberatedCountries,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["liberated-countries"],
    queryFn: getLiberatedCountries,
  });

  async function getLiberatedCountries() {
    const { data, status } = await api.get("/admin/countries/published");

    if (status >= 400) throw { message: "Failed to fetch liberated countries" };

    const countries = data as PublishedCountry[];

    if (!countries) throw "Failed to fetch liberated countries";

    return countries;
  }

  return {
    liberatedCountries: liberatedCountries || [],
    loading,
    error: error?.message || "Failed to fetch liberated countries",
  };
}
