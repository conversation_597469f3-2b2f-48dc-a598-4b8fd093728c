package de.interzero.oneepr.customer.certificate;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CertificateRepository extends JpaRepository<Certificate, Integer> {

    /**
     * Finds all certificates associated with the given license ID where the
     * `deletedAt` field is null, indicating that they are not soft-deleted.
     *
     * @param licenseId the ID of the license for which associated certificates
     *                  are to be retrieved
     * @return a list of certificates associated with the specified license ID
     * that are not soft-deleted, or an empty list if none are found
     */
    List<Certificate> findByLicense_IdAndDeletedAtIsNull(Integer licenseId);

    /**
     * Retrieves a certificate by its ID where the `deletedAt` field is null,
     * indicating that the certificate is not soft-deleted.
     *
     * @param id the ID of the certificate to be retrieved
     * @return an {@code Optional} containing the certificate if found and not
     * soft-deleted, or an empty {@code Optional} if no matching certificate
     * is found
     */
    Optional<Certificate> findByIdAndDeletedAtIsNull(Integer id);
}
