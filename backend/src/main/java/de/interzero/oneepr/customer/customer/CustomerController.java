package de.interzero.oneepr.customer.customer;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.customer.dto.*;
import de.interzero.oneepr.customer.entity.CustomerTutorial;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.CUSTOMERS)
@RequiredArgsConstructor
public class CustomerController {

    private final CustomerService customerService;

    private final CustomerMapper customerMapper;

    @PreAuthorize("permitAll()")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Customer create(@RequestBody CreateCustomerDto data) {
        return customerService.create(data);
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping
    public PaginationResponse<FindAllCustomersResponseDto> findAll(@RequestParam(
                                                                           name = "page",
                                                                           defaultValue = "0"
                                                                   ) int page,
                                                                   @RequestParam(
                                                                           name = "limit",
                                                                           defaultValue = "10"
                                                                   ) int limit,
                                                                   @RequestParam(
                                                                           name = "order",
                                                                           required = false
                                                                   ) String order,
                                                                   @RequestParam(
                                                                           name = "search",
                                                                           required = false
                                                                   ) String search,
                                                                   @RequestParam(
                                                                           name = "service_type",
                                                                           required = false
                                                                   ) String serviceType,
                                                                   @RequestParam(
                                                                           name = "status",
                                                                           required = false
                                                                   ) String status,
                                                                   @RequestParam(
                                                                           name = "country_code",
                                                                           required = false
                                                                   ) String countryCode) {
        FindAllCustomersDto params = new FindAllCustomersDto();
        params.setPage(page);
        params.setLimit(limit);
        params.setSearch(search);
        if (order != null) {
            params.setOrder(FindAllCustomersDto.Order.valueOf(order.toUpperCase()));
        }
        if (serviceType != null) {
            params.setServiceType(FindAllCustomersDto.ServiceType.valueOf(serviceType.toUpperCase()));
        }
        if (status != null) {
            params.setStatus(FindAllCustomersDto.Status.valueOf(status.toUpperCase()));
        }
        params.setCountryCode(countryCode);
        Page<Customer> customers = customerService.findAll(params);
        List<Customer> customerList = customers.getContent();
        PaginationResponse<FindAllCustomersResponseDto> customerPaginationResponse = new PaginationResponse<>();
        List<FindAllCustomersResponseDto> customerDtoList = new ArrayList<>();
        if (!customerList.isEmpty()) {
            customerDtoList = customerList.stream().map(customerMapper::toFindAllCustomersResponseDto).toList();
        }
        customerPaginationResponse.setCustomers(customerDtoList);
        customerPaginationResponse.setPages(customers.getTotalPages());
        customerPaginationResponse.setCount(customers.getTotalElements());
        customerPaginationResponse.setCurrentPage(customers.getNumber() + 1);
        return customerPaginationResponse;
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/tutorials")
    public List<CustomerTutorial> findTutorialStatus(FindTutorialDto params) {
        return customerService.findTutorialStatus(params, AuthUtil.getRelevantUserDetails());
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @PostMapping("/tutorials")
    public CustomerTutorial upsertTutorialStatus(@RequestBody UpsertTutorialDto body) {
        return customerService.upsertTutorialStatus(body);
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @PostMapping("/customer-with-countries")
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, Object> createWithCountries(@RequestBody CreateCustomerCountriesDto body) {
        return customerService.createWithCountries(body);
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/by-country")
    public Map<String, GroupByCountryDto> groupByCountry() {
        return customerService.groupByCountry();
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/{id}")
    public Customer findOne(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.findById(customerId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/user/{userId}")
    public CustomerProfileDto findByUserId(@PathVariable String userId) {
        try {
            Integer parsedUserId = Integer.valueOf(userId);
            return customerService.findByUserId(parsedUserId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "User ID must be a valid number");
        }
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/by-email/{email}")
    public Customer findOneByEmail(@PathVariable String email) {
        return customerService.findOneByEmail(email, AuthUtil.getRelevantUserDetails());
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @PutMapping("/{id}")
    public Customer update(@PathVariable String id,
                           @RequestBody UpdateCustomerDto data) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.update(customerId, data, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> remove(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            customerService.remove(customerId, AuthUtil.getRelevantUserDetails());
            return ResponseEntity.ok(Map.of("statusCode", 200, "message", "Customer deleted successfully"));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/{id}/details")
    public CustomerDetailsDto details(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.details(customerId, AuthUtil.getRelevantUserDetails());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/summary/monthly")
    public Map<String, Object> getSummary() {
        return customerService.getSummary();
    }

    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/{id}/direct-license-resources")
    public DirectLicenseResourcesDto getDirectLicenseResources(@PathVariable String id) {
        try {
            Integer customerId = Integer.valueOf(id);
            return customerService.getDirectLicenseResources(customerId);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }
}