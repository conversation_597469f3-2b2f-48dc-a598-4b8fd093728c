import { Body, Controller, Param, Post } from "@nestjs/common";
import { MondayService } from "./monday.service";
import { IntegrationType, WebhookResponse } from "./interfaces/monday";
import { ApiTags } from "@nestjs/swagger";

@Controller()
@ApiTags("monday")
export class MondayController {
  constructor(private readonly mondayService: MondayService) {}

  @Post("webhook/:integration_type")
  handleWebhook(@Param("integration_type") integrationType: IntegrationType, @Body() data: WebhookResponse) {
    return data;
    // return this.mondayService.handleWebhook(integrationType, data);
  }
}
