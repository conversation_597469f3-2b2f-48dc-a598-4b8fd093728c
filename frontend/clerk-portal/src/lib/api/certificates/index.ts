import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { UploadedFile } from "../file/types";

export interface Certificate {
  id: number;
  license_id: number;
  name: string;
  status: CertificateStatus;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  files: UploadedFile[];
}

export type CertificateStatus = "PENDING" | "DONE";

export interface GetCertificatesParams {
  license_id: number;
}

export async function getCertificates({ license_id }: GetCertificatesParams) {
  try {
    const res = await api.get<Certificate[]>(ApiEndpoints.certificate.getAll, {
      params: { license_id },
    });

    return res.data;
  } catch (error: any) {
    return error;
  }
}
