package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.third_party_cost.ThirdPartyCost;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Serves as the response body for the license cost calculation endpoint, providing a comprehensive
 * preview of all potential costs and obligations.
 * <p>
 * This DTO encapsulates the final calculated {@code licenseCosts} and includes any additional
 * requirements triggered by the input data, such as the need for an authorized representative
 * ({@code authorizeRepresentativeObligated}) or lists of applicable {@link RepresentativeTier},
 * {@link ThirdPartyCost}, and {@link RequiredInformation} entities.
 * <p>
 * It is constructed dynamically by the {@code ServiceSetupService#calculateLicenseCosts} method
 * based on a series of business rule evaluations and is not mapped from a single database entity.
 */
@Data
@NoArgsConstructor
public class CalculateLicenseCostsResponseDto {

    @JsonProperty("license_costs")
    private double licenseCosts;

    @JsonProperty("authorize_representative_obligated")
    private boolean authorizeRepresentativeObligated;

    @JsonProperty("representive_tier")
    private RepresentativeTier representativeTier;

    @JsonProperty("other_costs")
    private List<ThirdPartyCost> thirdPartyCosts;

    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations;
}