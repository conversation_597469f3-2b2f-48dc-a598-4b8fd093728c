import { customerApi } from "..";
import { ApiEndpoints } from "../endpoints";
import { Coupon, CreateCouponParams, GetAllCouponsResponse } from "./types";

export async function createCoupon(params: CreateCouponParams) {
  const response = await customerApi.post(ApiEndpoints.coupon.create, params);
  return response.data;
}

type GetAllCouponsParams = {
  page?: number;
  limit?: number;
  include_uses?: boolean;
  is_active?: boolean;
  lead_type?: "all" | "link" | "code";
  code?: string;
  start_date?: string;
  end_date?: string;
};

export async function getAllCoupons(params: GetAllCouponsParams) {
  const response = await customerApi.get<GetAllCouponsResponse>(ApiEndpoints.coupon.getAllPaginated, { params });
  return response.data;
}

export async function deleteCoupon(couponId: number) {
  const response = await customerApi.delete(ApiEndpoints.coupon.delete(couponId));
  return response.data;
}

export async function getCoupon(couponId: number) {
  const response = await customerApi.get<Coupon>(ApiEndpoints.coupon.findById(couponId));
  return response.data;
}

export async function updateCoupon(couponId: number, params: CreateCouponParams) {
  const response = await customerApi.patch(ApiEndpoints.coupon.update(couponId), params);
  return response.data;
}
