"use client";

import { DashboardPlaceHolder } from "@/components/modules/dashboard/placeholder/placeholder";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getServicesCustomers } from "@/lib/api/dashboard/services-customers";
import { GraphColors } from "@/utils/graph-colors";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { ServicesCustomersChart } from "./services-customers-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

const LICENSE_MONTH_FILTERS = [
  { label: "January", value: 1 },
  { label: "February", value: 2 },
  { label: "March", value: 3 },
  { label: "April", value: 4 },
  { label: "May", value: 5 },
  { label: "June", value: 6 },
  { label: "July", value: 7 },
  { label: "August", value: 8 },
  { label: "September", value: 9 },
  { label: "October", value: 10 },
  { label: "November", value: 11 },
  { label: "December", value: 12 },
];

function ListItem({ label, value, color }: { label: string; value: number; color: string }) {
  return (
    <div className="flex flex-row items-center justify-between">
      <div className="flex flex-row gap-2 items-center">
        <div style={{ backgroundColor: color }} className="size-4 rounded-full" />
        <span className="text-sm text-tonal-dark-cream-10">{label}</span>
      </div>

      <span style={{ color }} className="font-bold">
        {value}
      </span>
    </div>
  );
}

// TODO:empty and loading states
export function ServicesCustomers() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedLicenseMonth, setSelectedLicenseMonth] = useState(LICENSE_MONTH_FILTERS[0]);
  const { data, isLoading, isSuccess, isError } = useQuery({
    queryKey: ["services-customers", selectedLicenseYear.value, selectedLicenseMonth.value],
    queryFn: () => getServicesCustomers({ year: +selectedLicenseYear.value, month: selectedLicenseMonth.value }),
  });
  const showPlaceholder = (isSuccess && data.services.length === 0) || isError;

  const graphColors = isSuccess
    ? new GraphColors(data?.services.map((service) => service.setup_packaging_service_id))
    : null;

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  function handleChangeLicenseMonth(newOrder: (typeof LICENSE_MONTH_FILTERS)[number]) {
    setSelectedLicenseMonth(newOrder);
  }

  return (
    <div id="services-customers" className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Services customers</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>

          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <span className="mr-2 text-left">{selectedLicenseMonth.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_MONTH_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseMonth(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      <p className="text-[#808FA9] -mt-4">Values in thousands</p>
      {showPlaceholder && <DashboardPlaceHolder itemName="Services Customers" className="min-h-80" />}
      <div className="grid grid-cols-3 gap-10">
        <div className="col-span-1 flex flex-col gap-6">
          {isLoading && (
            <>
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
            </>
          )}
          {isSuccess && graphColors && (
            <>
              {data.services.map((service, idx) => {
                const color = graphColors.getColor(service.setup_packaging_service_id);
                return <ListItem key={idx} label={service.label} value={service.total} color={color} />;
              })}
            </>
          )}
        </div>

        <div className="col-span-2">
          {isLoading && (
            <>
              <Skeleton className="h-64" />
            </>
          )}

          {isSuccess && data.services.length > 0 && graphColors && (
            <ServicesCustomersChart data={data} graphColors={graphColors} />
          )}
        </div>
      </div>
    </div>
  );
}
