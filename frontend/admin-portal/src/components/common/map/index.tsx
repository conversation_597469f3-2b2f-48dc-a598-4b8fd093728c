/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Country } from "@/types/country";
import { cn } from "@/utils/cn";
import Image from "next/image";
import { useState } from "react";
import Map, { Layer, LayerProps, MapStyle, Source } from "react-map-gl";
import Mapbox from "mapbox-gl";
import countriesPolygon from "./countries.json";
import mapStyle from "./mapStyle.json";

interface IMapCountriesProps {
  onSelectCountry?: (name: string) => void;
  selectedCountries?: string[];
  children?: React.ReactNode;
  countries?: Country[];
  initialViewState?: {
    minZoom?: number;
    maxZoom?: number;
    latitude?: number;
    longitude?: number;
    zoom?: number;
  };
  className?: string;
  dealCountryCodes?: string[];
}

interface HoveredCountry {
  country: Country;
  isFromDeal: boolean;
  position: {
    x: number;
    y: number;
  };
}

export const MapCountries = ({
  children,
  onSelectCountry,
  selectedCountries,
  countries = [],
  initialViewState,
  className,
  dealCountryCodes = [],
}: IMapCountriesProps) => {
  const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  const [selectCountriesInternally, setSelectCountriesInternally] = useState<string[]>([]);
  const [hoveredCountry, setHoveredCountry] = useState<HoveredCountry | null>(null);

  const polygonLayer: LayerProps = {
    id: "countries",
    type: "fill-extrusion",
    paint: {
      "fill-extrusion-color": [
        "case",
        ["in", ["get", "iso_a2_eh"], ["literal", dealCountryCodes]], // Case country code in deal country codes
        "#002652", // Then paint with primary
        [
          "case",
          ["in", ["get", "iso_a2_eh"], ["literal", selectedCountries || selectCountriesInternally]], // Case country code in selected countries
          "#009DD3", // Then paint with light blue
          [
            "case",
            ["==", ["get", "iso_a2_eh"], ["literal", hoveredCountry?.country.code || ""]], // Case country code not selected and hover
            "#A6A4A2", // Then paint with light gray
            "#D7D6D5", // Else paint with gray
          ],
        ],
      ],
      "fill-extrusion-opacity": 1,
    },
  };

  const lineLayer: LayerProps = {
    id: "countries-line-layer",
    type: "line",
    source: "countries",
    layout: {},
    paint: {
      "line-color": "#FFF",
      "line-width": 0.1,
    },
  };

  function handleHoverCountry(event: mapboxgl.MapLayerMouseEvent) {
    const {
      features,
      point: { x, y },
    } = event;

    if (!features || !features[0]) {
      setHoveredCountry(null);
      return;
    }

    const feature = features[0];
    const countryCode: string = feature.properties?.iso_a2_eh;

    if (!countryCode) {
      setHoveredCountry(null);
      return;
    }

    const matchingCountry = countries.find((country) => country.code === countryCode);

    if (!matchingCountry) {
      setHoveredCountry(null);
      return;
    }

    setHoveredCountry({
      country: matchingCountry,
      position: { x, y: y - 25 },
      isFromDeal: dealCountryCodes.includes(countryCode),
    });
  }

  function handleClickCountry(event: mapboxgl.MapLayerMouseEvent) {
    const { features } = event;

    if (!features || !features[0]) return;

    const feature = features[0];
    const countryCode: string = feature.properties?.iso_a2_eh;

    if (!countryCode) return;

    if (dealCountryCodes.includes(countryCode)) return;

    onSelectCountry && onSelectCountry(countryCode);

    if (selectedCountries) return;

    if (selectCountriesInternally.includes(countryCode)) {
      setSelectCountriesInternally((current) => current.filter((country) => country !== countryCode));

      return;
    }

    setSelectCountriesInternally((current) => [...current, countryCode]);
  }

  return (
    <div
      className={cn("rounded-[40px] w-full h-full overflow-hidden", className)}
      style={{
        cursor: !hoveredCountry?.isFromDeal ? "pointer" : "auto",
      }}
    >
      <Map
        mapLib={Mapbox as any}
        mapboxAccessToken={mapboxToken}
        mapStyle={mapStyle as MapStyle}
        initialViewState={{ latitude: 46.8182, longitude: 8.2275, zoom: 3, ...initialViewState }}
        maxZoom={4}
        minZoom={3}
        pitch={0}
        bearing={0}
        doubleClickZoom={false}
        interactiveLayerIds={["countries"]}
        onClick={handleClickCountry}
        onMouseMove={handleHoverCountry as any}
      >
        <Source type="geojson" data={countriesPolygon as GeoJSON.GeoJSON}>
          <Layer {...polygonLayer} />
          <Layer {...lineLayer} />
        </Source>
        {hoveredCountry && (
          <div
            className="absolute bg-surface-04 py-1 px-3 rounded-lg z-50 pointer-events-none cursor-pointer"
            style={{ left: hoveredCountry.position.x, top: hoveredCountry.position.y }}
          >
            <div>
              <div className="flex gap-2">
                <Image
                  src={hoveredCountry.country.flag_url}
                  alt="flag country"
                  className="rounded-full w-4 h-4"
                  width={16}
                  height={16}
                />
                <p className="text-primary">{hoveredCountry.country.name}</p>
              </div>
              {hoveredCountry.isFromDeal && <p className="text-xs text-primary">Already purchased</p>}
            </div>
          </div>
        )}
        {children}
      </Map>
    </div>
  );
};
