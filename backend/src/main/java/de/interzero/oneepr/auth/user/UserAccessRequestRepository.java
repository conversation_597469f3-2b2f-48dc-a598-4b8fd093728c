package de.interzero.oneepr.auth.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for {@link UserAccessRequest} entities.
 * Provides access methods for handling user access requests.
 */
@Repository
public interface UserAccessRequestRepository extends JpaRepository<UserAccessRequest, Integer> {

    /**
     * Finds the first user access request matching the given token and status.
     *
     * @param token  the access request token
     * @param status the status of the request (e.g., "PENDING", "APPROVED")
     * @return an {@link Optional} containing the matching {@link UserAccessRequest}, or empty if not found
     */
    Optional<UserAccessRequest> findFirstByTokenAndStatus(String token,
                                                          String status);
}
