import { CalculatorLicense } from "@/components/modules/customers/components/add-countries-to-licensing-form/calculator/interface";
import { cartColumnToCartColumn, cartFractionToCartFraction } from "./column-fraction-inverter";
import { PriceList } from "@/lib/api/commitment/types";

export function createPurchaseObject(customerId: number, calculatorLicense: CalculatorLicense, priceList: PriceList) {
  return {
    customer_id: customerId,
    action_guides: Object.values(calculatorLicense.items || {})
      .filter((item) => !!item.actionGuide)
      .map((item) => ({
        country_id: item.country.id,
        country_code: item.country.code,
        country_name: item.country.name,
        country_flag: item.country.flag_url,
        price_list: {
          id: Number(item.actionGuide!.priceList.id),
          name: item.actionGuide!.priceList.title,
          description: item.actionGuide!.priceList.description,
          price: item.actionGuide?.priceList.price,
        },
      })),
    eu_licenses: Object.values(calculatorLicense.items || {})
      .filter((item) => !!item.license && item.license.type === `EU_LICENSE`)
      .map((item) => ({
        country_id: item.country.id,
        country_code: item.country.code,
        country_name: item.country.name,
        country_flag: item.country.flag_url,
        year: Number(item.license!.year),
        // TODO Check booleans obligation
        authorize_representative_obligated: false,
        other_costs_obligated: false,
        representative_tiers: item.license?.representativeTier ? [item.license!.representativeTier] : [],
        other_costs: item.license!.otherCosts || [],
        required_informations: item.license!.requiredInformations.map((info) => ({
          ...info,
          file_id: info.fileId,
        })),
        price_list: priceList,
        packaging_services: Object.values(item.license!.packagingServices).map((packagingService) => ({
          id: Number(packagingService.id),
          name: packagingService.title,
          description: packagingService.title,
          report_set: {
            id: Number(packagingService.reportSet.id),
            name: packagingService.reportSet.name,
            mode: packagingService.reportSet.mode,
            type: packagingService.reportSet.type,
          },
          report_set_frequency: {
            id: Number(packagingService.reportFrequency.id),
            rhythm: packagingService.reportFrequency.rhythm,
            frequency: packagingService.reportFrequency.frequency,
          },
          report_table: {
            fractions: packagingService.reportSet.fractions.map(cartFractionToCartFraction),
            columns: packagingService.reportSet.columns.map(cartColumnToCartColumn),
          },
        })),
      })),
    direct_license: (() => {
      const germanyItem =
        Object.values(calculatorLicense.items || {}).find((item) => item.country.code === "DE") || null;

      if (!germanyItem || !germanyItem.license) return undefined;

      return {
        country_id: germanyItem.country.id,
        country_code: germanyItem.country.code,
        country_name: germanyItem.country.name,
        country_flag: germanyItem.country.flag_url,
        year: Number(germanyItem.license!.year),
        report_table: {},
        authorize_representative_obligated: false,
        other_costs_obligated: false,
        other_costs: germanyItem.license!.otherCosts || [],
        required_informations: germanyItem.license!.requiredInformations.map((info) => ({
          ...info,
          file_id: info.fileId,
        })),
        price_list: {
          id: Number(germanyItem.license!.priceList.id),
          name: germanyItem.license!.priceList.name,
          description: germanyItem.license!.priceList.description,
          condition_type: germanyItem.license!.priceList.conditionType,
          condition_type_value: germanyItem.license!.priceList.conditionTypeValue,
          start_date: germanyItem.license!.priceList.startDate,
          end_date: germanyItem.license!.priceList.endDate,
          basic_price: germanyItem.license!.priceList.basicPrice,
          minimum_price: germanyItem.license!.priceList.minimumPrice,
          registration_fee: germanyItem.license!.priceList.registrationFee,
          handling_fee: germanyItem.license!.priceList.handlingFee,
          variable_handling_fee: germanyItem.license!.priceList.variableHandlingFee,
        },
        packaging_services: Object.values(germanyItem.license!.packagingServices).map((packagingService) => ({
          id: Number(packagingService.id),
          name: packagingService.title,
          description: packagingService.title,
          report_set: {
            id: Number(packagingService.reportSet.id),
            name: packagingService.reportSet.name,
            mode: packagingService.reportSet.mode,
            type: packagingService.reportSet.type,
          },
          report_set_frequency: {
            id: Number(packagingService.reportFrequency.id),
            rhythm: packagingService.reportFrequency.rhythm,
            frequency: packagingService.reportFrequency.frequency,
          },
          report_table: {
            fractions: packagingService.reportSet.fractions.map(cartFractionToCartFraction),
            columns: packagingService.reportSet.columns.map(cartColumnToCartColumn),
          },
        })),
      };
    })(),
  };
}
