package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Filters for finding users")
@Data
public class UserFindAllDto {

    @JsonProperty("role")
    @Schema(
            description = "Role of the user",
            example = "ADMIN,USER",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String role;

    @JsonProperty("is_active")
    @Schema(
            description = "User is active",
            example = "true",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String isActive;

    @JsonProperty("ids")
    @Schema(
            description = "Ids of the user",
            example = "1,2,3",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String ids;

    @JsonProperty("name")
    @Schema(
            description = "Name of the user",
            example = "<PERSON> Do<PERSON>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String name;
}
