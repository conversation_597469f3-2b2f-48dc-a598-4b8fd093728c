package de.interzero.oneepr.auth.user;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for {@link PasswordResetRequest} entities.
 * Provides methods to handle password reset request lookups.
 */
@Repository
public interface PasswordResetRequestRepository extends JpaRepository<PasswordResetRequest, Integer> {

    /**
     * Finds the first password reset request matching the given token and status.
     *
     * @param token  the token associated with the password reset request
     * @param status the status of the request (e.g., "PENDING", "COMPLETED")
     * @return an {@link Optional} containing the matching {@link PasswordResetRequest}, or empty if not found
     */
    Optional<PasswordResetRequest> findFirstByTokenAndStatus(String token,
                                                             String status);
}
