export interface LicenseServiceContract {
  customerName: string;
  country: string;
  purchaseStatus?: string;
  registrationFee?: number;
  handlingFee?: number;
  volumeDependent?: number;
  serviceType?: string;
  startingYear?: string;
  endingYear?: string;
  terminationDate?: string;
}

export enum IntegrationType {
  VOLUME_REPORTS = "VOLUME_REPORTS",
  REGISTRATION_AND_TERMINATIONS = "REGISTRATION_AND_TERMINATIONS",
  THIRD_PARTY_INVOICES = "THIRD_PARTY_INVOICES",
}

export class WebhookResponse {
  event: WebhookEvent;
}

export class WebhookEvent {
  value: WebhookEventValue;
  previousValue: WebhookPreviousValue;
  pulseId: number;
}

export class WebhookEventValue {
  label: EventLabel;
}

export class WebhookPreviousValue {
  label: EventLabel;
}

export class EventLabel {
  text: string;
}
