package de.interzero.oneepr.customer.customer_invite_token;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "customer_invite_token",
        schema = "public"
)
public class CustomerInviteToken {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_invite_token_id_gen"
    )
    @SequenceGenerator(
            name = "customer_invite_token_id_gen",
            sequenceName = "customer_invite_token_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "token",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("token")
    private String token;

    @NotNull
    @Column(
            name = "share_link",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("share_link")
    private String shareLink;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "customer_id")
    @JsonIgnore
    @JsonProperty("customer")
    private Customer customer;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Column(name = "expiration_date")
    @JsonProperty("expiration_date")
    private LocalDate expirationDate;

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return customer != null ? customer.getId() : null;
    }
}