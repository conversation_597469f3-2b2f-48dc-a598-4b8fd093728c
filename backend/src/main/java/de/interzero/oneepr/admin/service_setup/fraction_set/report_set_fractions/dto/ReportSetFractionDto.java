package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.FractionIconNestedDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.Instant;

/**
 * This DTO is a direct translation of the ReportSetFraction interface from the NestJS source.
 * It is designed to be the serializable response object for a single ReportSetFraction.
 */
@Data
public class ReportSetFractionDto {

    @Schema(description = "The unique identifier of the fraction.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The ID of the parent fraction, if this is a child fraction.")
    @JsonProperty("parent_id")
    private Integer parentId;

    @Schema(description = "A unique code for the fraction, used for linking.")
    @JsonProperty("code")
    private String code;

    @Schema(description = "The code of the parent fraction, if this is a child fraction.")
    @JsonProperty("parent_code")
    private String parentCode;

    @Schema(description = "The display name of the fraction.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "A detailed description of the fraction's purpose.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "The name of the icon associated with the fraction.")
    @JsonProperty("icon")
    private String icon;

    @Schema(description = "The ID of the associated fraction icon entity.")
    @JsonProperty("fraction_icon_id")
    private Integer fractionIconId;

    @Schema(description = "The nested fraction icon object.")
    @JsonProperty("fraction_icon")
    private FractionIconNestedDto fractionIcon;

    @Schema(description = "Indicates if the fraction is currently active.")
    @JsonProperty("is_active")
    private boolean isActive;

    @Schema(description = "The ID of the report set this fraction belongs to.")
    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @Schema(description = "The nesting level of the fraction in the hierarchy (1 for root).")
    @JsonProperty("level")
    private Integer level;

    @Schema(description = "The display order of the fraction among its siblings.")
    @JsonProperty("order")
    private Integer order;

    @Schema(description = "Indicates if this fraction has second-level children.")
    @JsonProperty("has_second_level")
    private boolean hasSecondLevel;

    @Schema(description = "Indicates if this fraction has third-level children.")
    @JsonProperty("has_third_level")
    private boolean hasThirdLevel;

    @Schema(description = "The timestamp when the fraction was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp when the fraction was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The timestamp when the fraction was soft-deleted.")
    @JsonProperty("deleted_at")
    private Instant deletedAt;
}
