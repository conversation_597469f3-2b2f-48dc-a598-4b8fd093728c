import { PaymentMethods } from "@/types/dashboard/payment-methods";
import { GraphColors } from "@/utils/graph-colors";
import { formatPaymentMethodLabel } from "@/utils/payment-method-labels";

function ListItem({ label, value, color }: { label: string; value: number; color: string }) {
  return (
    <div className="flex flex-row items-center justify-between">
      <div className="flex flex-row gap-2">
        <div style={{ backgroundColor: color }} className="size-4 rounded-full" />
        <span className="text-sm text-tonal-dark-cream-10">{label}</span>
      </div>

      <span className="text-on-tertiary font-bold">{value}</span>
    </div>
  );
}

export function PaymentMethodList({ data, graphColors }: { data: PaymentMethods; graphColors: GraphColors }) {
  return (
    <div className="flex flex-col gap-4 border-t border-t-tonal-dark-cream-80 pt-6">
      {data.payment_methods.map((method, idx) => (
        <ListItem
          key={idx}
          label={formatPaymentMethodLabel(method, false)}
          value={method.total_transactions}
          color={graphColors.getColor(method.label)}
        />
      ))}
    </div>
  );
}
