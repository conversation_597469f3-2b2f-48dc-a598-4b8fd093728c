"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, Delete, Error, Visibility, VisibilityOff } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext, useWatch } from "react-hook-form";
import { AddColumnFractionsDialog } from "./add-column-fractions-dialog";
import { FormColumn, ReportSetFormData } from "./report-set-form-provider";
import { generateCode } from "@/utils/generate-code";
import { DownRightIcon } from "@/components/ui/icons/DownRightIcon";

const UNIT_TYPES = [
  { label: "Kg", value: "KG" },
  { label: "Units", value: "UNITS" },
  { label: "Each", value: "EACH" },
];

export function ReportSetColumns() {
  const {
    register,
    formState: { errors },
    control,
    setValue,
    getValues,
  } = useFormContext<ReportSetFormData>();

  function handleAddColumn(data: {
    name: string;
    description: string;
    icon: string;
    is_active: boolean;
    parent_code?: string;
    children_visible?: boolean;
  }) {
    const currentColumns = getValues("columns");

    if (!currentColumns) return;
    if (!data.parent_code) {
      setValue("columns", [
        ...currentColumns,
        {
          parent_id: null,
          code: generateCode(),
          parent_code: null,
          name: data.name,
          description: data.description,
          unit_type: UNIT_TYPES[0].value as "KG" | "UNITS" | "EACH",
          level: 1,
          order: currentColumns.length + 1,
          children: [],
          fractions: [],
          children_visible: data.children_visible || false,
        },
      ]);
      return;
    }

    const foundParentColumn = findColumnByCode(data.parent_code);

    if (!foundParentColumn) return;

    // Parent level 1
    if (!foundParentColumn.firstLevelParent) {
      setValue(`columns.${foundParentColumn.column.order - 1}`, {
        ...foundParentColumn.column,
        children_visible: true,
        children: [
          ...foundParentColumn.column.children,
          {
            code: generateCode(),
            parent_code: data.parent_code,
            name: "",
            description: "",
            unit_type: foundParentColumn.column.unit_type,
            parent_id: foundParentColumn.column.id || null,
            fractions: [],
            children_visible: false,
            level: foundParentColumn.column.level + 1,
            order: foundParentColumn.column.children.length + 1,
          },
        ],
      });
      return;
    }
  }

  function handleUpdateColumn(code: string, data: Partial<FormColumn>) {
    const foundColumn = findColumnByCode(code);

    if (!foundColumn) return;

    if (!foundColumn.firstLevelParent) {
      setValue(`columns.${foundColumn.column.order - 1}`, {
        ...foundColumn.column,
        ...data,
      });
    }

    if (foundColumn.firstLevelParent) {
      setValue(`columns.${foundColumn.firstLevelParent.order - 1}.children.${foundColumn.column.order - 1}`, {
        ...foundColumn.column,
        ...data,
      });
    }
  }

  function handleRemoveColumn(code: string) {
    const foundColumn = findColumnByCode(code);

    if (!foundColumn) return;

    if (!foundColumn.firstLevelParent) {
      const currentColumns = getValues("columns") || [];

      const formattedColumns = currentColumns
        .filter((column) => column.code !== code)
        .map((column, index) => ({
          ...column,
          order: index + 1,
        }));

      setValue("columns", formattedColumns);
      return;
    }

    if (foundColumn.firstLevelParent) {
      const currentColumns = getValues(`columns.${foundColumn.firstLevelParent.order - 1}.children`) || [];

      const formattedColumns = currentColumns
        .filter((column) => column.code !== code)
        .map((column, index) => ({
          ...column,
          order: index + 1,
        }));

      setValue(`columns.${foundColumn.firstLevelParent.order - 1}.children`, formattedColumns);
      return;
    }
  }

  function handleAddColumnFractions(columnCode: string, fractions: string[]) {
    const foundColumn = findColumnByCode(columnCode);

    if (!foundColumn || !foundColumn.firstLevelParent) return;

    setValue(
      `columns.${foundColumn.firstLevelParent.order - 1}.children.${foundColumn.column.order - 1}.fractions`,
      fractions.map((fraction) => ({
        column_code: foundColumn.column.code,
        fraction_code: fraction,
      }))
    );
  }

  function findColumnByCode(code: string) {
    const firstLevelColumns = getValues("columns");

    if (!firstLevelColumns) return null;

    const firstLevelColumn = firstLevelColumns.find((firstLevelColumn) => firstLevelColumn.code === code);

    if (firstLevelColumn) {
      return {
        column: firstLevelColumn,
        firstLevelParent: null,
      };
    }

    for (const firstLevelColumn of firstLevelColumns) {
      const secondLevelColumn = firstLevelColumn.children.find((child) => child.code === code);

      if (secondLevelColumn) {
        return {
          column: secondLevelColumn,
          firstLevelParent: firstLevelColumn,
        };
      }
    }

    return null;
  }

  const columns = useWatch({ control, name: "columns" });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        {!!errors.columns && (
          <div className="flex items-center gap-2">
            <Error className="size-5 fill-error" />
            <p className="text-error text-sm">{errors.columns.message}</p>
          </div>
        )}
      </div>
      {columns?.length ? (
        <div>
          {columns?.map((firstLevelColumn, firstLevelColumnIndex) => (
            <div key={firstLevelColumn.code} className="space-y-4">
              <div className="flex items-center gap-3">
                <p className="text-primary text-lg font-bold flex-1">
                  Column list ({(firstLevelColumnIndex + 1).toString(10).padStart(2, "0")})
                </p>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <button
                      type="button"
                      className="text-sm font-bold text-error hover:bg-error/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:text-tonal-dark-cream-40 disabled:bg-white rounded-full py-1 px-3"
                      disabled={firstLevelColumn.children.length >= 1}
                    >
                      Delete group
                    </button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete column group?</AlertDialogTitle>
                      <AlertDialogDescription>
                        By clicking on ”confirm” you are deleting this column group and all content within it.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Back</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleRemoveColumn(firstLevelColumn.code)}>
                        Confirm
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-6 gap-3 lg:gap-6">
                <div className="lg:col-span-2">
                  <Input
                    label="Name"
                    placeholder="Name"
                    {...register(`columns.${firstLevelColumnIndex}.name`)}
                    variant={errors.columns?.[firstLevelColumnIndex]?.name ? "error" : "default"}
                    errorMessage={errors.columns?.[firstLevelColumnIndex]?.name?.message}
                  />
                </div>
                <div className="lg:col-span-3">
                  <Textarea
                    label="Description"
                    placeholder="Description"
                    rows={3}
                    {...register(`columns.${firstLevelColumnIndex}.description`)}
                    errorMessage={errors.columns?.[firstLevelColumnIndex]?.description?.message}
                  />
                </div>
                <div className="lg:col-span-1">
                  <div className="space-y-2">
                    <label className="hidden lg:block">&nbsp;</label>
                    <Controller
                      control={control}
                      name={`columns.${firstLevelColumnIndex}.unit_type`}
                      render={({ field: { value, onChange } }) => (
                        <Select
                          defaultValue={UNIT_TYPES[0].value}
                          value={value as string}
                          onValueChange={onChange}
                          disabled={firstLevelColumn.children.length >= 1}
                        >
                          <SelectTrigger className="h-10 w-full lg:w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {UNIT_TYPES.map((unitType) => (
                              <SelectItem key={unitType.value} value={unitType.value}>
                                {unitType.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                </div>
              </div>
              <div className="px-4 py-6 bg-surface-02 rounded-sm">
                <div className="space-y-6">
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      <DownRightIcon className="stroke-tonal-dark-cream-80 flex-none -mt-4" />
                      <p className="text-tonal-dark-cream-40">Sub-columns (level 02)</p>
                      <Button
                        type="button"
                        variant="text"
                        color="gray"
                        size="iconSmall"
                        onClick={() =>
                          handleUpdateColumn(firstLevelColumn.code, {
                            children_visible: !firstLevelColumn.children_visible,
                          })
                        }
                        leadingIcon={
                          firstLevelColumn.children_visible ? (
                            <Visibility className=" fill-tonal-dark-cream-50" />
                          ) : (
                            <VisibilityOff className=" fill-tonal-dark-cream-50" />
                          )
                        }
                      />
                      <Button
                        type="button"
                        variant="text"
                        color="light-blue"
                        size="small"
                        onClick={() =>
                          handleAddColumn({
                            name: "",
                            description: "",
                            icon: "",
                            is_active: true,
                            children_visible: false,
                            parent_code: firstLevelColumn.code,
                          })
                        }
                      >
                        Add Sub Columns
                      </Button>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        {!!errors.columns?.[firstLevelColumnIndex]?.children?.length && (
                          <Error className="w-6 h-6 fill-error" />
                        )}
                        {!!firstLevelColumn.children.length && (
                          <p className="flex items-center gap-1 text-tonal-dark-cream-40 text-sm">
                            <span>{firstLevelColumn.children.length}</span>
                            {firstLevelColumn.children.length > 1 ? "items" : "item"}
                          </p>
                        )}
                      </div>
                      <Controller
                        control={control}
                        name={`columns.${firstLevelColumnIndex}.unit_type`}
                        render={({ field: { value, onChange } }) => (
                          <Select
                            defaultValue={firstLevelColumn.unit_type}
                            value={value as string}
                            onValueChange={onChange}
                          >
                            <SelectTrigger className="h-10 w-full lg:w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {UNIT_TYPES.map((unitType) => (
                                <SelectItem key={unitType.value} value={unitType.value}>
                                  {unitType.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                  {firstLevelColumn.children_visible &&
                    firstLevelColumn.children.map((secondLevelColumn, secondLevelColumnIndex) => (
                      <div key={secondLevelColumnIndex} className="space-y-6">
                        <div className="pl-8 space-y-6">
                          <div className="grid grid-cols-5 gap-6">
                            <div className="col-span-2">
                              <div className="flex items-end gap-2 text-primary text-sm">
                                <Button
                                  type="button"
                                  variant="text"
                                  color="dark-blue"
                                  size="iconXSmall"
                                  leadingIcon={<Delete className="fill-primary" />}
                                  onClick={() => handleRemoveColumn(secondLevelColumn.code)}
                                />
                                Name
                                <span className="text-tonal-dark-cream-40 italic">Sub-columns (level 02)</span>
                              </div>
                              <Input
                                placeholder="Name"
                                {...register(
                                  `columns.${firstLevelColumnIndex}.children.${secondLevelColumnIndex}.name`
                                )}
                                variant={
                                  errors.columns?.[firstLevelColumnIndex]?.children?.[secondLevelColumnIndex]?.name
                                    ? "error"
                                    : "default"
                                }
                                errorMessage={
                                  errors.columns?.[firstLevelColumnIndex]?.children?.[secondLevelColumnIndex]?.name
                                    ?.message
                                }
                              />
                              <div className="flex items-center gap-2 mt-2">
                                {!!secondLevelColumn.fractions.length && (
                                  <span className="text-tonal-dark-cream-40 text-sm">
                                    {secondLevelColumn.fractions.length} fractions selected
                                  </span>
                                )}
                                <AddColumnFractionsDialog
                                  key={secondLevelColumn.code}
                                  columnCode={secondLevelColumn.code}
                                  onAdd={(data) => handleAddColumnFractions(secondLevelColumn.code, data)}
                                />
                              </div>
                            </div>
                            <div className="col-span-3">
                              <Textarea
                                label="Description"
                                placeholder="Description"
                                rows={3}
                                {...register(
                                  `columns.${firstLevelColumnIndex}.children.${secondLevelColumnIndex}.description`
                                )}
                                errorMessage={
                                  errors.columns?.[firstLevelColumnIndex]?.children?.[secondLevelColumnIndex]
                                    ?.description?.message
                                }
                              />
                            </div>
                          </div>
                        </div>
                        <div className="h-[1px] w-full bg-tonal-dark-cream-80" />
                      </div>
                    ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : null}
      <Button
        type="button"
        variant="text"
        color="light-blue"
        size="small"
        leadingIcon={<Add />}
        onClick={() =>
          handleAddColumn({
            name: "",
            description: "",
            icon: "",
            is_active: true,
            children_visible: false,
          })
        }
      >
        Add Column group
      </Button>
    </div>
  );
}
