"use client";

import { useState } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { z } from "zod";

import { Input } from "@interzero/oneepr-react-ui/Input";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { PhoneInput } from "@/components/common/phone-input";
import { Skeleton } from "@/components/ui/skeleton";
import type { UpdateBrokerDto } from "@/lib/api/broker/types";
import { getBroker, updateBroker } from "@/lib/api/broker";
import { queryClient } from "@/lib/react-query";

const editBrokerContactInfoFormSchema = z.object({
  email: z.string().email("Invalid e-mail").min(1, "Required"),
  phone: z.string().optional(),
});

type EditBrokerContactInfoFormData = z.infer<typeof editBrokerContactInfoFormSchema>;

interface EditBrokerContatoInfoDialogProps {
  brokerId: number;
  children: React.ReactNode;
}

export function EditBrokerContatoInfoDialog({ brokerId, children }: EditBrokerContatoInfoDialogProps) {
  const [open, setOpen] = useState(false);

  const { data: brokerData, status: queryStatus } = useQuery({
    queryKey: ["broker", brokerId],
    queryFn: async () => getBroker(brokerId),
    enabled: !!brokerId,
  });

  const defaultValues: Partial<EditBrokerContactInfoFormData> = {
    email: brokerData?.email || "",
    phone: brokerData?.phone || "",
  };

  const { formState, ...form } = useForm<EditBrokerContactInfoFormData>({
    resolver: zodResolver(editBrokerContactInfoFormSchema),
    defaultValues,
  });

  const { phone: phoneWatch } = useWatch({ control: form.control });

  const { mutateAsync } = useMutation({
    mutationFn: (data: EditBrokerContactInfoFormData) => {
      const payload = {
        email: data.email,
        phone: data.phone,
      } as UpdateBrokerDto;

      return updateBroker(brokerId, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["broker", brokerId] });
      enqueueSnackbar("Broker contact information updated successfully", { variant: "success" });
      setOpen(false);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleFormSubmit(data: EditBrokerContactInfoFormData) {
    await mutateAsync(data);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl !z-[1001]">
        <DialogHeader>
          <DialogTitle className="font-bold sm:text-[1.75rem] text-support-blue">
            Edit Broker contact informations
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="my-10 grid items-start sm:grid-cols-2 gap-8">
          {queryStatus === "pending" && (
            <>
              <div className="space-y-2">
                <Skeleton className="h-3 w-1/3" />
                <Skeleton className="h-5 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-3 w-1/3" />
                <Skeleton className="h-5 w-full" />
              </div>
              <div className="col-span-full flex justify-end">
                <Skeleton className="w-[200px] h-14 rounded-full" />
              </div>
            </>
          )}
          {queryStatus === "success" && (
            <>
              <Controller
                name="email"
                control={form.control}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    label="E-mail *"
                    type="email"
                    placeholder="E-mail"
                    {...field}
                    variant={!!error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                )}
              />
              <div className="flex flex-col gap-2">
                <label htmlFor="mobile-phone" className="text-primary">
                  Mobile
                </label>
                <PhoneInput
                  id="mobile-phone"
                  name="phone"
                  defaultValue={phoneWatch}
                  valueSetter={(value) => form.setValue("phone", value || ``)}
                  errorSetter={(valid) =>
                    valid ? form.clearErrors("phone") : form.setError("phone", { message: "Invalid phone number." })
                  }
                  isError={!!formState.errors.phone}
                  contentClassName="z-[9999]"
                  isModal
                />
                {!!formState.errors.phone && (
                  <span className="text-sm text-error">{formState.errors.phone.message}</span>
                )}
              </div>
              <DialogFooter className="col-span-full flex justify-end">
                <Button variant="filled" color="yellow" size="medium" disabled={formState.isSubmitting}>
                  {formState.isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </DialogFooter>
            </>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
}
