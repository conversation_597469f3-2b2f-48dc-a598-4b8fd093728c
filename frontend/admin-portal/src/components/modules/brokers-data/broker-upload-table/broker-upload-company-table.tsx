"use client";

import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import { CheckCompanyExcel } from "@/lib/api/broker-file/types";
import { createColumnHelper } from "@tanstack/react-table";
import { InfoTable, RenderInfoTable } from "./broker-info-table";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { useState } from "react";

const columnHelper = createColumnHelper<CheckCompanyExcel>();

const columns = [
  columnHelper.accessor("broker_company_id", {
    header: () => <span className="text-sm text-primary">Broker Company Id</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("name", {
    header: () => <span className="text-sm text-primary">Company Name</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("register_number", {
    header: () => <span className="text-sm text-primary">Reg. Nr.</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("vat", {
    header: () => <span className="text-sm text-primary">VAT ID</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("tax", {
    header: () => <span className="text-sm text-primary">Tax</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("country_code", {
    header: () => <span className="text-sm text-primary">Tax Nr. Country CD</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("address_street", {
    header: () => <span className="text-sm text-primary">Adress</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("city", {
    header: () => <span className="text-sm text-primary">City</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("contact_name", {
    header: () => <span className="text-sm text-primary">Contact</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("phone_number", {
    header: () => <span className="text-sm text-primary">Phone Number</span>,
    cell: (info) => <RenderInfoTable info={info as InfoTable} />,
  }),
  columnHelper.accessor("errors", {
    header: () => <span className="text-sm text-primary">Error</span>,
    cell: ({ row }) => {
      const uniqueValues = [...new Set(Object.values(row.original.errors || {}))];
      const errors = uniqueValues.join(", ");

      return (
        <p data-error={!!errors} className="text-primary data-[error=true]:text-error">
          {errors || "----"}
        </p>
      );
    },
  }),
];

export function BrokerUploadCompanyTable({ data }: { data: CheckCompanyExcel[] }) {
  const [search, setSearch] = useState("");

  const filterData = data.filter((item) => JSON.stringify(item).toLowerCase().includes(search.toLowerCase()));

  return (
    <div>
      <div className="flex justify-between items-center my-10">
        <DatatableSearch onSearch={(value) => setSearch(value)} />
        <p className="text-tonal-dark-cream-30">{data.length} Results</p>
      </div>
      <AsyncPaginatedTable
        columns={columns}
        data={filterData}
        currentPage={1}
        pages={1}
        onPageChange={() => {}}
        noResultsMessage={"no data"}
      />
    </div>
  );
}
