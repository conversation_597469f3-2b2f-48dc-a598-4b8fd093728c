import { AccountCircle, Add } from "@interzero/oneepr-react-ui/Icon";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";

import { AddNewBrokerDialog } from "./add-new-broker-dialog";
import { BrokersDataTable } from "./brokers-data-table";

export function BrokersDataModule() {
  return (
    <ModuleContent>
      <div className="flex items-start justify-between gap-4 mb-5">
        <ModuleTitle
          icon={AccountCircle}
          title="Brokers Data"
          description="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
        <AddNewBrokerDialog>
          <Button type="button" variant="filled" size="small" color="yellow" leadingIcon={<Add />}>
            Add new Broker
          </Button>
        </AddNewBrokerDialog>
      </div>
      <BrokersDataTable />
    </ModuleContent>
  );
}
