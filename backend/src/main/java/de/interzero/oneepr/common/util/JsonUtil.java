package de.interzero.oneepr.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;
/**
 * A utility class for handling JSON string conversions.
 * <p>
 * <b>Where:</b> This utility is used during the mapping from a JPA Entity to a response DTO,
 * specifically for fields that are stored as JSON strings in the database but need to be

 * represented as nested JSON objects in the API response.
 * <p>
 * <b>What:</b> It provides a static method, {@code parseJsonString}, which takes a string
 * containing JSON data and uses a Jackson {@code ObjectMapper} to parse it into a generic
 * {@code Object}. This object is typically a {@code Map<String, Object>} or a {@code List<Object>},
 * which <PERSON> can then serialize into a proper nested JSON structure.
 * <p>
 * <b>Why:</b> This class is a direct translation of the {@code JSON.parse()} pattern found in the
 * original NestJS service. It is required to maintain API response equivalence. Without this
 * parsing step, a field stored as a string like {@code "{\"key\":\"value\"}"} in the database
 * would be incorrectly returned as an escaped string in the final JSON payload. This utility
 * ensures the field is correctly represented as a nested object, matching the original API's contract.
 */
public final class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private JsonUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static Object parseJsonString(String json) {
        if (json == null || json.isBlank()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, Object.class);
        } catch (JsonProcessingException e) {
            // This mirrors the behavior where invalid JSON would cause an error.
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to parse JSON field", e);
        }
    }
}