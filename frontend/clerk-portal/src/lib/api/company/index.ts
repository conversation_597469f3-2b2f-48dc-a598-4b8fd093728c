import { AxiosError } from "axios";
import { api } from "..";
import { ApiEndpoints } from "../endpoints";

export const validateVatId = async (data: any) => {
  try {
    const res = await api.post(ApiEndpoints.company.postVatId, data);
    return res.data;
  } catch (error: unknown) {
    if ((error as AxiosError).response) {
      return (error as AxiosError).response?.data ?? null;
    }
    return null;
  }
};
