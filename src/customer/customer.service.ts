import { CompanyService } from "@/company/company.service";
import { CouponService } from "@/coupon/coupon.service";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { DatabaseService } from "@/database/database.service";
import { HttpModuleService } from "@/http/http.service";
import { MondayService } from "@/integration/monday.service";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { ContractType, Prisma } from "@prisma/client";
import axios from "axios";
import { lastValueFrom } from "rxjs";
import { CreateCustomerCountriesDto } from "./dto/create-customer-countries.dto";
import { CreateCustomerDto } from "./dto/create-customer.dto";
import { FindAllCustomersDto } from "./dto/find-all-customer.dto";
import { FindTutorialCustomersDto } from "./dto/find-tutorial.dto";
import { UpdateCustomerDto } from "./dto/update-customer.dto";
import { UpsertTutorialCustomersDto } from "./dto/upsert-tutorial.dto";
import { CustomerType } from "./enums/customer-type.enum";
import { GERMANY_FRACTIONS } from "@/shared/consts/germany-fractions";

@Injectable()
export class CustomerService {
  constructor(
    private databaseService: DatabaseService,
    private readonly customerIoService: CustomerIoService,
    private readonly mondayService: MondayService,
    private readonly httpModuleService: HttpModuleService,
    private readonly companyService: CompanyService,
    private readonly couponService: CouponService
  ) {}

  async findAll(params: FindAllCustomersDto) {
    const { search, service_type, status, page = 1, limit = 10, country_code } = params;

    if (Number.isNaN(page) || page < 1) throw new BadRequestException("Page param is invalid");
    if (Number.isNaN(limit) || limit < 1) throw new BadRequestException("Limit param is invalid");

    const skip = (Number(page) - 1) * Number(limit);

    const orderBy: Prisma.CustomerOrderByWithRelationInput = (() => {
      if (params?.order === "ASC") return { id: "asc" };
      if (params?.order === "DESC") return { id: "desc" };
      if (params?.order === "LAST_MODIFIED") return { updated_at: "desc" };
      if (params?.order === "FIRST_MODIFIED") return { updated_at: "asc" };

      return { id: "desc" };
    })();

    const countryFilter = (() => {
      if (!service_type && !!country_code) {
        return {
          OR: [
            { licenses: { some: { country_code: country_code } } },
            { action_guides: { some: { country_code: country_code } } },
          ],
        };
      }

      return {};
    })();

    const where: Prisma.CustomerWhereInput = {
      deleted_at: null,
      ...(search && {
        OR: [
          {
            email: { contains: search, mode: "insensitive" },
          },
          {
            companies: {
              some: {
                name: { contains: search, mode: "insensitive" },
              },
            },
          },
          {
            id: !isNaN(Number(search)) ? Number(search) : undefined,
          },
        ],
      }),
      contracts: {
        some: {
          deleted_at: null,
          ...(service_type && { type: service_type }),
          ...(status && !!["ACTIVE", "TERMINATED"].includes(status) && { status: status }),
          ...countryFilter,
        },
      },
    };

    const [customers, total] = await Promise.all([
      this.databaseService.customer.findMany({
        where,
        include: {
          companies: true,
          phones: true,
          contracts: {
            select: {
              type: true,
              status: true,
              termination: true,
              licenses: {
                select: {
                  country_id: true,
                  country_code: true,
                  country_name: true,
                  country_flag: true,
                  year: true,
                  registration_status: true,
                  contract_status: true,
                  clerk_control_status: true,
                  termination: true,
                },
              },
              action_guides: {
                select: {
                  country_id: true,
                  country_code: true,
                  country_name: true,
                  country_flag: true,
                  contract_status: true,
                  termination: true,
                },
              },
            },
          },
        },
        orderBy,
        skip,
        take: Number(limit),
      }),
      this.databaseService.customer.count({
        where,
      }),
    ]);

    return {
      customers,
      count: total,
      pages: Math.ceil(total / limit),
      current_page: Number(page),
    };
  }

  async findById(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomer(id, user);

    const customer = await this.databaseService.customer.findUnique({
      where: { id },
      include: {
        invite_token: true,
        companies: {
          include: {
            address: true,
          },
        },
        coupons: {
          include: {
            coupon: {
              include: {
                coupon_uses: {
                  include: {
                    shopping_cart: {
                      include: {
                        items: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    return customer;
  }

  async findOneByEmail(email: string, user: AuthenticatedUser) {
    if (!email) {
      throw new HttpException("Bad Request", HttpStatus.BAD_REQUEST);
    }

    const customer = await this.databaseService.customer.findFirst({
      where: { email: email, deleted_at: null },
      include: {
        companies: {
          include: {
            address: true,
          },
        },
      },
    });

    if (!customer) throw new NotFoundException("Customer not found");
    await this.validatingUserPermissionCustomer(customer.id, user);

    return customer;
  }

  async findByUserId(user_id: number, user: AuthenticatedUser) {
    const customer = await this.databaseService.customer.findUnique({
      where: { user_id, deleted_at: null },
      include: {
        companies: {
          include: {
            address: true,
            emails: true,
            billing: true,
          },
        },
        phones: true,
        contracts: {
          include: {
            action_guides: { where: { deleted_at: null } },
            licenses: {
              where: { deleted_at: null },
              include: {
                files: true,
                termination: true,
                price_list: true,
                packaging_services: true,
              },
            },
            general_informations: {
              where: {
                deleted_at: null,
              },
              include: {
                answer_files: true,
              },
            },
            files: {
              where: {
                deleted_at: null,
              },
            },
            termination: {
              include: {
                files: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!customer) throw new NotFoundException("Customer not found!");

    if (user.role === Role.CUSTOMER && user_id != +user.id) {
      throw new ForbiddenException("You do not have permission to access this customer");
    }

    return customer;
  }

  async findTutorialStatus(
    { customer_id = null, service_type = null }: FindTutorialCustomersDto,
    user: AuthenticatedUser
  ) {
    await this.validatingUserPermissionCustomer(+customer_id, user);

    return this.databaseService.customerTutorial.findMany({
      where: {
        customer_id: customer_id ? Number(customer_id) : null,
        service_type,
        deleted_at: null,
      },
    });
  }

  async upsertTutorialStatus({ customer_id, service_type, is_finished, tutorial_id }: UpsertTutorialCustomersDto) {
    const checkCustomer = await this.databaseService.customer.findFirst({
      where: {
        id: customer_id,
        deleted_at: null,
      },
    });

    if (!checkCustomer) throw new NotFoundException("Customer not found");

    const checkTutorial = await this.databaseService.customerTutorial.findFirst({
      where: {
        customer_id,
        service_type,
      },
    });

    return await this.databaseService.customerTutorial.upsert({
      where: {
        id: tutorial_id || checkTutorial?.id || 0,
      },
      update: {
        is_finished,
        service_type,
      },
      create: {
        is_finished,
        customer_id,
        service_type,
      },
    });
  }

  async groupByCountry() {
    const customers = await this.databaseService.customer.findMany({
      where: {
        deleted_at: null,
        contracts: {
          some: {
            deleted_at: null,
            status: "ACTIVE",
          },
        },
      },
      include: {
        contracts: {
          select: {
            type: true,
            status: true,
            licenses: {
              select: {
                country_id: true,
                country_code: true,
                country_name: true,
                country_flag: true,
                year: true,
                registration_status: true,
                contract_status: true,
                clerk_control_status: true,
                termination: true,
              },
            },
          },
        },
      },
    });

    return customers.reduce((acc, customer) => {
      customer.contracts.forEach((contract) => {
        contract.licenses.forEach((license) => {
          const countryKey = license.country_code;
          if (!acc[countryKey]) {
            acc[countryKey] = {
              country_id: license.country_id,
              country_code: license.country_code,
              country_name: license.country_name,
              country_flag: license.country_flag,
              licensed_customer_count: 1,
              unlicensed_customer_count: customers.length,
            };
          } else {
            acc[countryKey].licensed_customer_count++;
          }
        });
      });
      return acc;
    }, {});
  }

  async create(data: CreateCustomerDto) {
    return await this.databaseService.$transaction(async (tx) => {
      const emailAlreadyExists = await tx.customer.findFirst({
        where: {
          email: data.email,
        },
      });

      if (emailAlreadyExists) throw new ConflictException("This email is already in use");

      const user = await (() => {
        if (!data.user_id)
          return this.createUserCustomer({
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            password: data.password,
            token_magic_link: data.token_magic_link,
          });

        return lastValueFrom(
          this.httpModuleService.auth({
            url: `/user/${data.user_id}`,
            params: undefined,
            method: "GET",
          })
        )
          .then((res) => res?.data as { id: number })
          .catch((error) => {
            throw new HttpException(
              error?.response?.message || `Error finding user`,
              error?.response?.statusCode || 404
            );
          });
      })();

      const date = new Date();

      const customer = await tx.customer.create({
        data: {
          user_id: user.id,
          type: CustomerType.REGULAR,
          email: data.email,
          first_name: data.first_name,
          last_name: data.last_name,
          company_name: data.company_name || null,
          salutation: data.salutation,
          document_id: data.document_id,
          currency: data.currency,
          is_active: false,
          created_at: date,
          updated_at: date,
          deleted_at: null,
        },
      });

      const consents = await tx.consent.findMany({
        where: {
          type: "ACCOUNT",
          deleted_at: null,
        },
      });

      await tx.customerConsent.createMany({
        data: consents.map((consent) => {
          return {
            customer_id: customer.id,
            consent_id: consent.id,
            created_at: date,
            updated_at: date,
            given: true,
          };
        }),
      });

      (async () => {
        try {
          await this.customerIoService.updateAttributesByCustomerId(
            customer.id,
            {
              cio_subscription_preferences: {
                topics: {
                  topic_1: true,
                  topic_3: true,
                  topic_5: true,
                  topic_6: true,
                },
              },
            },
            tx
          );

          await this.customerIoService.updateAttributesByCustomerId(
            customer.id,
            {
              first_name: customer.first_name,
              last_name: customer.last_name,
            },
            tx
          );
        } catch (err) {
          console.info("CUSTOMER IO ERROR", err);
        }

        try {
          await this.mondayService.sendDataBoardCustomer(data, customer.id);
        } catch (err) {
          console.info("MONDAY ERROR", err);
        }
      })();

      const nextYear = new Date();
      nextYear.setFullYear(nextYear.getFullYear() + 1);

      await this.couponService.create(
        {
          code: `${customer.id}`,
          discount_type: `ABSOLUTE`,
          value: 1000,
          is_active: true,
          max_amount: 100,
          max_uses_per_customer: 1,
          redeemable_by_new_customers: true,
          start_date: new Date().toISOString(),
          type: `CUSTOMER`,
          end_date: nextYear.toISOString(),
          mode: `GENERAL`,
          customers: [customer.id],
        },
        tx
      );
      return customer;
    });
  }

  async createWithCountries({ customer, company }: CreateCustomerCountriesDto) {
    const user = await lastValueFrom(
      this.httpModuleService.auth({
        url: `/user?create_by_admin=true`,
        params: {
          name: customer.first_name + ` ` + customer.last_name,
          email: customer.email,
          is_active: true,
          role_id: 1,
        },
        method: "POST",
      })
    )
      .then((res) => res?.data)
      .catch((error) => {
        throw new HttpException(
          error?.response?.message || `Error when creating a user`,
          error?.response?.statusCode || 400
        );
      });

    if (!user?.id) throw new BadRequestException(`Error when creating a user`);

    const resCustomer = await this.create({ ...customer, user_id: user.id });
    const resCompany = await this.companyService.create({ ...company, customer_id: resCustomer.id });

    // TODO: add integration with purchase

    return {
      customer: resCustomer,
      company: resCompany,
    };
  }

  async update(id: number, data: UpdateCustomerDto, user: AuthenticatedUser) {
    Logger.log("CustomerService / update - ", data);

    await this.validatingUserPermissionCustomer(id, user);

    const customer = await this.databaseService.customer.findUnique({
      where: { id },
    });

    const oldEmail = customer.email;

    if (data.phones) {
      const customerPhone = data.phones;
      delete data.phones;

      await this.databaseService.customerPhone.deleteMany({
        where: {
          customer_id: customer.id,
        },
      });

      const customerPhones = customerPhone.map((phone) => {
        return {
          phone_number: phone,
          customer_id: customer.id,
          phone_type: "PHONE",
          created_at: new Date(),
          updated_at: new Date(),
        };
      });

      await this.databaseService.customerPhone.createMany({
        data: customerPhones,
        skipDuplicates: true,
      });
    }
    const url = `${process.env.AUTH_API_URL}/user/by-email/${customer.email}`;

    if (data.first_name && data.last_name) {
      await axios.patch(
        url,
        {
          name: data.first_name + " " + data.last_name,
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );
    }

    if (data.email && data.email !== customer.email) {
      const checkNewEmail = await this.databaseService.customer.findFirst({
        where: {
          email: data.email,
        },
      });

      if (checkNewEmail) throw new HttpException("This email is already in use", HttpStatus.CONFLICT);
    }

    const updatedCustomer = await this.databaseService.customer.update({
      where: { id },
      data: {
        email: data.email,
        first_name: data.first_name,
        last_name: data.last_name,
        company_name: data.company_name || customer.company_name || null,
        salutation: data.salutation,
        document_id: data.document_id || customer.document_id || null,
        updated_at: new Date(),
        language: data.language || customer.language,
        currency: data.currency || customer.currency,
      },
      include: {
        phones: true,
        companies: true,
      },
    });

    if (data.email) {
      await this.databaseService.shoppingCart.updateMany({
        where: {
          email: customer.email,
          deleted_at: null,
        },
        data: {
          email: data.email,
        },
      });
    }

    await this.customerIoService.updateAttributesByCustomerId(updatedCustomer.id, {
      first_name: updatedCustomer.first_name,
      last_name: updatedCustomer.last_name,
      language: data.language || customer.language,
    });

    if (data.email) {
      await this.customerIoService.updateEmailByCustomerId(updatedCustomer.id, oldEmail);
      await this.mondayService.updateCustomerEmailMonday({ email: data.email, customerId: customer.id });
    }

    try {
      await this.mondayService.updateBoardAccountsMonday(data, customer.id);
    } catch (err) {
      console.info("MONDAY ERROR", err);
    }

    if (customer.salutation && !data.email) {
      try {
        await lastValueFrom(
          this.httpModuleService.admin({
            url: "/emails/send-message",
            params: {
              transactional_message_id: "28",
              identifiers: {
                email: updatedCustomer.email,
              },
              to: updatedCustomer.email,
              from: "Lizenzero <<EMAIL>>",
              subject: "Personal information changed",
            },
            method: "post",
          })
        ).catch((error) => {
          console.error(error?.response?.data);
        });
      } catch (error) {
        console.error(error);
      }
    }

    return updatedCustomer;
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomer(id, user);

    const customer = await this.databaseService.customer.findUnique({
      where: { id },
    });

    if (customer.deleted_at !== null) {
      throw new HttpException("Customer already deleted", 400);
    }
    await this.databaseService.customer.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });

    return { statusCode: 200, message: "Customer deleted successfully" };
  }

  async details(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionCustomer(id, user);

    return await this.databaseService.customer.findUnique({
      where: { id, deleted_at: null },
      include: {
        companies: {
          include: {
            address: true,
            emails: true,
            billing: true,
          },
        },
        phones: true,
        contracts: {
          include: {
            licenses: {
              select: {
                id: true,
                country_id: true,
                country_code: true,
                country_name: true,
                country_flag: true,
                year: true,
                registration_status: true,
                contract_status: true,
                clerk_control_status: true,
                termination: {
                  include: {
                    files: {
                      where: {
                        deleted_at: null,
                      },
                    },
                  },
                },
                files: {
                  where: {
                    deleted_at: null,
                  },
                },
                certificates: {
                  where: {
                    deleted_at: null,
                  },
                  include: {
                    files: {
                      where: {
                        deleted_at: null,
                      },
                    },
                  },
                },
              },
              orderBy: {
                created_at: "asc",
              },
            },
            action_guides: {
              select: {
                country_id: true,
                country_code: true,
                country_name: true,
                country_flag: true,
                contract_status: true,
                termination: {
                  include: {
                    files: {
                      where: {
                        deleted_at: null,
                      },
                    },
                  },
                },
              },
            },
            termination: {
              include: {
                files: {
                  where: {
                    deleted_at: null,
                  },
                },
              },
              where: {
                deleted_at: null,
              },
            },
          },
        },
      },
    });
  }

  async getSummary() {
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const [
      currentMonthCreated,
      previousMonthCreated,
      currentMonthActive,
      previousMonthActive,
      currentMonthTerminated,
      previousMonthTerminated,
    ] = await Promise.all([
      // currentMonthCreated
      this.databaseService.customer.count({
        where: {
          created_at: {
            gte: currentMonthStart,
            lt: now,
          },
        },
      }),
      // previousMonthCreated
      this.databaseService.customer.count({
        where: {
          created_at: {
            gte: previousMonthStart,
            lt: currentMonthStart,
          },
        },
      }),
      // currentMonthActive
      this.databaseService.customer.count({
        where: {
          deleted_at: null,
          contracts: {
            some: {
              deleted_at: null,
              status: {
                in: ["ACTIVE", "TERMINATION_PROCESS"],
              },
            },
          },
          created_at: {
            lt: now,
          },
        },
      }),
      // previousMonthActive
      this.databaseService.customer.count({
        where: {
          deleted_at: null,
          contracts: {
            some: {
              deleted_at: null,
              status: {
                in: ["ACTIVE", "TERMINATION_PROCESS"],
              },
            },
          },
          created_at: {
            lt: currentMonthStart,
          },
        },
      }),
      // currentMonthTerminated
      this.databaseService.customer.count({
        where: {
          contracts: {
            some: {
              status: "TERMINATED",
              updated_at: {
                gte: currentMonthStart,
                lt: now,
              },
            },
          },
        },
      }),
      // previousMonthTerminated
      this.databaseService.customer.count({
        where: {
          contracts: {
            some: {
              status: "TERMINATED",
              updated_at: {
                gte: previousMonthStart,
                lt: currentMonthStart,
              },
            },
          },
        },
      }),
    ]);

    const calculatePercentage = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Number((((current - previous) / previous) * 100).toFixed(1));
    };

    return {
      created: {
        current: currentMonthCreated,
        growthRate: calculatePercentage(currentMonthCreated, previousMonthCreated),
      },
      active: {
        current: currentMonthActive,
        growthRate: calculatePercentage(currentMonthActive, previousMonthActive),
      },
      terminated: {
        current: currentMonthTerminated,
        growthRate: calculatePercentage(currentMonthTerminated, previousMonthTerminated),
      },
    };
  }

  async createUserCustomer(data: {
    first_name: string;
    last_name: string;
    email: string;
    password?: string;
    token_magic_link?: string;
    by_admin?: boolean;
  }) {
    try {
      const url = (() => {
        const baseUrl = "/user";
        const queryParams = new URLSearchParams();

        if (data.by_admin) queryParams.append("create_by_admin", "true");

        return `${baseUrl}?${queryParams.toString()}`;
      })();

      const response = await lastValueFrom(
        this.httpModuleService.auth({
          url,
          params: {
            name: `${data.first_name} ${data.last_name}`,
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            is_active: true,
            role_id: 1,
            password: data.password || null,
            token_magic_link: data.token_magic_link || null,
          },
          method: "POST",
        })
      );

      if (!response.data || ![200, 201].includes(response.status)) {
        throw new Error();
      }

      const user = response.data as {
        id: number;
        name: string;
        email: string;
        password: string | null;
        is_active: boolean;
        role_id: number | null;
      };

      return user;
    } catch (error) {
      console.error(error);
      throw new HttpException(
        error?.response?.message || "Error creating user. Pleasse try again",
        error?.response?.statusCode || 400
      );
    }
  }

  async validatingUserPermissionCustomer(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(id)) throw new BadRequestException("Id is required");

    const customer = await this.databaseService.customer.findUnique({
      where: {
        id,
      },
    });

    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id != +user.id) {
      throw new ForbiddenException("You do not have permission to access this customer");
    }
  }

  async getDirectLicenseResources(id: number) {
    const customer = await this.databaseService.customer.findUnique({
      where: { id },
    });

    if (!customer) throw new NotFoundException("Customer not found");

    const directLicenseContract = await this.databaseService.contract.findFirst({
      where: {
        customer_id: id,
        status: "ACTIVE",
        type: ContractType.DIRECT_LICENSE,
      },
      include: {
        licenses: {
          where: { deleted_at: null },
          orderBy: {
            year: "asc",
          },
          include: {
            packaging_services: {
              where: { deleted_at: null },
              include: {
                volume_reports: {
                  include: {
                    volume_report_items: {
                      where: { deleted_at: null },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!directLicenseContract) throw new NotFoundException("Direct license contract not found");

    const resources = {
      total: {
        years: [],
        reportedWeight: 0,
        rosourcesSaved: 0,
        greenhouseGases: 0,
        fractions: {},
      },
      years: {},
    };

    for (const license of directLicenseContract.licenses) {
      const licenseYear = license.year;
      const packagingService = license.packaging_services[0];

      if (!packagingService) continue;

      resources.total.years.push(licenseYear);
      resources.years[licenseYear] = {
        year: licenseYear,
        reportedWeight: 0,
        rosourcesSaved: 0,
        greenhouseGases: 0,
        fractions: {},
      };

      for (const volumeReport of packagingService.volume_reports) {
        for (const volumeReportItem of volumeReport.volume_report_items) {
          const reportedWeightInKilograms = volumeReportItem.value / 1000;

          const fraction = GERMANY_FRACTIONS.find((fraction) => fraction.code === volumeReportItem.setup_fraction_code);

          if (!fraction) return;

          const reportedWeightInKg = Number(reportedWeightInKilograms.toFixed(2));
          const rosourcesSavedInKg = Number((fraction.resource_saving_factor * reportedWeightInKilograms).toFixed(2));
          const greenhouseGasesInKg = Number((fraction.greenhouse_gases_factor * reportedWeightInKilograms).toFixed(3));

          resources.total.reportedWeight += reportedWeightInKg;
          resources.total.rosourcesSaved += rosourcesSavedInKg;
          resources.total.greenhouseGases += greenhouseGasesInKg;

          if (!resources.total.fractions[fraction.key]) {
            resources.total.fractions[fraction.key] = {
              key: fraction.key,
              code: fraction.code,
              name: fraction.name,
              reportedWeight: reportedWeightInKg,
              rosourcesSaved: rosourcesSavedInKg,
              greenhouseGases: greenhouseGasesInKg,
            };
          } else {
            resources.total.fractions[fraction.key].reportedWeight += reportedWeightInKg;
            resources.total.fractions[fraction.key].rosourcesSaved += rosourcesSavedInKg;
            resources.total.fractions[fraction.key].greenhouseGases += greenhouseGasesInKg;
          }

          resources.years[licenseYear].reportedWeight += reportedWeightInKg;
          resources.years[licenseYear].rosourcesSaved += rosourcesSavedInKg;
          resources.years[licenseYear].greenhouseGases += greenhouseGasesInKg;

          if (!resources.years[licenseYear].fractions[fraction.key]) {
            resources.years[licenseYear].fractions[fraction.key] = {
              key: fraction.key,
              code: fraction.code,
              name: fraction.name,
              reportedWeight: reportedWeightInKg,
              rosourcesSaved: rosourcesSavedInKg,
              greenhouseGases: greenhouseGasesInKg,
            };
          } else {
            resources.years[licenseYear].fractions[fraction.key].reportedWeight += reportedWeightInKg;
            resources.years[licenseYear].fractions[fraction.key].rosourcesSaved += rosourcesSavedInKg;
            resources.years[licenseYear].fractions[fraction.key].greenhouseGases += greenhouseGasesInKg;
          }
        }
      }
    }

    return resources;
  }
}
