import * as React from "react";

import { cn } from "@/utils/cn";

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  errorMessage?: string;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, label, errorMessage, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {!!label && (
          <label htmlFor="description" className="text-primary text-base font-centra mb-2">
            {label}
          </label>
        )}
        <textarea
          data-error={!!errorMessage}
          className={cn(
            "resize-none flex min-h-[80px] w-full rounded-2xl border border-tonal-dark-cream-80 text-tonal-dark-cream-10  bg-background p-4 ring-offset-background placeholder:placeholder-tonal-dark-cream-60 focus-visible:outline-none focus-visible:ring-2 focus-visible:outline-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[error=true]:border-tonal-red-40 data-[error=true]:bg-tonal-red-90",
            className
          )}
          ref={ref}
          {...props}
        />
        {!!errorMessage && (
          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
            <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
              {errorMessage}
            </span>
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";

export { Textarea };
