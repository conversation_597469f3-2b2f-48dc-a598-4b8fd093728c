"use client";

import { Divider } from "@/components/common/divider";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Elipse, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { CircularProgressChart } from "./pie-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function LeadsGeneratedPieChart() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="partner-hub" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col">
      <h3 className="text-primary text-2xl font-bold">Leads generated</h3>
      <div className="flex flex-row gap-4 items-center mt-2">
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base">
              <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
              <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
            </button>
          }
        >
          {LICENSE_YEAR_FILTERS.map((filter, idx) => (
            <DropdownItem
              key={idx}
              onClick={() => handleChangeLicenseYear(filter)}
              className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
            >
              {filter.label}
            </DropdownItem>
          ))}
        </Dropdown>
        <Divider vertical initialMarginDisabled className="h-[20px]" />
        <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
      </div>

      <div className="w-full h-[280px] mt-4">
        <CircularProgressChart />
      </div>

      <div className="flex flex-col items-start gap-3 mt-4">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-2 w-[150px]">
            <Elipse className="fill-support-blue size-4" />
            <p className="text-tonal-dark-cream-20 text-small-paragraph-regular font-bold">Customer invite customer</p>
          </div>
          <div>
            <p className="text-support-blue text-paragraph-regular font-bold">€ 150 241 659</p>
          </div>
        </div>
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-2">
            <Elipse className="fill-[#1B6C64] size-4" />
            <p className="text-tonal-dark-cream-20 text-small-paragraph-regular font-bold">Partner</p>
          </div>
          <div>
            <p className="text-[#1B6C64] text-paragraph-regular font-bold">€ 150 241 659</p>
          </div>
        </div>
      </div>
    </div>
  );
}
