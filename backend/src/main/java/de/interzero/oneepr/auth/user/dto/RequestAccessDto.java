package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for requesting access to a user account")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RequestAccessDto extends BaseDto {

    @JsonProperty("requesterId")
    @Schema(
            description = "ID of the user requesting access",
            example = "42"
    )
    private Integer requesterId;

    @JsonProperty("callbackUrl")
    @Schema(
            description = "URL to redirect after completing access request",
            example = "https://in-deutsch.de/callback"
    )
    private String callbackUrl;

    @JsonProperty("user_id")
    @Schema(
            description = "Optional user ID for whom access is requested",
            example = "123",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer userId;

    @JsonProperty("email")
    @Schema(
            description = "Optional email of the user",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String email;
}
