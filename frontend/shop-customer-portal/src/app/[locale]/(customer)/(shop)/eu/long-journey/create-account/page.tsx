"use client";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneyCreateAccount } from "@/components/modules/shop/journeys/components/journey-create-account";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { Icons } from "@/components/ui/icons";

import { useTranslations } from "next-intl";
import { ShopLongJourneyStepper } from "@/components/modules/shop/components/shop-stepper";

export default function CreateAnAccount() {
  const t = useTranslations("shop.longJourney.createAccount");
  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Icons.starIcon} />
      <ShopLongJourneyStepper stepById={"see-results"} />
      <ShopContent>
        <TitleAndSubTitle subText={t("subText")} title={t("title")} subTitle={t("subTitle")} />
        <JourneyCreateAccount />
      </ShopContent>
    </>
  );
}
