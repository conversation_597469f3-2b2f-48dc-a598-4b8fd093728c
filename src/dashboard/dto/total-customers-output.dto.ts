import { ApiProperty } from "@nestjs/swagger";

class NewCustomersSummaryDto {
  @ApiProperty()
  total: number;
  @ApiProperty()
  eu_licensed: number;
  @ApiProperty()
  direct_licensed: number;
  @ApiProperty()
  action_guide: number;
  @ApiProperty()
  other_services: number;
  @ApiProperty()
  brokers: number;
}

class NetRevenuesSummaryDto {
  @ApiProperty()
  overral_products: number;

  @ApiProperty()
  eu_licensing: number;

  @ApiProperty()
  direct_licensing: number;

  @ApiProperty()
  action_guide: number;

  @ApiProperty()
  other_services: number;

  @ApiProperty()
  bulk_registration: number;
}

export class TotalCustomersOutputDto {
  @ApiProperty()
  total_licensed: number;

  @ApiProperty({ type: NetRevenuesSummaryDto })
  net_revenues_summary: NetRevenuesSummaryDto;

  @ApiProperty()
  existing_clients_licensed: number;

  @ApiProperty({ type: NewCustomersSummaryDto })
  new_customers_summary: NewCustomersSummaryDto;
}
