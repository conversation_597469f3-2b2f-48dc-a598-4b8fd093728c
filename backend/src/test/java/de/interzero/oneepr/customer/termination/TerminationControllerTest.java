package de.interzero.oneepr.customer.termination;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.reason.TerminationReasonRepository;
import de.interzero.oneepr.customer.termination.dto.CreateTerminationDto;
import de.interzero.oneepr.customer.termination.dto.UpdateTerminationDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class TerminationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private TerminationRepository terminationRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private TerminationReasonRepository terminationReasonRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @MockBean
    private EmailOutboxGateway emailOutboxGateway;

    private final ArgumentCaptor<EmailMessage> emailMessageCaptor = ArgumentCaptor.forClass(EmailMessage.class);

    private Contract testContract;

    private Termination testTermination;

    private Reason testReason1;

    @BeforeEach
    void setUp() {
        Customer testCustomer = new Customer();
        testCustomer.setUserId(101);
        testCustomer.setFirstName("Test");
        testCustomer.setLastName("User");
        testCustomer.setEmail("testuser." + Instant.now().toEpochMilli() + "@example.com");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        testContract = new Contract();
        testContract.setCustomer(testCustomer);
        testContract.setTitle("Active Test Contract");
        testContract.setStartDate(Instant.now().minus(10, ChronoUnit.DAYS));
        testContract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.TERMINATION_PROCESS);
        testContract = contractRepository.save(testContract);

        License testLicense = new License();
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setCountryCode("DE");
        testLicense.setContractStatus(License.ContractStatus.TERMINATION_PROCESS);
        testLicense.setStartDate(Instant.now());
        testLicense.setYear(java.time.Year.now().getValue());
        testLicense.setRegistrationNumber("DE-TEST-12345");
        testLicense.setCountryId(1);
        testLicense.setCountryFlag("🇩🇪");
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        licenseRepository.save(testLicense);

        testReason1 = new Reason();
        testReason1.setTitle("Test Reason 1");
        testReason1.setType(Reason.Type.TERMINATION);
        testReason1.setValue("test_reason_1");
        testReason1.setCreatedAt(Instant.now());
        testReason1.setUpdatedAt(Instant.now());
        testReason1 = reasonRepository.save(testReason1);

        testTermination = new Termination();
        testTermination.setRequestedAt(Instant.now());
        testTermination.setStatus(Termination.Status.REQUESTED);
        testTermination.setCreatedAt(Instant.now());
        testTermination.setUpdatedAt(Instant.now());
        testTermination.getContracts().add(testContract);
        testTermination.getLicenses().add(testLicense);
        testTermination = terminationRepository.save(testTermination);
        testContract.setStatus(Contract.Status.ACTIVE);
        contractRepository.save(testContract);
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findById_shouldReturnTermination_whenExists() throws Exception {
        mockMvc.perform(get(Api.TERMINATION + "/{id}", testTermination.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testTermination.getId())))
                .andExpect(jsonPath("$.status", is("REQUESTED")));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void create_shouldCreateNewTerminationAndSendEmail() throws Exception {
        testContract.setStatus(Contract.Status.ACTIVE);
        contractRepository.save(testContract);

        CreateTerminationDto createDto = new CreateTerminationDto(
                testContract.getId(),
                                                                  Termination.Status.REQUESTED,
                                                                  null,
                                                                  List.of(testReason1.getId()),
                                                                  null,
                                                                  null);

        mockMvc.perform(post(Api.TERMINATION).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.message", is("Termination created successfully")));

        // Verify that the email service was called
        verify(emailOutboxGateway, times(1)).sendEmail(emailMessageCaptor.capture());
        assertEquals("23", emailMessageCaptor.getValue().getTransactionalMessageId());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingTerminationAndRelatedEntities() throws Exception {
        UpdateTerminationDto updateDto = new UpdateTerminationDto();
        updateDto.setStatus(Termination.Status.COMPLETED);

        mockMvc.perform(put(Api.TERMINATION + "/{id}", testTermination.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message", is("Termination updated successfully")));

        // Enhance assertions to check related entities
        Termination updatedTermination = terminationRepository.findById(testTermination.getId()).orElseThrow();
        assertEquals(Termination.Status.COMPLETED, updatedTermination.getStatus());

        Contract terminatedContract = contractRepository.findById(testContract.getId()).orElseThrow();
        assertEquals(Contract.Status.TERMINATED, terminatedContract.getStatus());

        License terminatedLicense = licenseRepository.findAllByContractIdAndDeletedAtIsNull(testContract.getId())
                .getFirst();
        assertEquals(License.ContractStatus.TERMINATED, terminatedLicense.getContractStatus());
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void revoke_shouldRevertTerminationAndSendEmail() throws Exception {
        mockMvc.perform(post(Api.TERMINATION + "/{id}/revoke", testTermination.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message", is("Termination revoked successfully")));

        // Verify state changes in the database
        assertTrue(terminationRepository.findById(testTermination.getId()).isEmpty());
        assertTrue(terminationReasonRepository.findAllByTermination_Id(testTermination.getId()).isEmpty());

        Contract revertedContract = contractRepository.findById(testContract.getId()).orElseThrow();
        assertEquals(Contract.Status.ACTIVE, revertedContract.getStatus());

        // Verify the email for revoking was sent
        verify(emailOutboxGateway, times(1)).sendEmail(emailMessageCaptor.capture());
        assertEquals("12", emailMessageCaptor.getValue().getTransactionalMessageId());
        assertEquals(testContract.getCustomer().getEmail(), emailMessageCaptor.getValue().getTo());
    }
}