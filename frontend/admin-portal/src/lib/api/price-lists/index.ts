import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { CreatePriceList, PriceList, UpdatePriceList } from "@/types/service-setup/price-list";

export async function getPriceList(priceListId: number) {
  const response = await adminApi.get<PriceList>(ApiEndpoints.priceLists.findById(priceListId));

  return response.data;
}

export interface GetPriceListsParams {
  search?: string;
  license_year?: string;
  service_type?: string;
}

export async function getPriceLists(params: GetPriceListsParams = {}) {
  const response = await adminApi.get<PriceList[]>(ApiEndpoints.priceLists.findAll, {
    params,
  });

  return response.data;
}

export async function createPriceList(priceList: CreatePriceList) {
  const response = await adminApi.post(ApiEndpoints.priceLists.create, priceList);

  return response.data;
}

export async function updatePriceList(priceListId: number, priceList: UpdatePriceList) {
  const response = await adminApi.put(ApiEndpoints.priceLists.update(priceListId), priceList);

  return response.data;
}

export async function deletePriceList(priceListId: number) {
  await adminApi.delete(ApiEndpoints.priceLists.delete(priceListId));
}
