package de.interzero.oneepr.customer.customer_activity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerActivityRepository extends JpaRepository<CustomerActivity, Integer> {

    Optional<CustomerActivity> findByIdAndDeletedAtIsNull(Integer id);

    @Query("SELECT ca FROM CustomerActivity ca LEFT JOIN FETCH ca.customer c WHERE ca.id = :id AND ca.deletedAt IS NULL")
    Optional<CustomerActivity> findByIdAndDeletedAtIsNullFetchingCustomer(@Param("id") Integer id);

    List<CustomerActivity> findByDeletedAtIsNullOrderByCreatedAtDesc();

    List<CustomerActivity> findByCustomer_IdAndDeletedAtIsNullOrderByCreatedAtDesc(Integer customerId);
}