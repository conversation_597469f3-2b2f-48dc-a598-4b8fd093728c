package de.interzero.oneepr.customer.coupon.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Schema(description = "Fields required to representing invite customer manager settings")
@Data
public class InviteCustomersManager {

    @JsonProperty("isLinkEnabled")
    @Schema(description = "Is link-based referral enabled")
    private Boolean isLinkEnabled;

    @JsonProperty("linkPattern")
    @Schema(description = "Pattern for referral link")
    private String linkPattern;

    @JsonProperty("isVoucherEnabled")
    @Schema(description = "Is voucher-based referral enabled")
    private Boolean isVoucherEnabled;

    @JsonProperty("couponPrefix")
    @Schema(description = "Prefix for voucher code")
    private String couponPrefix;

    @JsonProperty("minimumOrderValue")
    @Schema(description = "Minimum order value to be eligible")
    private Integer minimumOrderValue;

    @JsonProperty("validity")
    @Schema(description = "Validity duration configuration")
    private Validity validity;

    @JsonProperty("maximumReferrals")
    @Schema(description = "Maximum number of allowed referrals")
    private Integer maximumReferrals;

    @JsonProperty("minimumOrderPeriod")
    @Schema(description = "Minimum order period configuration")
    private Validity minimumOrderPeriod;

    @JsonProperty("euLicenseDiscountType")
    @Schema(description = "Discount type for EU license")
    private String euLicenseDiscountType;

    @JsonProperty("directLicenseDiscountType")
    @Schema(description = "Discount type for direct license")
    private String directLicenseDiscountType;

    @JsonProperty("actionGuideDiscountType")
    @Schema(description = "Discount type for action guide")
    private String actionGuideDiscountType;

    @JsonProperty("carbonOffset")
    @Schema(description = "Carbon offset configuration")
    private CarbonOffset carbonOffset;

    @Getter
    @Setter
    public static class Validity {

        @JsonProperty("value")
        @Schema(description = "Numerical value for duration")
        private Integer value;

        @JsonProperty("type")
        @Schema(
                description = "Duration type (month, year, weeks)",
                example = "month"
        )
        private String type;
    }

    @Getter
    @Setter
    public static class CarbonOffset {

        @JsonProperty("percentage")
        @Schema(description = "Percentage-based carbon offset")
        private OffsetValue percentage;

        @JsonProperty("absoluteValue")
        @Schema(description = "Absolute value carbon offset")
        private OffsetValue absoluteValue;
    }

    @Getter
    @Setter
    public static class OffsetValue {

        @JsonProperty("value")
        @Schema(description = "Numerical value of offset")
        private Integer value;

        @JsonProperty("serviceType")
        @Schema(description = "Type of service duration (month, year, weeks)")
        private String serviceType;

        @JsonProperty("amount")
        @Schema(description = "Amount for the offset")
        private Integer amount;
    }
}
