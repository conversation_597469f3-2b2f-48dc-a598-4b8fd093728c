"use client";

import { ChartData } from "@/components/common/charts/area-chart-linear";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";
import { Skeleton } from "@/components/ui/skeleton";
import { getTotalCustomers } from "@/lib/api/dashboard/total-customers";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { ChartItem } from "./chart-item";
import { ValueItem } from "./value-item";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

function formatToChartData(value: number): ChartData {
  return [
    { name: "1", value: value },
    { name: "2", value: value },
  ];
}

const TotalCustomersSkeletons = () => (
  <>
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
    <Skeleton className="h-16 w-full rounded-xl" />
  </>
);

export function TotalCustomers() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedDateInterval, setSelectedDateInterval] = useState<DateRange | undefined>();

  const { data, isSuccess, isLoading } = useQuery({
    queryKey: ["total-customers", selectedLicenseYear.value, selectedDateInterval?.from, selectedDateInterval?.to],
    queryFn: () =>
      getTotalCustomers({
        year: +selectedLicenseYear.value,
        start_date: selectedDateInterval?.from,
        end_date: selectedDateInterval?.to,
      }),
  });

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="total-customers" className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Total customers</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          <MonthDatePickerWithRange onDateChange={setSelectedDateInterval} />
        </div>
      </div>

      <p className="text-[#808FA9] -mt-4">Values in thousands</p>

      <div className="grid grid-cols-2 gap-6">
        <div className="flex flex-col gap-10 rounded-2xl bg-surface-01 px-10 py-6">
          {isLoading && <TotalCustomersSkeletons />}
          {isSuccess && (
            <>
              <ValueItem value={data.total_licensed} label="Customer licensed in total" isCurrency={false} />
              <ValueItem
                value={data.net_revenues_summary.overral_products / 100}
                label="Net Revenue overalll products"
              />
              <ValueItem value={data.net_revenues_summary.eu_licensing / 100} label="Net Revenue EU Licensing" />
              <ValueItem
                value={data.net_revenues_summary.direct_licensing / 100}
                label="Net Revenue Direct Licensing"
              />
              <ValueItem value={data.net_revenues_summary.action_guide / 100} label="Net Revenue Action Guide" />
              <ValueItem value={data.net_revenues_summary.other_services / 100} label="Net Revenue other Services" />
              <ValueItem
                value={data.net_revenues_summary.bulk_registration / 100}
                label="Net Renenue Bulk resgistration"
              />
            </>
          )}
        </div>

        <div className="flex flex-col gap-10 py-6">
          {isLoading && <TotalCustomersSkeletons />}
          {isSuccess && (
            <>
              <ChartItem
                chartData={formatToChartData(data.existing_clients_licensed)}
                value={data.existing_clients_licensed}
                label="Existing clients licensed"
                color="red"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.total)}
                value={data.new_customers_summary.total}
                label="New clients total"
                color="red"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.direct_licensed)}
                value={data.new_customers_summary.direct_licensed}
                label="New customers DE Licensing"
                color="red"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.eu_licensed)}
                value={data.new_customers_summary.eu_licensed}
                label="New customers EU Licensing"
                color="blue"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.action_guide)}
                value={data.new_customers_summary.action_guide}
                label="New customers Action Guide"
                color="yellow"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.other_services)}
                value={data.new_customers_summary.other_services}
                label="New customers Other Services"
                color="orange"
              />
              <ChartItem
                chartData={formatToChartData(data.new_customers_summary.brokers)}
                value={data.new_customers_summary.brokers}
                label="New brokers"
                color="yellow"
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
