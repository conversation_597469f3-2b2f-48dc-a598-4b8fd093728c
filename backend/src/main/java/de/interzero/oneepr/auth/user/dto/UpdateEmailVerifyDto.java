package de.interzero.oneepr.auth.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for verifying email update using a token")
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateEmailVerifyDto extends BaseDto {

    @JsonProperty("token")
    @Schema(
            description = "Verification token for email change",
            example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
    )
    private String token;
}
