module.exports = {
  preset: "ts-jest",
  testEnvironment: "node",
  rootDir: "src",
  testRegex: ".*\\.spec\\.ts$",
  moduleFileExtensions: ["js", "json", "ts"],
  transform: {
    "^.+\\.js$": "babel-jest",
  },
  collectCoverageFrom: [
    "**/*.service.ts",
    "!app.service.ts",
    "!database/**",
    "!http/**",
    "!shared/**",
    "!integration/**",
    "!upload-files/**",
    "!customer-io/**",
    "!guards/**",
    "!file/**",
    "!certificate/**",
    "!dashboard/**",
  ],
  coverageDirectory: "../coverage",
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  transformIgnorePatterns: ["/node_modules/(?!axios)/"],
  reporters: ["default", "../custom-coverage-reporter.js"],
};
