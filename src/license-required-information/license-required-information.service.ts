import { DatabaseService } from "@/database/database.service";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import { CreateLicenseRequiredInformationDto } from "./dto/create-license-required-information.dto";
import { UpdateLicenseRequiredInformationDto } from "./dto/update-license-required-information.dto";
import { LicenseRequiredInformationKind, LicenseRequiredInformationStatus } from "@prisma/client";
import { FindLicenseRequiredInformationsDto } from "./dto/find-license-required-informations.dto";
import { DeclineLicenseRequiredInformationDto } from "./dto/decline-license-required-information.dto";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { HttpModuleService } from "@/http/http.service";
import { lastValueFrom } from "rxjs";

@Injectable()
export class LicenseRequiredInformationService {
  constructor(
    private databaseService: DatabaseService,
    private readonly customerIoService: CustomerIoService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  async findAll(data: FindLicenseRequiredInformationsDto) {
    if (!data.license_id && !data.contract_id) {
      throw new BadRequestException("License ID or Contract ID is required");
    }

    const licenseRequiredInformations = await this.databaseService.licenseRequiredInformation.findMany({
      where: {
        deleted_at: null,
        ...(data.license_id && { license_id: data.license_id }),
        ...(data.contract_id && { contract_id: data.contract_id }),
      },
      include: {
        license: true,
        answer_files: {
          where: {
            deleted_at: null,
          },
        },
        decline: {
          include: {
            decline_reasons: {
              include: {
                reason: true,
              },
            },
          },
        },
      },
      orderBy: {
        id: "asc",
      },
    });

    return licenseRequiredInformations.map((licenseRequiredInformation) => ({
      ...licenseRequiredInformation,
      decline: {
        ...licenseRequiredInformation.decline,
        decline_reasons: licenseRequiredInformation.decline?.decline_reasons.map(
          (declineReason) => declineReason.reason
        ),
      },
    }));
  }

  async findOne(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRequiredInformation(id, user);

    const licenseRequiredInformation = await this.databaseService.licenseRequiredInformation.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: true,
        answer_files: {
          where: {
            deleted_at: null,
          },
        },
        decline: {
          include: {
            decline_reasons: {
              include: {
                reason: true,
              },
            },
          },
        },
      },
    });

    return {
      ...licenseRequiredInformation,
      decline: {
        ...licenseRequiredInformation.decline,
        decline_reasons: licenseRequiredInformation.decline?.decline_reasons.map(
          (declineReason) => declineReason.reason
        ),
      },
    };
  }

  async create(data: CreateLicenseRequiredInformationDto) {
    if (data.kind === LicenseRequiredInformationKind.REQUIRED_INFORMATION) {
      if (!data.license_id || !data.setup_required_information_id) {
        throw new BadRequestException(
          "License ID and Setup Required Information ID are required for required information"
        );
      }
    }

    if (data.kind === LicenseRequiredInformationKind.GENERAL_INFORMATION) {
      if (!data.contract_id || !data.setup_general_information_id) {
        throw new BadRequestException(
          "Contract ID and Setup General Information ID are required for general information"
        );
      }
    }

    const licenseRequiredInformation = await this.databaseService.licenseRequiredInformation.create({
      data: {
        setup_required_information_id: data.setup_required_information_id,
        license_id: data.license_id,
        setup_general_information_id: data.setup_general_information_id,
        contract_id: data.contract_id,
        type: data.type,
        kind: data.kind,
        status: data.status,
        name: data.name,
        description: data.description,
        question: data.question,
        file_id: data.file_id,
        answer: data.answer,
        created_at: new Date(),
        updated_at: new Date(),
      },
      include: {
        contract: true,
      },
    });

    await this.customerIoService.processRequiredInformation(licenseRequiredInformation?.contract?.customer_id);
    return licenseRequiredInformation;
  }

  async update(id: number, data: UpdateLicenseRequiredInformationDto, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRequiredInformation(id, user);

    const licenseRequiredInformation = await this.databaseService.licenseRequiredInformation.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        decline: true,
        contract: true,
      },
    });

    return await this.databaseService.$transaction(async (tx) => {
      const updatedLicenseRequiredInformation = await tx.licenseRequiredInformation.update({
        where: { id: Number(id) },
        data: {
          ...data,
          updated_at: new Date(),
        },
      });

      if (
        (LicenseRequiredInformationStatus.OPEN === data.status ||
          LicenseRequiredInformationStatus.DONE === data.status) &&
        !!licenseRequiredInformation.decline
      ) {
        const decline = await tx.decline.findUnique({
          where: { license_required_information_id: licenseRequiredInformation.id },
        });

        if (decline) {
          await tx.declineReason.deleteMany({
            where: { decline_id: decline.id },
          });

          await tx.decline.delete({
            where: { license_required_information_id: licenseRequiredInformation.id },
          });
        }
      }

      await this.customerIoService.processRequiredInformation(licenseRequiredInformation?.contract?.customer_id, tx);

      return updatedLicenseRequiredInformation;
    });
  }

  async remove(id: number, user: AuthenticatedUser) {
    await this.validatingUserPermissionLicenseRequiredInformation(id, user);

    return await this.databaseService.licenseRequiredInformation.update({
      where: { id },
      data: {
        deleted_at: new Date(),
      },
    });
  }

  async decline(id: number, data: DeclineLicenseRequiredInformationDto) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Required Information ID is invalid");
    }

    if (!Array.isArray(data.reason_ids) || !data.reason_ids.length) {
      throw new BadRequestException("At least one decline reason ID is required");
    }

    const licenseRequiredInformation = await this.databaseService.licenseRequiredInformation.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        answer_files: true,
      },
    });

    if (!licenseRequiredInformation) {
      throw new NotFoundException("License Required Information not found");
    }

    const resUpdate = await this.databaseService.licenseRequiredInformation.update({
      where: { id: Number(id) },
      data: {
        status: LicenseRequiredInformationStatus.DECLINED,
        updated_at: new Date(),
        decline: {
          create: {
            title: data.title || "",
            decline_reasons: {
              create: data.reason_ids.map((reason_id) => ({
                reason_id,
              })),
            },
          },
        },
      },
      include: {
        contract: {
          include: {
            customer: true,
          },
        },
      },
    });

    const emailToSend = resUpdate?.contract?.customer?.email;

    if (emailToSend) {
      try {
        await lastValueFrom(
          this.httpModuleService.admin({
            url: "/emails/send-message",
            params: {
              transactional_message_id: "10",
              identifiers: {
                email: emailToSend,
              },
              to: emailToSend,
              from: "Lizenzero <<EMAIL>>",
              subject: "Document declined",
              message_data: {
                required_information_title: licenseRequiredInformation.name,
                required_information_file_name: licenseRequiredInformation?.answer_files?.[0]?.original_name,
                required_information_answer: licenseRequiredInformation?.answer,
              },
            },
            method: "post",
          })
        ).catch((error) => {
          console.error(error?.response?.data);
        });
      } catch (error) {
        console.error(error);
      }
    }

    return resUpdate;
  }

  async validatingUserPermissionLicenseRequiredInformation(id: number, user: AuthenticatedUser) {
    if (!id || Number.isNaN(Number(id))) {
      throw new BadRequestException("License Required Information ID is invalid");
    }

    const licenseRequiredInformation = await this.databaseService.licenseRequiredInformation.findUnique({
      where: { id: Number(id), deleted_at: null },
      include: {
        license: true,
        contract: {
          include: {
            customer: true,
          },
        },
        answer_files: {
          where: {
            deleted_at: null,
          },
        },
        decline: {
          include: {
            decline_reasons: {
              include: {
                reason: true,
              },
            },
          },
        },
      },
    });

    if (!licenseRequiredInformation) {
      throw new NotFoundException("License Required Information not found");
    }

    const { contract } = licenseRequiredInformation;

    if (!contract) {
      throw new NotFoundException("Contract not found");
    }
    const { customer } = contract;
    if (!customer) {
      throw new NotFoundException("Customer not found");
    }

    if (user.role === Role.CUSTOMER && customer.user_id !== +user.id) {
      throw new ForbiddenException("You do not have permission to access this license required information");
    }
  }
}
