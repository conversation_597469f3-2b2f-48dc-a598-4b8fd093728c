package de.interzero.oneepr.admin.country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.entity.CountryFollower;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * DTO for the country overview, combining database fields with aggregated customer data.
 * This is a plain data object and is decoupled from the JPA entity.
 */
@Getter
@Setter
public class CountryOverviewDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("code")
    private String code;

    @JsonProperty("flag_url")
    private String flagUrl;

    @JsonProperty("followers")
    private List<CountryFollower> followers;

    @JsonProperty("licensed_customer_count")
    private int licensedCustomerCount = 0;

    @JsonProperty("unlicensed_customer_count")
    private int unlicensedCustomerCount = 0;

    @JsonProperty("tasks")
    private int tasks = 0;
}