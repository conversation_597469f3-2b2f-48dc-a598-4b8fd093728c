package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object for creating a new company.
 * Converted from TypeScript CreateCompanyDto with exact same structure and field names.
 */
@Schema(description = "Fields required to create a new company")
@Data
public class CreateCompanyDto {

    @JsonProperty("customer_id")
    @Schema(description = "The ID of the customer")
    private Integer customerId;

    @JsonProperty("partner_id")
    @Schema(description = "The ID of the partner")
    private Integer partnerId;

    @JsonProperty("name")
    @Schema(
            description = "The name of the company",
            example = "Acme Corporation"
    )
    private String name;

    @JsonProperty("description")
    @Schema(
            description = "The description of the company",
            example = "Leading technology company"
    )
    private String description;

    @JsonProperty("address")
    @Schema(description = "The address of the company")
    private CreateCompanyAddressDto address;

    @JsonProperty("vat")
    @Schema(
            description = "The VAT ID of the company",
            example = "DE*********"
    )
    private String vat;

    @JsonProperty("tin")
    @Schema(
            description = "The TIN of the company",
            example = "*********"
    )
    private String tin;

    @JsonProperty("lucid")
    @Schema(
            description = "The Lucid ID of the company",
            example = "LUCID123"
    )
    private String lucid;

    @JsonProperty("contact")
    @Schema(description = "The contact of the company")
    private CreateCompanyContactDto contact;

    @JsonProperty("emails")
    @Schema(description = "The emails of the company")
    private List<String> emails;

    @JsonProperty("managing_director")
    @Schema(
            description = "The managing director of the company",
            example = "John Doe"
    )
    private String managingDirector;

    @JsonProperty("starting")
    @Schema(description = "The starting date of the company")
    private Instant starting;

    @JsonProperty("website")
    @Schema(
            description = "The website of the company",
            example = "https://www.acme.com"
    )
    private String website;

    @JsonProperty("billing")
    @Schema(description = "The billing information of the company")
    private CreateCompanyBillingDto billing;
}
