import { Body, Controller, Delete, Get, Param, Post } from "@nestjs/common";
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { RecommendCountryService } from "./recommend-country.service";
import { CreateRecommendCountryDto } from "./dto/create-recommend-country.dto";
import { PublicRoute } from "@/shared/auth/public-route.decorator";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("RecommendCountry")
@Controller("RecommendCountry")
export class RecommendCountryController {
  constructor(private readonly recommendCountryService: RecommendCountryService) {}

  @PublicRoute()
  @Post()
  @ApiOperation({ summary: "Create Recommended Country" })
  @ApiResponse({
    status: 201,
    description: "The recommended country has been successfully created.",
  })
  @ApiBody({ type: CreateRecommendCountryDto })
  create(@Body() createRecommendCountryDto: CreateRecommendCountryDto) {
    return this.recommendCountryService.create(createRecommendCountryDto);
  }

  @Get("count")
  @ApiOperation({ summary: "Count Recommendations by Country" })
  @ApiResponse({
    status: 200,
    description: "Count of recommendations by country.",
  })
  countRecommendations() {
    return this.recommendCountryService.countRecommendations();
  }

  @Delete(":id")
  @ApiOperation({ summary: "Remove Recommended Country" })
  @ApiResponse({
    status: 200,
    description: "The recommended country has been successfully removed.",
  })
  remove(@Param("id") id: number) {
    return this.recommendCountryService.remove(id);
  }

  @Get("customer/:customerId")
  @ApiOperation({ summary: "Get Recommendations by Customer" })
  @ApiResponse({
    status: 200,
    description: "List of recommendations by the specified customer.",
  })
  getByCustomer(@Param("customerId") customerId: number) {
    return this.recommendCountryService.getByCustomer(customerId);
  }

  @Get()
  @ApiOperation({ summary: "Get All Recommended Countries" })
  @ApiResponse({
    status: 200,
    description: "List of all recommended countries.",
  })
  getAll() {
    return this.recommendCountryService.getAll();
  }
}
