"use client";

import { DashboardPlaceHolder } from "@/components/modules/dashboard/placeholder/placeholder";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getServicesRevenueOvertime } from "@/lib/api/dashboard/services-revenues-overtime";
import { GraphColors } from "@/utils/graph-colors";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { ListItem } from "./list-item";
import { ServicesRevenueChart } from "./services-revenue-chart";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

export function ServicesRevenueOvertime() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const { data, isSuccess, isLoading, isError } = useQuery({
    queryKey: ["services-revenue-overtime", selectedLicenseYear.value],
    queryFn: () => getServicesRevenueOvertime({ year: +selectedLicenseYear.value }),
  });
  const showContent = isSuccess && data.data.length > 0;
  const showPlaceholder = (isSuccess && data.data.length === 0) || isError;
  const graphColors = showContent
    ? new GraphColors(data.data.map((service) => service.setup_packaging_service_id))
    : null;

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  return (
    <div id="services-revenue-overtime" className="w-full h-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Services revenue over time</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      <p className="text-[#808FA9] -mt-4">Values in thousands of euros</p>

      <div className="flex-col gap-10">
        {showPlaceholder && <DashboardPlaceHolder itemName="Services Revenue" />}
        <div className="flex flex-col gap-6 mb-10">
          {isLoading && (
            <>
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
              <Skeleton className="h-5" />
            </>
          )}
          {showContent &&
            graphColors &&
            data?.data.map((service) => (
              <ListItem
                key={service.setup_packaging_service_id}
                label={service.label}
                value={service.total / 100}
                color={graphColors.getColor(service.setup_packaging_service_id)}
              />
            ))}
        </div>
        <div className="h-[400px] w-full">
          {showContent && graphColors && <ServicesRevenueChart data={data} graphColors={graphColors} />}
          {isLoading && <Skeleton className="h-full" />}
        </div>
      </div>
    </div>
  );
}
