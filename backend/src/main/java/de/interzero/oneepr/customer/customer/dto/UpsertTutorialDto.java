package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating or updating a customer's tutorial status.
 * <p>
 * This DTO is a direct translation of the {@code UpsertTutorialCustomersDto}
 * from {@code src/customer/dto/upsert-tutorial.dto.ts}. It is used in the
 * {@code upsertTutorialStatus} service method.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UpsertTutorialDto extends BaseDto {

    @Schema(description = "Tutorial id number")
    @JsonProperty("tutorial_id")
    private Integer tutorialId;

    @Schema(
            description = "Customer id number",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("customer_id")
    private Integer customerId;

    @Schema(
            description = "Is finished boolean",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("is_finished")
    private Boolean isFinished;

    @Schema(
            description = "Customer service type",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("service_type")
    private Contract.Type serviceType;
}