import { AccountsAndObligationsOutputDto } from "@/dashboard/dto/accounts-and-obligations-output.dto";
import { AverageRevenueOutputDto } from "@/dashboard/dto/average-revenue-output.dto";
import { AverageRevenueDto } from "@/dashboard/dto/average-revenue.dto";
import { OpenBalanceOutputDto } from "@/dashboard/dto/open-balance-output.dto";
import { OpenBalanceDto } from "@/dashboard/dto/open-balance.dto";
import { PaymentMethodsSummaryOutputDto } from "@/dashboard/dto/payment-methods-output.dto";
import {
  RevenueAndContractsCountryOutputDto,
  RevenueAndContractsOutputDto,
} from "@/dashboard/dto/revenue-and-contracts-output.dto";
import { RevenueAndContractsDto } from "@/dashboard/dto/revenue-and-contracts.dto";
import { ServiceCustomerOutputDto, ServicesCustomersOutputDto } from "@/dashboard/dto/services-customers-output.dto";
import { ServicesCustomersDto } from "@/dashboard/dto/services-customers.dto";
import {
  ServiceRevenueOvertimeDto,
  ServicesRevenueOvertimeOutputDto,
} from "@/dashboard/dto/services-revenue-overtime-output.dto";
import { TerminationsOutputDto } from "@/dashboard/dto/terminations-output.dto";
import { TerminationsDto } from "@/dashboard/dto/terminations.dto";
import { TopServicesOutputDto } from "@/dashboard/dto/top-services-output.dto";
import { TotalCustomersOutputDto } from "@/dashboard/dto/total-customers-output.dto";
import { TotalCustomersDto } from "@/dashboard/dto/total-customers.dto";
import { DatabaseService } from "@/database/database.service";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { Role } from "@/shared/auth/role.enum";
import { Injectable } from "@nestjs/common";
import { ContractType, LicenseContractStatus, Prisma } from "@prisma/client";
import axios from "axios";

@Injectable()
export class DashboardService {
  constructor(private readonly databaseService: DatabaseService) {}

  async totalCustomers(options: TotalCustomersDto): Promise<TotalCustomersOutputDto> {
    const { start_date, end_date, year } = options;

    const newCustomersSummary = await this.getNewCustomersSummary(start_date, end_date, year);
    const existingClientsLicensed = await this.getExistingCustomersLicensedCount(year);
    const netRevenuesSummary = await this.getNetRevenuesSummary(start_date, end_date, year);
    const totalLicensed = newCustomersSummary.total + existingClientsLicensed;

    return {
      total_licensed: totalLicensed,
      existing_clients_licensed: existingClientsLicensed,
      new_customers_summary: newCustomersSummary,
      net_revenues_summary: netRevenuesSummary,
    };
  }

  private async getNewCustomersSummary(startDate: Date, endDate: Date, licenseYear: number) {
    const customers = await this.databaseService.customer.findMany({
      where: {
        created_at: {
          gte: startDate,
          lte: endDate,
        },
        deleted_at: null,
        contracts: {
          some: {
            licenses: {
              some: {
                year: licenseYear,
                created_at: {
                  gte: startDate,
                  lte: endDate,
                },
                contract_status: LicenseContractStatus.ACTIVE,
              },
            },
            deleted_at: null,
          },
        },
      },
      include: {
        contracts: {
          select: {
            type: true,
          },
        },
      },
    });

    const total = customers.length;
    const euLicensed = customers.filter((c) =>
      c.contracts.some((contract) => contract.type === ContractType.EU_LICENSE)
    ).length;
    const directLicensed = customers.filter((c) =>
      c.contracts.some((contract) => contract.type === ContractType.DIRECT_LICENSE)
    ).length;
    const actionGuide = customers.filter((c) =>
      c.contracts.some((contract) => contract.type === ContractType.ACTION_GUIDE)
    ).length;

    return {
      total,
      eu_licensed: euLicensed,
      direct_licensed: directLicensed,
      action_guide: actionGuide,
      other_services: 0, // TODO: Implement when other services are defined
      brokers: 0, // TODO: Implement when broker module is available
    };
  }

  private async getExistingCustomersLicensedCount(licenseYear: number): Promise<number> {
    const total = await this.databaseService.customer.findMany({
      where: {
        contracts: {
          some: {
            licenses: {
              some: {
                year: licenseYear,
              },
            },
            status: LicenseContractStatus.ACTIVE,
            deleted_at: null,
          },
        },
        deleted_at: null,
      },
    });
    return total.length;
  }

  private async getNetRevenuesSummary(startDate: Date, endDate: Date, licenseYear: number) {
    const contracts = await this.databaseService.contract.findMany({
      where: {
        licenses: {
          some: {
            year: licenseYear,
            created_at: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
        deleted_at: null,
      },
      include: {
        licenses: {
          include: {
            price_list: {
              select: {
                registration_fee: true,
              },
            },
          },
        },
        action_guides: {
          include: {
            price_list: {
              select: {
                price: true,
              },
            },
          },
        },
      },
    });

    let euLicensing = 0;
    let directLicensing = 0;
    let actionGuide = 0;

    for (const contract of contracts) {
      const totalRegistrationFees = contract.licenses.reduce((sum, license) => {
        const licenseFees = license.price_list.reduce((licenseSum, priceList) => {
          return licenseSum + (priceList.registration_fee || 0);
        }, 0);
        return sum + licenseFees;
      }, 0);
      const totalActionGuides = contract.action_guides.reduce((sum, actionGuide) => {
        return (
          sum +
          actionGuide.price_list.reduce((guideSum, priceList) => {
            return guideSum + (priceList.price || 0);
          }, 0)
        );
      }, 0);

      switch (contract.type) {
        case ContractType.EU_LICENSE:
          euLicensing += totalRegistrationFees;
          break;
        case ContractType.DIRECT_LICENSE:
          directLicensing += totalRegistrationFees;
          break;
        case ContractType.ACTION_GUIDE:
          actionGuide += totalActionGuides;
          break;
      }
    }

    const overallProducts = euLicensing + directLicensing + actionGuide;

    return {
      overral_products: overallProducts,
      eu_licensing: euLicensing,
      direct_licensing: directLicensing,
      action_guide: actionGuide,
      other_services: 0, // TODO: Implement when other services are defined
      bulk_registration: 0, // TODO: Implement when broker module is available
    };
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async openBalance(query: OpenBalanceDto): Promise<OpenBalanceOutputDto> {
    return {} as OpenBalanceOutputDto; //TODO: Waiting Claim Management module implementation
  }

  async averageRevenue(options: AverageRevenueDto): Promise<AverageRevenueOutputDto> {
    const endDate = options.end_date || new Date();
    const startDate = options.start_date || new Date(endDate.getFullYear(), endDate.getMonth() - 1, 1);
    const previousEndDate = new Date(startDate);
    const previousStartDate = new Date(startDate.getFullYear(), startDate.getMonth() - 1, 1);

    const [currentMetrics, previousMetrics] = await Promise.all([
      this.getRevenues(startDate, endDate),
      this.getRevenues(previousStartDate, previousEndDate),
    ]);

    return this.buildOutput(currentMetrics, previousMetrics);
  }

  private async getRevenues(start_date: Date, end_date: Date) {
    const metrics = await this.databaseService.$queryRaw<any[]>`
      SELECT 
        c.type,
        COUNT(DISTINCT c.customer_id) as customer_count,
        SUM(
          COALESCE(lpl.registration_fee, 0) + 
          COALESCE(apl.price, 0)
        ) as revenue
      FROM contract c
      LEFT JOIN license l ON l.contract_id = c.id
      LEFT JOIN license_price_list lpl ON lpl.license_id = l.id
      LEFT JOIN action_guide ag ON ag.contract_id = c.id
      LEFT JOIN action_guide_price_list apl ON apl.action_guide_id = ag.id
      WHERE c.created_at BETWEEN ${start_date} AND ${end_date}
      AND c.deleted_at IS NULL
      GROUP BY c.type;
    `;

    const totalCustomers = await this.databaseService.$queryRaw<[{ count: bigint }]>`
      SELECT COUNT(DISTINCT customer_id) as count
      FROM contract
      WHERE created_at BETWEEN ${start_date} AND ${end_date}
      AND deleted_at IS NULL;
    `;

    const result = {
      eu_licensing: { revenue: 0, customers: 0 },
      direct_licensing: { revenue: 0, customers: 0 },
      action_guide: { revenue: 0, customers: 0 },
      other_services: { revenue: 0, customers: 0 },
      total_customers: Number(totalCustomers[0].count),
    };

    metrics.forEach((row) => {
      const type = row.type.toLowerCase();
      if (type in result) {
        result[type] = {
          revenue: Number(row.revenue),
          customers: Number(row.customer_count),
        };
      }
    });

    return result;
  }

  private buildOutput(current: any, previous: any): AverageRevenueOutputDto {
    const currentTotal = Number(
      Object.values(current).reduce((sum: number, item: any) => sum + (Number(item.revenue) || 0), 0)
    );
    const previousTotal = Number(
      Object.values(previous).reduce((sum: number, item: any) => sum + (item.revenue || 0), 0)
    );

    return {
      cart_overall: currentTotal,
      cart_per_customer: {
        overall: {
          total: currentTotal / current.total_customers,
          growth: this.calculateGrowth(
            previousTotal / (previous.total_customers || 1),
            currentTotal / (current.total_customers || 1)
          ),
        },
        direct_licensing: {
          total: current.direct_licensing.revenue / (current.direct_licensing.customers || 1),
          growth: this.calculateGrowth(
            previous.direct_licensing.revenue / (previous.direct_licensing.customers || 1),
            current.direct_licensing.revenue / (current.direct_licensing.customers || 1)
          ),
        },
        action_guide: {
          total: current.action_guide.revenue / (current.action_guide.customers || 1),
          growth: this.calculateGrowth(
            previous.action_guide.revenue / (previous.action_guide.customers || 1),
            current.action_guide.revenue / (current.action_guide.customers || 1)
          ),
        },
        other_services: {
          total: current.other_services.revenue / (current.other_services.customers || 1),
          growth: this.calculateGrowth(
            previous.other_services.revenue / (previous.other_services.customers || 1),
            current.other_services.revenue / (current.other_services.customers || 1)
          ),
        },
        eu_licensing: {
          total: current.eu_licensing.revenue / (current.eu_licensing.customers || 1),
          growth: this.calculateGrowth(
            previous.eu_licensing.revenue / (previous.eu_licensing.customers || 1),
            current.eu_licensing.revenue / (current.eu_licensing.customers || 1)
          ),
        },
      },
    };
  }

  async revenueAndContracts(options: RevenueAndContractsDto): Promise<RevenueAndContractsOutputDto> {
    const result: {
      country_code: string;
      contracts_number: number;
      total_revenue: number;
      country_flag: string;
      country_name: string;
    }[] = await this.databaseService.$queryRaw`
        select
          l.country_code,
          l.country_flag,
          l.country_name,
          count(distinct(l.id)) as contracts_number,
          coalesce(sum(lp.registration_fee),0) as total_revenue
        from
          license l
        inner join license_price_list lp on
          l.id = lp.license_id
        where
          l.year = ${options.year}
        group by
          l.country_code,
          l.country_flag,
          l.country_name
        order by
          ${Prisma.raw(options.order_by)} DESC
    `;

    const countries: RevenueAndContractsCountryOutputDto[] = result.map((item) => ({
      code: item.country_code,
      contracts_number: Number(item.contracts_number),
      total_revenue: Number(item.total_revenue),
      country_name: item.country_name,
      country_flag: item.country_flag,
    }));
    return {
      countries,
    };
  }

  async servicesCustomers(options: ServicesCustomersDto): Promise<ServicesCustomersOutputDto> {
    const { year, month } = options;

    const results: {
      setup_packaging_service_id: number;
      service_name: string;
      week: number;
      customer_count: number;
    }[] = await this.databaseService.$queryRaw`
      WITH service_customers AS (
        SELECT DISTINCT
          lps.setup_packaging_service_id,
          lps.name as service_name,
          c.customer_id,
          date_part('week', lvr.created_at) as week_number
        FROM license_packaging_service lps
        JOIN license l ON l.id = lps.license_id
        JOIN contract c ON l.contract_id = c.id
        JOIN license_volume_report lvr ON lvr.license_packaging_service_id = lps.id
        WHERE 
          EXTRACT(YEAR FROM lvr.created_at) = ${year}
          AND EXTRACT(MONTH FROM lvr.created_at) = ${month}
          AND lps.deleted_at IS NULL
          AND l.deleted_at IS NULL
          AND c.deleted_at IS NULL
          AND lvr.deleted_at IS NULL
      ),
      all_services AS (
        SELECT DISTINCT
          setup_packaging_service_id,
          service_name
        FROM service_customers
      ),
      service_weeks AS (
        SELECT 
          s.setup_packaging_service_id,
          s.service_name,
          w.week,
          COUNT(DISTINCT sc.customer_id) as customer_count
        FROM all_services s
        CROSS JOIN generate_series(1,5) as w(week)
        LEFT JOIN service_customers sc ON 
          s.setup_packaging_service_id = sc.setup_packaging_service_id 
          AND w.week = sc.week_number
        GROUP BY 
          s.setup_packaging_service_id,
          s.service_name,
          w.week
      )
      SELECT *
      FROM service_weeks
      ORDER BY setup_packaging_service_id, week;
    `;

    const serviceMap = new Map<number, ServiceCustomerOutputDto>();

    results.forEach((row) => {
      if (!serviceMap.has(row.setup_packaging_service_id)) {
        serviceMap.set(row.setup_packaging_service_id, {
          setup_packaging_service_id: row.setup_packaging_service_id,
          label: row.service_name,
          total: 0,
          weekly_data: [],
        });
      }

      const service = serviceMap.get(row.setup_packaging_service_id)!; // eslint-disable-line @typescript-eslint/no-non-null-assertion
      service.total += Number(row.customer_count);
      service.weekly_data.push({
        week: row.week,
        value: Number(row.customer_count),
      });
    });
    return {
      services: Array.from(serviceMap.values()).sort((a, b) => b.total - a.total),
    };
  }

  async topServices(year: number): Promise<TopServicesOutputDto> {
    const queryResult: {
      name: string;
      setup_packaging_service_id: number;
      total_licenses: number;
      total_countries: number;
    }[] = await this.databaseService.$queryRaw`
      SELECT 
        lps.name,
        COUNT(DISTINCT lic.id) as total_licenses,
        COUNT(DISTINCT(lic.country_id)) as total_countries
      FROM license_packaging_service lps
      INNER JOIN license lic ON lic.id = lps.license_id
      INNER JOIN license_price_list lpl ON lpl.license_id = lic.id
      WHERE lps.deleted_at IS NULL 
        AND lic.deleted_at IS NULL 
        AND lpl.deleted_at IS NULL
        AND lic.year = ${year}
      GROUP BY lps.name
      ORDER BY total_licenses DESC
    `;
    const response: TopServicesOutputDto = {
      data: queryResult.map((queryItem) => ({
        total_countries: Number(queryItem.total_countries),
        total_licenses: Number(queryItem.total_licenses),
        label: queryItem.name,
        setup_packaging_service_id: queryItem.setup_packaging_service_id,
      })),
    };
    return response;
  }

  async servicesRevenueOvertime(year: number): Promise<ServicesRevenueOvertimeOutputDto> {
    const results: {
      setup_packaging_service_id: number;
      service_name: string;
      month: number;
      revenue: number;
    }[] = await this.databaseService.$queryRaw`
      WITH RECURSIVE months AS (
        SELECT 1 as month
        UNION ALL
        SELECT month + 1 FROM months WHERE month < 12
      ),
      service_revenues AS (
        SELECT
          lps.setup_packaging_service_id,
          lps.name as service_name,
          EXTRACT(MONTH FROM lvr.created_at) as report_month,
          COALESCE(SUM(lvri.value), 0) as monthly_revenue
        FROM license_packaging_service lps
        LEFT JOIN license_volume_report lvr ON lvr.license_packaging_service_id = lps.id
        LEFT JOIN license_volume_report_item lvri ON lvri.license_volume_report_id = lvr.id
        WHERE 
          EXTRACT(YEAR FROM lvr.created_at) = ${year}
          AND lps.deleted_at IS NULL
          AND lvr.deleted_at IS NULL
          AND lvri.deleted_at IS NULL
        GROUP BY 
          lps.setup_packaging_service_id,
          lps.name,
          EXTRACT(MONTH FROM lvr.created_at)
      )
      SELECT 
        sr.setup_packaging_service_id,
        sr.service_name,
        m.month,
        COALESCE(sr2.monthly_revenue, 0) as revenue
      FROM months m
      CROSS JOIN (
        SELECT DISTINCT ON (setup_packaging_service_id) 
          setup_packaging_service_id, 
          FIRST_VALUE(service_name) OVER (
            PARTITION BY setup_packaging_service_id 
            ORDER BY service_name
          ) as service_name
        FROM service_revenues
      ) sr
      LEFT JOIN service_revenues sr2 ON 
        sr.setup_packaging_service_id = sr2.setup_packaging_service_id 
        AND m.month = sr2.report_month
      ORDER BY sr.setup_packaging_service_id, m.month
    `;

    const serviceMap = new Map<number, ServiceRevenueOvertimeDto>();

    results.forEach((row) => {
      if (!serviceMap.has(row.setup_packaging_service_id)) {
        serviceMap.set(row.setup_packaging_service_id, {
          total: 0,
          label: row.service_name,
          month_perfomances: [],
          setup_packaging_service_id: row.setup_packaging_service_id,
        });
      }

      const service = serviceMap.get(row.setup_packaging_service_id)!; // eslint-disable-line @typescript-eslint/no-non-null-assertion
      service.total += Number(row.revenue);
      service.month_perfomances.push({
        month: row.month,
        result: Number(row.revenue),
      });
    });

    return {
      data: Array.from(serviceMap.values()).sort((a, b) => b.total - a.total),
    };
  }

  async accountsAndObligations(): Promise<AccountsAndObligationsOutputDto> {
    const currentDate = new Date();
    const firstDayCurrentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const firstDayLastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const currentMonthAccounts = await this.databaseService.customer.count({
      where: {
        created_at: {
          gte: firstDayCurrentMonth,
          lt: currentDate,
        },
        deleted_at: null,
      },
    });
    const lastMonthAccounts = await this.databaseService.customer.count({
      where: {
        created_at: {
          gte: firstDayLastMonth,
          lt: firstDayCurrentMonth,
        },
        deleted_at: null,
      },
    });
    const accountGrowth = this.calculateGrowth(lastMonthAccounts, currentMonthAccounts);
    const currentMonthObligations = await this.databaseService.customerCommitment.count({
      where: {
        updated_at: {
          gte: firstDayCurrentMonth,
          lt: currentDate,
        },
        deleted_at: null,
        is_license_required: true,
        customer: {
          deleted_at: null,
        },
      },
    });
    const lastMonthObligations = await this.databaseService.customerCommitment.count({
      where: {
        updated_at: {
          gte: firstDayLastMonth,
          lt: firstDayCurrentMonth,
        },
        deleted_at: null,
        is_license_required: true,
        customer: {
          deleted_at: null,
        },
      },
    });
    const obligationGrowth = this.calculateGrowth(lastMonthObligations, currentMonthObligations);
    return {
      accounts: {
        total: currentMonthAccounts,
        growth: Number(accountGrowth.toFixed(2)),
      },
      obligations: {
        total: currentMonthObligations,
        growth: Number(obligationGrowth.toFixed(2)),
      },
    };
  }

  async paymentMethods(year: number): Promise<PaymentMethodsSummaryOutputDto> {
    const { data } = await axios.get<PaymentMethodsSummaryOutputDto>(
      `${process.env.PAYMENT_API_URL}/payment-method/summary?year=${year}`,
      {
        headers: {
          [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
          [HEADER_USER_ROLE]: Role.SYSTEM,
        },
      }
    );
    return data;
  }

  async terminationsData(options: TerminationsDto): Promise<TerminationsOutputDto> {
    const currentYear = options.year;
    const upcomingYear = currentYear + 1;
    const include: Prisma.TerminationInclude = {
      contracts: {
        include: {
          licenses: {
            select: {
              price_list: true,
              other_costs: true,
              representative_tiers: true,
            },
          },
          action_guides: {
            select: {
              price_list: true,
            },
          },
        },
      },
    };
    const commonWhere: Prisma.TerminationWhereInput = {
      contracts: {
        some: {
          type: options.contract_type,
          deleted_at: null,
        },
      },
    };
    const currentYearTerminations = await this.databaseService.termination.findMany({
      where: {
        ...commonWhere,
        licenses: {
          some: {
            year: currentYear,
          },
        },
      },
      include,
    });
    const upcomingYearTerminations = await this.databaseService.termination.findMany({
      where: {
        ...commonWhere,
        licenses: {
          some: {
            year: upcomingYear,
          },
        },
      },
      include,
    });
    const currentYearMetrics = this.calculateTerminationMetrics(currentYearTerminations);
    const upcomingYearMetrics = this.calculateTerminationMetrics(upcomingYearTerminations);
    return {
      current_year: {
        cancellations_revenue: currentYearMetrics.revenue,
        total_contracts_cancelled: currentYearMetrics.totalContracts,
      },
      upcoming_year: {
        cancellations_revenue: upcomingYearMetrics.revenue,
        total_contracts_cancelled: upcomingYearMetrics.totalContracts,
      },
    };
  }

  private calculateTerminationMetrics(terminations: any[]): { revenue: number; totalContracts: number } {
    let totalRevenue = 0;
    let totalContracts = 0;
    for (const termination of terminations) {
      totalContracts += termination.contracts.length;
      for (const contract of termination.contracts) {
        if (contract.licenses.length) {
          for (const license of contract.licenses) {
            totalRevenue += license.price_list.reduce(
              (sum, price) => sum + price.basic_price + price.registration_fee + price.handling_fee,
              0
            );
            totalRevenue += license.other_costs.reduce((sum: number, cost) => sum + cost.price, 0);
            totalRevenue += license.representative_tiers.reduce((sum: number, tier) => sum + tier.price, 0);
          }
        }
        if (contract.action_guides) {
          for (const guide of contract.action_guides) {
            totalRevenue += guide.price_list.reduce((sum, price) => sum + price.price, 0);
          }
        }
      }
    }
    return {
      revenue: totalRevenue,
      totalContracts: totalContracts,
    };
  }

  private calculateGrowth(previousValue: number, currentValue: number): number {
    const PERCENTAGE_VALUE = 100;
    const NO_GROWTH = 0;
    if (previousValue === 0 && currentValue === 0) {
      return NO_GROWTH;
    }
    const growth = ((currentValue - previousValue) / (previousValue || 1)) * PERCENTAGE_VALUE;
    return growth;
  }
}
