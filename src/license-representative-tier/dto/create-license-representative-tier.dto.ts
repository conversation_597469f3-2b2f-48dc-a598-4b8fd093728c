import { ApiProperty } from "@nestjs/swagger";

export class CreateLicenseRepresentativeTierDto {
  @ApiProperty({
    description: "The license ID",
  })
  license_id: number;

  @ApiProperty({
    description: "The representative tier ID",
  })
  setup_representative_tier_id: number;

  @ApiProperty({
    description: "The name of the representative tier",
  })
  name: string;

  @ApiProperty({
    description: "The price of the representative tier (in cents)",
  })
  price: number;
}
