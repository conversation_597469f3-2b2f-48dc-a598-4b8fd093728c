package de.interzero.oneepr.customer.market_material;

import de.interzero.oneepr.customer.entity.MarketingMaterialPartner;
import de.interzero.oneepr.customer.entity.MarketingMaterialPartnerRepository;
import de.interzero.oneepr.customer.file.FileService;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.market_material.dto.CreateMarketingMaterialParams;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Service for handling marketing material operations.
 */
@Service
@RequiredArgsConstructor
public class MarketingMaterialService {

    private final MarketMaterialRepository marketingMaterialRepository;

    private final MarketingMaterialPartnerRepository marketingMaterialPartnerRepository;

    private final FileService fileService;

    /**
     * Creates a new MarketingMaterial and optionally uploads associated files and links to partners.
     *
     * <p>This method saves the marketing material entity with metadata and associates it
     * with related partners and uploaded files, if provided.</p>
     *
     * @param dto the DTO containing all data for marketing material creation
     * @return the created {@link MarketingMaterial} entity
     * @throws IOException if file upload fails
     */
    @Transactional
    public MarketingMaterial create(CreateMarketingMaterialParams dto) throws IOException {
        List<Integer> partnerIds = dto.getPartners() != null ? Arrays.stream(dto.getPartners().split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Integer::parseInt)
                .filter(id -> id > 0)
                .toList() : List.of();

        MarketingMaterial material = new MarketingMaterial();
        material.setName(dto.getName());
        material.setStartDate(dto.getStartDate());
        material.setEndDate(dto.getEndDate());
        material.setCategory(dto.getCategory());
        material.setPartnerRestriction(dto.getPartnerRestriction());

        marketingMaterialRepository.save(material);

        for (Integer partnerId : partnerIds) {
            Partner partner = new Partner();
            partner.setId(partnerId);

            MarketingMaterialPartner relation = new MarketingMaterialPartner();
            relation.setMarketingMaterial(material);
            relation.setPartner(partner);

            marketingMaterialPartnerRepository.save(relation);
            material.getPartners().add(relation);
        }

        if (dto.getFiles() != null && !dto.getFiles().isEmpty()) {
            for (MultipartFile file : dto.getFiles()) {
                CreateFileDto fileDto = new CreateFileDto();
                fileDto.setType(de.interzero.oneepr.customer.file.File.Type.MARKETING_MATERIAL);
                fileDto.setMarketingMaterialId(material.getId());

                fileService.uploadFile(fileDto, file, dto.getUser().getId(), dto.getUser().getRole());
            }
        }


        return material;
    }
}
