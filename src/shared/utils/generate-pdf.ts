import * as fs from "fs";
import * as path from "path";
import * as Handlebars from "handlebars";
import * as puppeteer from "puppeteer";
import { Readable } from "stream";

type generatePdfParams<T> = puppeteer.PDFOptions & {
  htmlPath?: string;
  templateFile?: string;
  fileName: string;
  data: T;
};

// Helpers customizados
Handlebars.registerHelper("breaklines", function (text) {
  if (!text) return "";
  text = Handlebars.Utils.escapeExpression(text);
  text = text.replace(/(\r\n|\n|\r)/gm, "<br>");
  text = text.replace(/<br>/g, "<br>");
  return new Handlebars.SafeString(text);
});
Handlebars.registerHelper("eq", function (a, b) {
  return a === b;
});
Handlebars.registerHelper("gt", function (a, b) {
  return a > b;
});
Handlebars.registerHelper("gte", function (a, b) {
  return a >= b;
});
Handlebars.registerHelper("lt", function (a, b) {
  return a < b;
});
Handlebars.registerHelper("lte", function (a, b) {
  return a <= b;
});
Handlebars.registerHelper("ne", function (a, b) {
  return a !== b;
});

Handlebars.registerHelper("gt", (a, b) => a > b);
Handlebars.registerHelper("lt", (a, b) => a < b);
Handlebars.registerHelper("eq", (a, b) => a === b);
Handlebars.registerHelper("and", (a, b) => a && b);
Handlebars.registerHelper("or", (a, b) => a || b);

export async function generatePdf<T>({
  htmlPath,
  templateFile,
  fileName,
  data,
  ...pdfOptions
}: generatePdfParams<T>): Promise<Express.Multer.File> {
  const timeId = `GENERATE_PDF_${fileName}_${new Date().toISOString()}`;
  console.time(timeId);

  if (!htmlPath && !templateFile) {
    throw new Error("htmlPath or templateFile is required");
  }

  const browser = await puppeteer.launch(getPuppeteerConfig());

  const html = (() => {
    if (htmlPath) {
      return fs.readFileSync(htmlPath, "utf-8");
    } else if (templateFile) {
      const templateRaw = fs.readFileSync(
        path.join(process.cwd(), "src", "shared", "templates", templateFile),
        "utf-8"
      );
      const template = Handlebars.compile(templateRaw);
      return template(data);
    } else {
      throw new Error("Invalid state: No HTML or Handlebars path provided.");
    }
  })();

  const page = await browser.newPage();

  await page.setContent(html, { waitUntil: "networkidle0", timeout: 300_000 });

  const buffer = await page.pdf({
    format: "A4",
    printBackground: true,
    ...pdfOptions,
  });

  await page.close();
  await browser.close();

  const pdfFile: Express.Multer.File = {
    fieldname: "file",
    originalname: fileName,
    encoding: "7bit",
    mimetype: "application/pdf",
    size: buffer.length,
    buffer: Buffer.from(buffer),
    stream: Readable.from(buffer),
    filename: fileName,
    destination: "",
    path: "",
  };

  console.timeEnd(timeId);

  return pdfFile;
}

export function getImageBase64(filePath: string): string {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    return `data:image/png;base64,${fileBuffer.toString("base64")}`;
  } catch (error) {
    console.error(`Error reading image file ${filePath}:`, error);
    return "";
  }
}

export function getPuppeteerConfig(): puppeteer.LaunchOptions {
  const baseConfig: puppeteer.LaunchOptions = {
    headless: "shell",
    timeout: 120_000,
    defaultViewport: { width: 1080, height: 1920 },
  };

  if (process.env.NODE_ENV !== "development") {
    return {
      ...baseConfig,
      executablePath: "/usr/bin/chromium-browser",
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--js-flags=--max-old-space-size=8192",
      ],
    };
  }

  return {
    ...baseConfig,
    dumpio: true,
  };
}
