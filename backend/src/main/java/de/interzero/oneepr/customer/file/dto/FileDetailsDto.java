package de.interzero.oneepr.customer.file.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Data Transfer Object representing the essential details of a file.
 * <p>
 * This DTO is a direct translation of the {@code File} model, included multiple
 * times with a {@code where: { deleted_at: null }} filter within the
 * {@code customer.service.ts#details} method's response structure.
 */
@Data
@NoArgsConstructor
public class FileDetailsDto {

    @Schema(description = "Unique identifier of the file (UUID).")
    @JsonProperty("id")
    private String id;

    @Schema(description = "The name of the file.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "The original name of the file upon upload.")
    @JsonProperty("original_name")
    private String originalName;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Timestamp of when the record was deleted, if applicable.")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

}