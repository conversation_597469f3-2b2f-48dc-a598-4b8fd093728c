"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { ChevronLeftIcon, Loader2Icon } from "lucide-react";
import { Controller } from "react-hook-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";

import { Button } from "@interzero/oneepr-react-ui/Button";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { queryClient } from "@/lib/react-query";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { CheckboxInput } from "@/components/ui/checkbox";
import { Divider } from "@/components/common/divider";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FractionInput } from "@/components/ui/fraction-input";
import { getCountries } from "@/lib/api/countries";
import { BuyXGetYDiscount, BuyXGetYProduct, Coupon, CouponDiscountType } from "@/lib/api/coupon/types";
import { CouponFormData, useCouponForm, validateCouponForm } from "@/hooks/use-coupon-form";
import { createCoupon } from "@/lib/api/coupon";
import Status from "@/components/common/license-status";
import MultipleSelector from "@/components/ui/multiple-selector";
import { useCallback, useEffect } from "react";
import { getCustomers } from "@/lib/api/customer";

const years = Array.from({ length: 9 }, (_, i) => new Date().getFullYear() - 4 + i);

const VOUCHER_TYPES_MAPPING: Record<CouponDiscountType, string> = {
  PERCENTAGE: "Percentage discount shopping cart",
  ABSOLUTE: "Absolute discount shopping cart",
  BUY_X_PRODUCTS_GET_Y_PRODUCTS: "Buy X products get Y products for free",
  BUY_X_PRODUCTS_GET_Y_DISCOUNT: "Buy X products get Y discount",
};

const PRODUCT_TYPES = {
  DIRECT_LICENSE: "Direct licensing Germany",
  EU_LICENSE: "Licensing service EU",
  ACTION_GUIDE: "Action guide",
} as const;

type ProductType = keyof typeof PRODUCT_TYPES;

export function CreateDiscountCodeForm() {
  const router = useRouter();

  const form = useCouponForm();
  const { formState } = form;

  const selectedVoucherMode = form.watch("voucherMode");
  const selectedVoucherType = form.watch("voucherType");
  const isApplyToAllProducts = form.watch("isApplyToAllProducts");
  const voucherIndividualModeType = form.watch("voucherIndividualModeType");
  const selectedProducts = form.watch("selectedProducts") || [];
  const directLicenseYears = form.watch("directLicenseYears") || [];
  const euLicenseCountries = form.watch("euLicenseCountries") || [];
  const actionGuideCountries = form.watch("actionGuideCountries") || [];

  const storedCoupon = typeof window !== "undefined" ? sessionStorage.getItem("duplicatedCoupon") : null;

  useEffect(() => {
    if (!storedCoupon) return;
    const coupon = JSON.parse(storedCoupon) as Coupon;
    sessionStorage.removeItem("duplicatedCoupon");

    form.setValue("status", coupon.is_active ? "active" : "inactive");
    form.setValue("linkOrCode", coupon.link ? "LINK" : "CODE");
    form.setValue("voucherName", coupon.code);
    form.setValue("description", coupon.description);
    form.setValue("note", coupon.note);
    form.setValue("redeemableVouchersPerCustomer", coupon.max_uses_per_customer);
    form.setValue("isRedeemableToNewCustomers", coupon.redeemable_by_new_customers);
    form.setValue("vouchersNumber", coupon.max_uses);
    form.setValue("start_date", coupon.start_date ? new Date(coupon.start_date) : new Date());
    form.setValue("end_date", coupon.end_date ? new Date(coupon.end_date) : new Date());
    form.setValue("voucherMode", coupon.mode);
    form.setValue("voucherType", coupon.discount_type);
    form.setValue("couponValue", coupon.value);
    form.setValue("minimumOrderValue", coupon.min_amount);
    form.setValue("maximumOrderValue", coupon.max_amount);
    form.setValue("minimumProductsToPromotion", coupon.min_products ?? 0);

    if (coupon.buy_x_get_y) {
      if (coupon.discount_type === "BUY_X_PRODUCTS_GET_Y_PRODUCTS") {
        const buyXGetYProduct = coupon.buy_x_get_y as BuyXGetYProduct;
        form.setValue("buyValue", buyXGetYProduct.buyAtLeast);
        form.setValue("getValue", buyXGetYProduct.receiveAtLeast);
        form.setValue("buyValueProduct", buyXGetYProduct.buyProduct);
        form.setValue("getValueProduct", buyXGetYProduct.receiveProduct);
      }

      if (coupon.discount_type === "BUY_X_PRODUCTS_GET_Y_DISCOUNT") {
        const buyXGetYDiscount = coupon.buy_x_get_y as BuyXGetYDiscount;
        form.setValue("buyValue", buyXGetYDiscount.buyAtLeast);
        form.setValue("getValue", buyXGetYDiscount.discountValue / 100);
        form.setValue("buyValueProduct", buyXGetYDiscount.buyProduct);
        form.setValue(
          "getValueProduct",
          buyXGetYDiscount.discountType === "PERCENTAGE" ? "Discount (%)" : "Discount (Absolute)"
        );
      }
    }

    if (coupon.elegible_products) {
      form.setValue(
        "selectedProducts",
        Object.keys(coupon.elegible_products).map((s) => s.toUpperCase()) as ProductType[]
      );
      form.setValue("directLicenseYears", coupon.elegible_products.direct_license.years);
      form.setValue("euLicenseCountries", coupon.elegible_products.eu_license.countries);
      form.setValue("actionGuideCountries", coupon.elegible_products.action_guide.countries);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storedCoupon]);

  const { data: countries, isLoading: isCountriesLoading } = useQuery({
    queryKey: ["countries"],
    queryFn: () => getCountries(),
  });

  const { mutate, isPending: isCreatingDiscountCode } = useMutation({
    mutationFn: async (data: CouponFormData) => {
      const params = validateCouponForm({ data, form });
      if (!params) return;
      const response = await createCoupon(params);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["coupons"] });
      enqueueSnackbar("Discount created successfully", { variant: "success" });
      router.push("/discount-codes");
    },
    onError: (error) => {
      // eslint-disable-next-line no-console
      console.error("error", error);

      if (error.message === "Mandatory") {
        return;
      }

      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  const fetchCustomers = useCallback(async (search: string) => {
    const response = await getCustomers({
      search,
    });
    const options = response.customers.map((customer) => ({
      label: `${customer.first_name} ${customer.last_name}`,
      value: customer.id.toString(),
    }));
    return options;
  }, []);

  const handleProductSelection = (productType: ProductType, isChecked: boolean) => {
    const currentProducts = form.getValues("selectedProducts") || [];

    if (isChecked) {
      form.setValue("selectedProducts", [...currentProducts, productType]);

      if (productType === "DIRECT_LICENSE") {
        form.setValue("directLicenseYears", years);
      } else if (productType === "EU_LICENSE" && countries) {
        form.setValue(
          "euLicenseCountries",
          countries.map((country) => country.id.toString())
        );
      } else if (productType === "ACTION_GUIDE" && countries) {
        form.setValue(
          "actionGuideCountries",
          countries.map((country) => country.id.toString())
        );
      }
    } else {
      form.setValue(
        "selectedProducts",
        currentProducts.filter((p) => p !== productType)
      );

      if (productType === "DIRECT_LICENSE") {
        form.setValue("directLicenseYears", []);
      } else if (productType === "EU_LICENSE") {
        form.setValue("euLicenseCountries", []);
      } else if (productType === "ACTION_GUIDE") {
        form.setValue("actionGuideCountries", []);
      }
    }
  };

  const handleYearSelection = (year: number, isChecked: boolean) => {
    const currentYears = form.getValues("directLicenseYears") || [];
    const updatedYears = isChecked ? [...currentYears, year] : currentYears.filter((y) => y !== year);

    form.setValue("directLicenseYears", updatedYears);

    if (updatedYears.length === 0) {
      const currentProducts = form.getValues("selectedProducts") || [];
      form.setValue(
        "selectedProducts",
        currentProducts.filter((p) => p !== "DIRECT_LICENSE")
      );
    }
  };

  const handleEuCountrySelection = (countryId: string, isChecked: boolean) => {
    const currentCountries = form.getValues("euLicenseCountries") || [];
    const updatedCountries = isChecked
      ? [...currentCountries, countryId]
      : currentCountries.filter((id) => id !== countryId);

    form.setValue("euLicenseCountries", updatedCountries);

    if (updatedCountries.length === 0) {
      const currentProducts = form.getValues("selectedProducts") || [];
      form.setValue(
        "selectedProducts",
        currentProducts.filter((p) => p !== "EU_LICENSE")
      );
    }
  };

  const handleActionGuideCountrySelection = (countryId: string, isChecked: boolean) => {
    const currentCountries = form.getValues("actionGuideCountries") || [];
    const updatedCountries = isChecked
      ? [...currentCountries, countryId]
      : currentCountries.filter((id) => id !== countryId);

    form.setValue("actionGuideCountries", updatedCountries);

    if (updatedCountries.length === 0) {
      const currentProducts = form.getValues("selectedProducts") || [];
      form.setValue(
        "selectedProducts",
        currentProducts.filter((p) => p !== "ACTION_GUIDE")
      );
    }
  };

  function handleFormSubmit(data: CouponFormData) {
    mutate(data);
  }

  const isLoading = formState.isSubmitting || isCreatingDiscountCode;

  return (
    <div className="space-y-6">
      <Button
        variant="text"
        color="light-blue"
        size="small"
        leadingIcon={<ChevronLeftIcon className="size-5" />}
        onClick={() => router.back()}
        className="hover:bg-[transparent] hover:opacity-75 px-0"
      >
        Back
      </Button>

      <div className="space-y-2">
        <h3 className="text-primary text-[2rem] font-bold">Create new discount</h3>
        <p className="text-[#808FA9] text-sm mb-9">*Mandatory Fields</p>
      </div>

      <form className="space-y-10" onSubmit={form.handleSubmit(handleFormSubmit)}>
        {/* Details */}
        <section className="space-y-8">
          <div className="bg-white rounded-[40px] p-10 space-y-10">
            <h4 className="text-2xl font-bold text-primary">Details</h4>
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <label htmlFor="status" className="text-primary">
                  Status*
                </label>
                <Controller
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger className="w-24 p-0 border-none bg-[transparent]">
                        <div className="ml-2">
                          <Status status={field.value === "active" ? "Active" : "Inactive"} />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <Controller
                control={form.control}
                name="linkOrCode"
                render={({ field }) => (
                  <RadioGroup value={field.value} onValueChange={field.onChange} defaultValue="CODE">
                    <div className="flex items-center gap-2">
                      <RadioGroupItem value="LINK" className="block" />
                      <p className="text-primary text-base font-normal">Link</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <RadioGroupItem value="CODE" className="block" />
                      <p className="text-primary text-base font-normal">Code</p>
                    </div>
                  </RadioGroup>
                )}
              />
              <Controller
                control={form.control}
                name="voucherName"
                render={({ field }) => (
                  <Input {...field} label="Voucher Name / Code *" placeholder="Enter voucher code" required />
                )}
              />
              <div className="space-y-2">
                <p className="text-on-surface-01 text-base font-normal mb-2">Voucher link</p>
                <input
                  disabled
                  value={"/" + form.watch("voucherName")}
                  className="bg-tonal-dark-cream-80 placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80  block w-full border rounded-2xl p-4 text-on-surface-01 focus:outline-primary"
                />
              </div>
              <Controller
                control={form.control}
                name="description"
                render={({ field }) => (
                  <Textarea {...field} label="Description" placeholder="Add a description" className="min-h-[160px]" />
                )}
              />
              <Controller
                control={form.control}
                name="note"
                render={({ field }) => (
                  <Input {...field} label="Note text for shopping cart" placeholder="Enter note" />
                )}
              />
              <Controller
                control={form.control}
                name="redeemableVouchersPerCustomer"
                render={({ field }) => (
                  <Input
                    {...field}
                    label="Redeemable vouchers / customer"
                    placeholder="Enter number"
                    type="number"
                    min={1}
                  />
                )}
              />
              <Controller
                control={form.control}
                name="isRedeemableToNewCustomers"
                render={({ field }) => (
                  <CheckboxInput label="Redeemable by new customers" checked={field.value} onChange={field.onChange} />
                )}
              />
            </div>
          </div>
        </section>

        {/* Basic configuration */}
        <section className="space-y-8">
          <div className="bg-white rounded-[40px] p-10 space-y-10">
            <h4 className="text-2xl font-bold text-primary">Basic configuration</h4>
            <Controller
              control={form.control}
              name="vouchersNumber"
              render={({ field }) => (
                <Input {...field} label="Number of vouchers" placeholder="Enter number" type="number" min={1} />
              )}
            />
            <Divider initialMarginDisabled className="my-4" />
            <div className="grid grid-cols-2 gap-6 max-w-sm">
              <Controller
                control={form.control}
                name="start_date"
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    value={field.value ? new Date(field.value).toISOString().split("T")[0] : ""}
                    label="Start date"
                    placeholder="Enter date"
                    type="date"
                    variant={fieldState.error ? "error" : "default"}
                    errorMessage={fieldState.error?.message}
                  />
                )}
              />
              <Controller
                control={form.control}
                name="end_date"
                render={({ field, fieldState }) => (
                  <Input
                    {...field}
                    value={field.value ? new Date(field.value).toISOString().split("T")[0] : ""}
                    label="End date"
                    placeholder="Enter date"
                    type="date"
                    variant={fieldState.error ? "error" : "default"}
                    errorMessage={fieldState.error?.message}
                  />
                )}
              />
            </div>
          </div>
        </section>

        {/* Voucher options */}
        <section className="space-y-8">
          <div className="bg-white rounded-[40px] p-10 space-y-10 sm:max-w-2xl">
            <h4 className="text-2xl font-bold text-primary">Voucher Options</h4>
            <div className="space-y-6">
              <div className="space-y-4">
                <label htmlFor="voucher-mode" className="text-primary">
                  Voucher mode *
                </label>
                <Controller
                  control={form.control}
                  name="voucherMode"
                  render={({ field: { onChange, value } }) => (
                    <RadioGroup id="voucher-mode" value={value} onValueChange={(newValue) => onChange(newValue)}>
                      <div className="flex items-center gap-6">
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="GENERAL" className="block" />
                          General
                        </label>
                        <label className="flex items-center gap-2 text-tonal-dark-cream-10 cursor-pointer">
                          <RadioGroupItem value="INDIVIDUAL" className="block" />
                          Individual
                        </label>
                      </div>
                    </RadioGroup>
                  )}
                />
                {selectedVoucherMode === "INDIVIDUAL" && (
                  <div className="my-6">
                    <Divider />
                    <Controller
                      control={form.control}
                      name="voucherIndividualModeType"
                      render={({ field }) => (
                        <RadioGroup
                          value={field.value}
                          onValueChange={(value) => {
                            field.onChange(value);
                            form.setValue("selectedCustomers", []);
                          }}
                        >
                          <div className="flex flex-col gap-6">
                            <label className="flex items-center gap-2 text-primary cursor-pointer">
                              <RadioGroupItem value="INDIVIDUAL" className="block" />
                              Select individual customers
                            </label>
                            {voucherIndividualModeType === "INDIVIDUAL" && (
                              <MultipleSelector
                                useValueAsLabel
                                onSearch={fetchCustomers}
                                placeholder="Select customers"
                                loadingIndicator={
                                  <div className="p-7">
                                    <Loader2Icon className="w-6 h-6 animate-spin" />
                                  </div>
                                }
                                emptyIndicator={
                                  <p className="text-center text-base leading-10 text-primary">No customers found</p>
                                }
                                onChange={(value) => {
                                  form.setValue("selectedCustomers", value);
                                }}
                              />
                            )}
                            <label className="flex items-center gap-2 text-primary cursor-pointer">
                              <RadioGroupItem value="GROUP_SEGMENT" className="block" />
                              Restriction to customer group / segment
                            </label>
                            {voucherIndividualModeType === "GROUP_SEGMENT" && (
                              <Select defaultValue="TBD">
                                <SelectTrigger>
                                  <SelectValue placeholder="Select an option" />
                                </SelectTrigger>
                                <SelectContent>
                                  {["TBD"].map((option) => {
                                    return (
                                      <SelectItem key={option} value={option}>
                                        {option}
                                      </SelectItem>
                                    );
                                  })}
                                </SelectContent>
                              </Select>
                            )}
                          </div>
                        </RadioGroup>
                      )}
                    />
                  </div>
                )}
              </div>
              <Divider className="my-6" />
              <Controller
                control={form.control}
                name="voucherType"
                render={({ field }) => (
                  <Select
                    onValueChange={(value) => {
                      field.onChange(value);
                      form.resetField("buyValue");
                      form.resetField("getValue");
                      form.resetField("buyValueProduct");
                      form.resetField("getValueProduct");
                      form.resetField("couponValue");
                    }}
                    defaultValue={field.value}
                  >
                    <SelectGroup>
                      <SelectLabel className="text-primary text-base font-normal pl-0">Voucher Type *</SelectLabel>
                      <SelectTrigger>
                        <SelectValue placeholder="Select voucher type" />
                      </SelectTrigger>
                    </SelectGroup>
                    <SelectContent>
                      {Object.entries(VOUCHER_TYPES_MAPPING).map(([key, label]) => {
                        return (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                )}
              />
              {selectedVoucherType === "PERCENTAGE" && (
                <div className="space-y-4">
                  <div className="w-full sm:max-w-[15rem]">
                    <Input
                      type="number"
                      min={1}
                      label="Coupon value *"
                      placeholder="Percentage (%)"
                      variant={formState.errors.couponValue ? "error" : "default"}
                      errorMessage={formState.errors.couponValue?.message}
                      {...form.register("couponValue")}
                    />
                  </div>
                  <p className="text-sm text-tonal-dark-cream-40">
                    By limiting the rating a 100% percentage will be determinate the product as free.{" "}
                  </p>
                </div>
              )}
              {selectedVoucherType === "ABSOLUTE" && (
                <div className="w-full sm:max-w-[15rem]">
                  <Controller
                    name="couponValue"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <FractionInput
                        label="Coupon value *"
                        type="currency"
                        error={error?.message}
                        className="text-left"
                        {...field}
                      />
                    )}
                  />
                </div>
              )}
              {selectedVoucherType === "BUY_X_PRODUCTS_GET_Y_PRODUCTS" && (
                <div className="grid md:grid-cols-2 items-center gap-6">
                  <div className="col-span-1 flex items-center gap-6">
                    <p className="text-primary">Buy</p>
                    <Controller
                      name="buyValue"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          placeholder="Number"
                          variant={formState.errors.buyValue ? "error" : "default"}
                          errorMessage={formState.errors.buyValue?.message}
                          style={{ marginTop: -6 }}
                        />
                      )}
                    />
                  </div>
                  <Controller
                    name="buyValueProduct"
                    control={form.control}
                    render={({ field, fieldState }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {["EU License", "Direct License", "Action Guide"].map((option) => {
                            return (
                              <SelectItem key={option} value={option} className="py-5 hover:bg-support-blue/10">
                                {option}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  <div className="col-span-1 flex items-center gap-6">
                    <p className="text-primary">Get</p>
                    <Controller
                      name="getValue"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          placeholder="Number"
                          variant={formState.errors.getValue ? "error" : "default"}
                          errorMessage={formState.errors.getValue?.message}
                          style={{ marginTop: -6 }}
                        />
                      )}
                    />
                  </div>
                  <Controller
                    name="getValueProduct"
                    control={form.control}
                    render={({ field, fieldState }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {["Workshop", "Action Guide"].map((option) => {
                            return (
                              <SelectItem key={option} value={option} className="py-5 hover:bg-support-blue/10">
                                {option}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}
              {selectedVoucherType === "BUY_X_PRODUCTS_GET_Y_DISCOUNT" && (
                <div className="grid md:grid-cols-2 items-center gap-6">
                  <div className="col-span-1 flex items-center gap-6">
                    <p className="text-primary">Buy</p>
                    <Controller
                      name="buyValue"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          placeholder="Number"
                          variant={formState.errors.buyValue ? "error" : "default"}
                          errorMessage={formState.errors.buyValue?.message}
                          style={{ marginTop: -6 }}
                        />
                      )}
                    />
                  </div>
                  <Controller
                    name="buyValueProduct"
                    control={form.control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select product" />
                        </SelectTrigger>
                        <SelectContent>
                          {["EU Lincense", "Direct License", "Action Guide"].map((option) => {
                            return (
                              <SelectItem key={option} value={option} className="py-5 hover:bg-support-blue/10">
                                {option}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  <div className="col-span-1 flex items-center gap-6">
                    <p className="text-primary">Get</p>
                    <Controller
                      name="getValue"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          min={1}
                          placeholder={form.watch("getValueProduct") === "Discount (%)" ? "Percentage (%)" : "Number"}
                          variant={formState.errors.getValue ? "error" : "default"}
                          errorMessage={formState.errors.getValue?.message}
                          style={{ marginTop: -6 }}
                        />
                      )}
                    />
                  </div>
                  <Controller
                    name="getValueProduct"
                    control={form.control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select discount type" />
                        </SelectTrigger>
                        <SelectContent>
                          {["Discount (%)", "Discount (Absolute)"].map((option) => {
                            return (
                              <SelectItem key={option} value={option} className="py-5 hover:bg-support-blue/10">
                                {option}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              )}
              <Divider className="my-8" />
              <h5 className="font-bold text-primary">Triggering</h5>
              <div className="mt-8 space-y-8 sm:max-w-md">
                <Controller
                  control={form.control}
                  name="minimumProductsToPromotion"
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                      <SelectGroup>
                        <SelectLabel className="text-primary text-base font-normal pl-0">
                          Minimum number of products to promotion *
                        </SelectLabel>
                        <SelectTrigger>
                          <SelectValue placeholder="Select number of products" />
                        </SelectTrigger>
                      </SelectGroup>
                      <SelectContent>
                        {["01", "02", "03", "04", "05", "06", "07", "08", "09", "10"].map((option) => (
                          <SelectItem key={option} value={option} className="py-2 hover:bg-support-blue/10">
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                <div className="grid md:grid-cols-2 gap-8">
                  <Controller
                    name="minimumOrderValue"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <FractionInput
                        {...field}
                        label="Minimum order value *"
                        type="currency"
                        error={error?.message}
                        className="text-left"
                      />
                    )}
                  />
                  <Controller
                    name="maximumOrderValue"
                    control={form.control}
                    render={({ field, fieldState: { error } }) => (
                      <FractionInput
                        {...field}
                        label="Maximum order value *"
                        type="currency"
                        error={error?.message}
                        className="text-left"
                      />
                    )}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Apply to product */}
        <section className="space-y-8">
          <div className="bg-white rounded-[40px] p-10 space-y-10">
            <h4 className="text-2xl font-bold text-primary">Apply to product*</h4>
            {selectedVoucherType === "PERCENTAGE" && (
              <p className="text-tonal-dark-cream-40 text-sm">Voucher type does not permit editing this field</p>
            )}
            <Controller
              control={form.control}
              name="isApplyToAllProducts"
              render={({ field: { onChange, value } }) => (
                <RadioGroup
                  value={selectedVoucherType === "PERCENTAGE" ? "" : value == true ? "YES" : "NO"}
                  onValueChange={(value) => onChange(value === "YES")}
                  disabled={selectedVoucherType === "PERCENTAGE"}
                  data-disabled={selectedVoucherType === "PERCENTAGE"}
                  className="group"
                >
                  <div className="flex flex-col gap-6">
                    <label className="flex items-center gap-2 text-primary group-data-[disabled=true]:text-tonal-dark-cream-50 cursor-pointer group-data-[disabled=true]:cursor-not-allowed">
                      <RadioGroupItem value="YES" className="block" />
                      Apply to all products
                    </label>
                    <label className="flex items-center gap-2 text-primary group-data-[disabled=true]:text-tonal-dark-cream-50 cursor-pointer group-data-[disabled=true]:cursor-not-allowed">
                      <RadioGroupItem value="NO" className="block" />
                      Apply to the following products
                    </label>
                  </div>
                </RadioGroup>
              )}
            />
            {!isApplyToAllProducts && selectedVoucherType !== "PERCENTAGE" && (
              <div className="pl-6 flex flex-col items-start gap-4 text-primary">
                <div className="py-2 space-y-4 w-full">
                  <div className="flex items-center justify-between w-full">
                    <CheckboxInput
                      label="Direct licensing Germany"
                      checked={selectedProducts.includes("DIRECT_LICENSE")}
                      onChange={(e) => handleProductSelection("DIRECT_LICENSE", e.target.checked)}
                    />
                    {selectedProducts.includes("DIRECT_LICENSE") && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-tonal-dark-cream-40">
                          {directLicenseYears.length} years selected
                        </span>
                      </div>
                    )}
                  </div>
                  {selectedProducts.includes("DIRECT_LICENSE") && (
                    <div className="px-6 space-y-2">
                      <div className="flex items-center justify-between">
                        <i className="text-xs text-tonal-dark-cream-40 font-medium">Select Years</i>
                      </div>
                      <div className="flex flex-wrap gap-8">
                        {years.map((year) => (
                          <CheckboxInput
                            key={year}
                            label={String(year)}
                            value={year}
                            checked={directLicenseYears.includes(year)}
                            onChange={(e) => handleYearSelection(Number(e.target.value), e.target.checked)}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="py-2 space-y-4 w-full">
                  <div className="flex items-center justify-between w-full">
                    <CheckboxInput
                      label="Licensing service EU"
                      checked={selectedProducts.includes("EU_LICENSE")}
                      onChange={(e) => handleProductSelection("EU_LICENSE", e.target.checked)}
                    />
                    {selectedProducts.includes("EU_LICENSE") && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-tonal-dark-cream-40">
                          {euLicenseCountries.length} countries selected
                        </span>
                      </div>
                    )}
                  </div>
                  {selectedProducts.includes("EU_LICENSE") && (
                    <div className="px-6 space-y-2">
                      <div className="flex items-center justify-between">
                        <i className="text-xs text-tonal-dark-cream-40 font-medium">Select Countries</i>
                      </div>
                      <div className="flex flex-wrap md:grid md:grid-cols-5 gap-3">
                        {countries?.map(({ id, code, flag_url }) => (
                          <label key={id} className="flex flex-shrink-0 items-center gap-1 py-1 pr-6 cursor-pointer">
                            <CheckboxInput
                              key={id}
                              value={id.toString()}
                              disabled={isCountriesLoading}
                              checked={euLicenseCountries.includes(id.toString())}
                              onChange={(e) => handleEuCountrySelection(e.target.value, e.target.checked)}
                            />
                            <Image
                              src={flag_url}
                              alt={`${code} flag`}
                              width={16}
                              height={16}
                              className="rounded-full border h-4 w-4 object-cover"
                            />
                            <span className="text-primary uppercase">{code}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="py-2 space-y-4 w-full">
                  <div className="flex items-center justify-between w-full">
                    <CheckboxInput
                      label="Action guide"
                      checked={selectedProducts.includes("ACTION_GUIDE")}
                      onChange={(e) => handleProductSelection("ACTION_GUIDE", e.target.checked)}
                    />
                    {selectedProducts.includes("ACTION_GUIDE") && (
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-tonal-dark-cream-40">
                          {actionGuideCountries.length} countries selected
                        </span>
                      </div>
                    )}
                  </div>
                  {selectedProducts.includes("ACTION_GUIDE") && (
                    <div className="px-6 space-y-2">
                      <div className="flex items-center justify-between">
                        <i className="text-xs text-tonal-dark-cream-40 font-medium">Select Countries</i>
                      </div>
                      <div className="flex flex-wrap md:grid md:grid-cols-5 gap-3">
                        {countries?.map(({ id, code, flag_url }) => (
                          <label key={id} className="flex flex-shrink-0 items-center gap-1 py-1 pr-6 cursor-pointer">
                            <CheckboxInput
                              key={id}
                              value={id.toString()}
                              disabled={isCountriesLoading}
                              checked={actionGuideCountries.includes(id.toString())}
                              onChange={(e) => handleActionGuideCountrySelection(e.target.value, e.target.checked)}
                            />
                            <Image
                              src={flag_url}
                              alt={`${code} flag`}
                              width={16}
                              height={16}
                              className="rounded-full border h-4 w-4 object-cover"
                            />
                            <span className="text-primary uppercase">{code}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </section>

        <div className="flex justify-end">
          <Button type="submit" variant="filled" color="yellow" size="medium" disabled={isLoading}>
            {isLoading ? "Creating..." : "Create Discount"}
          </Button>
        </div>
      </form>
    </div>
  );
}
