import { CertificateService } from "@/certificate/certificate.service";
import { CommissionService } from "@/commission/commission.service";
import { ContractService } from "@/contract/contract.service";
import { CouponService } from "@/coupon/coupon.service";
import { CustomerServiceSetup } from "@/customer-commitment/dto/customer-commitment.dto";
import { CustomerIoService } from "@/customer-io/customer-io.service";
import { DatabaseService } from "@/database/database.service";
import { FileService } from "@/file/file.service";
import { HttpModuleService } from "@/http/http.service";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ROLE } from "@/shared/auth/const";
import { Role } from "@/shared/auth/role.enum";
import { AuthenticatedUser } from "@/shared/auth/user.decorator";
import { generatePdf } from "@/shared/utils/generate-pdf";
import { ShoppingCartPackagingService } from "@/shopping-cart/dto/update-shopping-cart.dto";
import { BadRequestException, ForbiddenException, Injectable, NotFoundException } from "@nestjs/common";
import {
  GeneralInformationType,
  LicenseReportSetRhythm,
  LicenseRequiredInformationKind,
  LicenseRequiredInformationStatus,
  LicenseRequiredInformationType,
  LicenseVolumeReportStatus,
  Prisma,
  ShoppingCartStatus,
} from "@prisma/client";
import axios from "axios";
import { Exception } from "handlebars";
import * as crypto from "node:crypto";
import { firstValueFrom, lastValueFrom } from "rxjs";
import { GeneratePurchasePdfDto } from "./dto/generate-purchase-pdf.dto";
import { PurchaseDto } from "./dto/purchase.dto";

function getMonthName(): string {
  const date = new Date();
  const monthNames = [
    "JANUARY",
    "FEBRUARY",
    "MARCH",
    "APRIL",
    "MAY",
    "JUNE",
    "JULY",
    "AUGUST",
    "SEPTEMBER",
    "OCTOBER",
    "NOVEMBER",
    "DECEMBER",
  ];

  return monthNames[date.getMonth()];
}

function getInitialVolumeReportInterval(rhythm: LicenseReportSetRhythm, licenseYear: number) {
  if (rhythm === "QUARTERLY") return "Q1";

  if (rhythm === "MONTHLY") return getMonthName();

  return String(licenseYear);
}

const ORDER_ITEM_SERVICE_TYPE_LABEL = {
  EU_LICENSE: "Licensing Service",
  DIRECT_LICENSE: "Direct License",
  ACTION_GUIDE: "Action guide",
} as const;

const ORDER_ITEM_SPECIFICATION_TYPE_LABEL = {
  HANDLING_FEE: "Handling fee",
  REGISTRATION_FEE: "Registration fee",
  VARIABLE_HANDLING_FEE: "Variable handling fee",
  DIRECT_LICENSE_PURCHASE: "Purchase",
  DIRECT_LICENSE_REFUND: "Volume reduction",
  DIRECT_LICENSE_VOLUME_CHANGE: "Volume change",
} as const;

const CRM_API_URL = process.env.CRM_API_URL;

type FullShoppingCart = Prisma.ShoppingCartGetPayload<{
  include: {
    customer_commitments: true;
    items: true;
    affiliate_customer: true;
    affiliate_partner: true;
    coupon: true;
    customer: true;
  };
}>;

type FullCustomerContract = Prisma.ContractGetPayload<{
  include: {
    licenses: {
      include: {
        price_list: true;
        certificates: true;
      };
      orderBy: {
        created_at: "asc";
      };
    };
    action_guides: {
      include: {
        price_list: true;
      };
    };
  };
}>;

@Injectable()
export class PurchaseService {
  constructor(
    private databaseService: DatabaseService,
    private readonly customerIoService: CustomerIoService,
    private httpModuleService: HttpModuleService,
    private readonly certificateService: CertificateService,
    private readonly fileService: FileService,
    private readonly couponService: CouponService,
    private readonly commissionService: CommissionService,
    private readonly contractService: ContractService
  ) {}

  async create({ shopping_cart_id }: PurchaseDto, user: AuthenticatedUser) {
    return await this.databaseService.$transaction(
      async (tx) => {
        const shoppingCart = (await tx.shoppingCart.findUnique({
          where: { id: shopping_cart_id },
          include: {
            items: {
              where: {
                deleted_at: null,
              },
            },
            customer_commitments: true,
            affiliate_customer: true,
            affiliate_partner: true,
            coupon: true,
            customer: true,
          },
        })) satisfies FullShoppingCart;

        if (!shoppingCart) throw new NotFoundException("Shopping cart not found");

        if (!shoppingCart.items.length) {
          throw new NotFoundException("Shopping cart is empty");
        }

        if (!shoppingCart.email || !shoppingCart.customer)
          throw new NotFoundException("Shopping cart is missing customer email");

        if (user.role === Role.CUSTOMER && shoppingCart.email !== user.email) {
          throw new ForbiddenException("User not authorized to purchase this shopping cart");
        }

        const orderResponse = await this.createOrder(shopping_cart_id);

        if (!orderResponse.success) {
          throw new Exception(`Error creating order: ${orderResponse.error}`);
        }

        const { order, payment } = orderResponse.data;

        const contracts = (await tx.contract.findMany({
          where: { customer_id: shoppingCart.customer.id, deleted_at: null },
          include: {
            licenses: {
              include: {
                price_list: true,
                certificates: true,
              },
              orderBy: {
                created_at: "asc",
              },
            },
            action_guides: {
              include: {
                price_list: true,
              },
            },
          },
        })) satisfies FullCustomerContract[];

        await this.handleEuLicenses(shoppingCart, contracts, tx);

        await this.handleDirectLicense(shoppingCart, contracts, tx);

        await this.handleActionGuides(shoppingCart, contracts, tx);

        if (shoppingCart.coupon_id) {
          await this.couponService.useCoupon(
            {
              code: shoppingCart?.coupon?.code,
              customerId: shoppingCart.customer?.id,
              shoppingCartId: shoppingCart?.id,
              orderId: order.id,
            },
            tx
          );
        }

        if (shoppingCart.affiliate_customer_id || shoppingCart.affiliate_partner_id || shoppingCart.coupon_id) {
          await this.commissionService.create(
            {
              commission_percentage: 10,
              order_id: order.id,
              price_total: shoppingCart.total,
              affiliate_link: shoppingCart.affiliate_link,
              coupon_id: shoppingCart.coupon_id,
              order_customer_id: shoppingCart.customer.id,
              service_type: shoppingCart.items?.[0]?.service_type,
              user_id: shoppingCart.affiliate_customer?.user_id || shoppingCart.affiliate_partner?.user_id,
            },
            tx
          );
        }

        await tx.shoppingCart.update({
          where: {
            id: shopping_cart_id,
          },
          data: {
            status: ShoppingCartStatus.PURCHASED,
          },
        });

        await this.customerIoService.processPurchaseData(shoppingCart.customer.id, tx);

        await this.generateInvoicePdf(order, payment);

        const hasContractDirect = contracts.some((contract) => contract.type === "DIRECT_LICENSE");
        const hasVolumesDirect = shoppingCart.items.some((item) => item.service_type === "DIRECT_LICENSE");
        const isPurchaseVolumesDirect = hasContractDirect && hasVolumesDirect;

        if (!isPurchaseVolumesDirect) await this.sendPurchaseEmail({ ...order, customer: shoppingCart.customer });

        return {
          order_id: order.id,
          order,
          message: "Services added to contract successfully",
        };
      },
      {
        maxWait: 180000,
        timeout: 180000,
      }
    );
  }

  private async handleEuLicenses(
    shoppingCart: FullShoppingCart,
    contracts: FullCustomerContract[],
    tx: Prisma.TransactionClient
  ) {
    const nowDate = new Date();

    const euLicenses = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

    if (!euLicenses.length) return;

    let euLicenseContract = contracts.find((contract) => contract.type === "EU_LICENSE");

    if (!euLicenseContract) {
      const biggestYear = Math.max(...euLicenses.map((license) => license.year));

      euLicenseContract = await tx.contract.create({
        data: {
          customer_id: shoppingCart.customer.id,
          type: "EU_LICENSE",
          status: "ACTIVE",
          title: "EU License",
          start_date: new Date(nowDate.getFullYear(), 0, 1),
          end_date: new Date(biggestYear, 11, 31),
          general_informations: {
            create: [
              {
                kind: LicenseRequiredInformationKind.GENERAL_INFORMATION,
                setup_general_information_id: 1,
                type: GeneralInformationType.DOCUMENT,
                status: LicenseRequiredInformationStatus.OPEN,
                name: "Power of Attorney",
                description: "Power of Attorney",
              },
            ],
          },
        },
        include: {
          licenses: {
            include: {
              price_list: true,
              certificates: true,
            },
          },
          action_guides: {
            include: {
              price_list: true,
            },
          },
        },
      });

      // Generated in first eu license purchase
      await this.contractService.generateContractPdf(euLicenseContract.id, tx);
    }

    const currentLicenses = euLicenseContract.licenses;

    await Promise.all(
      euLicenses
        .filter(
          (license) => !currentLicenses.find((l) => l.country_code === license.country_code && l.year === license.year)
        )
        .map(async (license) => {
          const countryLicenses = currentLicenses.filter((l) => l.country_code === license.country_code);

          const firstCountryLicense = !!countryLicenses.length ? countryLicenses[0] : null;

          const customerCommitment = shoppingCart.customer_commitments.find(
            (i) => i.country_code === license.country_code
          );
          const priceList = (firstCountryLicense?.price_list[0] || license.price_list) as any;

          if (!customerCommitment) {
            throw new BadRequestException(`Customer commitment not found for ${license.country_code}`);
          }

          const { mondayVolumeReports, mondayRegistrationAndTerminations } = await (async () => {
            try {
              const responseVolumeReports = await lastValueFrom(
                this.httpModuleService.crm({
                  method: "POST",
                  url: "/volume-reports",
                  params: {
                    country: license.country_name,
                    customerId: String(shoppingCart.customer.id),
                    activeYear: String(license.year),
                  },
                })
              );

              const responseRegistrationAndTerminations = await lastValueFrom(
                this.httpModuleService.crm({
                  method: "POST",
                  url: "/registration-and-terminations",
                  params: {
                    country: license.country_name,
                    customerId: String(shoppingCart.customer.id),
                    activeYear: String(license.year),
                  },
                })
              );

              return {
                mondayVolumeReports: responseVolumeReports.data || null,
                mondayRegistrationAndTerminations: responseRegistrationAndTerminations.data || null,
              };
            } catch (err) {
              if (err.response) {
                console.log("Monday purchase error", err?.response?.data);
              } else {
                console.log("Monday purchase error", err?.message);
              }

              return {
                mondayVolumeReports: null,
                mondayRegistrationAndTerminations: null,
              };
            }
          })();

          const setup = customerCommitment.service_setup as CustomerServiceSetup;

          let licenseCreateData: Prisma.LicenseCreateInput = {
            country_id: license.country_id,
            country_code: license.country_code,
            country_name: license.country_name,
            country_flag: license.country_flag,
            registration_number: crypto.randomUUID().split("-")[0],
            year: license.year,
            start_date: new Date(license.year, 0, 1),
            end_date: new Date(license.year, 11, 31),
            registration_and_termination_monday_ref: Number(mondayRegistrationAndTerminations) || null,
            price_list: {
              create: {
                setup_price_list_id: priceList.id,
                name: priceList.name,
                description: priceList.description,
                condition_type: priceList.condition_type,
                condition_type_value: priceList.condition_type_value,
                start_date: priceList.start_date,
                end_date: priceList.end_date,
                registration_fee: priceList.registration_fee,
                handling_fee: priceList.handling_fee,
                variable_handling_fee: priceList.variable_handling_fee,
                basic_price: null,
                minimum_price: null,
              },
            },
            contract: {
              connect: {
                id: euLicenseContract.id,
              },
            },
            packaging_services: {
              create: (license.packaging_services as any[]).map((packagingService) => {
                const setupPackagingService = setup.packaging_services.find(
                  (service) => service.id === packagingService.id
                );

                if (!setupPackagingService) {
                  throw new BadRequestException(
                    `Packaging service not found for: ${license.country_code}. Packaging service id: ${packagingService.id}`
                  );
                }

                const reportTable = (() => {
                  if (setupPackagingService.report_set.mode !== "ON_PLATAFORM") return null;

                  return {
                    fractions: setupPackagingService.report_set.fractions,
                    columns: setupPackagingService.report_set.columns,
                  };
                })();

                return {
                  setup_packaging_service_id: setupPackagingService.id,
                  name: setupPackagingService.name,
                  description: setupPackagingService.description,
                  report_set: {
                    create: {
                      setup_report_set_id: setupPackagingService.report_set.id,
                    },
                  },
                  report_set_frequency: {
                    create: {
                      setup_report_set_frequency_id: setupPackagingService.report_set_frequency.id,
                      rhythm: setupPackagingService.report_set_frequency.rhythm as LicenseReportSetRhythm,
                      frequency: JSON.stringify(setupPackagingService.report_set_frequency.frequency),
                    },
                  },
                  volume_reports: {
                    create: [
                      {
                        year: license.year,
                        interval: getInitialVolumeReportInterval(
                          setupPackagingService.report_set_frequency.rhythm,
                          license.year
                        ),
                        status: LicenseVolumeReportStatus.OPEN,
                        volume_report_monday_ref: Number(mondayVolumeReports) || null,
                        report_table: reportTable,
                      },
                    ],
                  },
                };
              }),
            },
          };

          if (firstCountryLicense) {
            licenseCreateData = {
              ...licenseCreateData,
              required_informations: {
                create: setup.required_informations.map((requiredInformation) => ({
                  setup_required_information_id: requiredInformation.id,
                  name: requiredInformation.name,
                  type: requiredInformation.type as LicenseRequiredInformationType,
                  description: requiredInformation.description,
                  file_id: requiredInformation.file_id || null,
                })),
              },
              certificates: {
                create: [
                  {
                    name: `${license.country_name} Certificate`,
                  },
                  {
                    name: `${license.country_name} specific certificate`,
                  },
                ],
              },
              representative_tiers: {
                create: setup.representative_tier
                  ? {
                      setup_representative_tier_id: setup.representative_tier.id,
                      name: setup.representative_tier.name,
                      price: setup.representative_tier.price,
                    }
                  : [],
              },
              other_costs: {
                create: setup.other_costs.map((otherCost) => ({
                  setup_other_cost_id: otherCost.id,
                  name: otherCost.name,
                  price: otherCost.price,
                })),
              },
              next_steps: {
                create: [
                  {
                    title: "Create an account",
                    available_date: nowDate,
                    deadline_date: new Date(license.year, 11, 31),
                  },
                  {
                    title: "Enter required information",
                    available_date: nowDate,
                    deadline_date: new Date(license.year, 11, 31),
                  },
                  {
                    title: "Register with CONAI",
                    available_date: nowDate,
                    deadline_date: new Date(license.year, 11, 31),
                  },
                  {
                    title: "Quantity declaration",
                    available_date: nowDate,
                    deadline_date: new Date(license.year, 11, 31),
                  },
                ],
              },
            };
          }

          return tx.license.create({ data: licenseCreateData });
        })
    );

    // Updated every service change
    await this.contractService.generateServiceOverviewPdf(euLicenseContract.id, tx);
  }

  private async handleDirectLicense(
    shoppingCart: FullShoppingCart,
    contracts: FullCustomerContract[],
    tx: Prisma.TransactionClient
  ) {
    const nowDate = new Date();
    const directLicense = shoppingCart.items.find((item) => item.service_type === "DIRECT_LICENSE");

    if (!directLicense) return;

    let directLicenseContract = contracts.find((contract) => contract.type === "DIRECT_LICENSE");

    const firstDirectLicense = directLicenseContract?.licenses[0];
    const priceList = (firstDirectLicense?.price_list[0] || directLicense.price_list) as any;

    if (!directLicenseContract) {
      directLicenseContract = await tx.contract.create({
        data: {
          customer_id: shoppingCart.customer.id,
          type: "DIRECT_LICENSE",
          status: "ACTIVE",
          title: "Direct License",
          start_date: new Date(nowDate.getFullYear(), 0, 1),
          end_date: new Date(directLicense.year, 11, 31),
        },
        include: {
          licenses: {
            include: {
              price_list: true,
              certificates: true,
            },
          },
          action_guides: {
            include: {
              price_list: true,
            },
          },
        },
      });
    }

    let currentLicense = directLicenseContract.licenses.find((license) => license.year === directLicense.year);

    const packagingService = directLicense.packaging_services[0] as ShoppingCartPackagingService;
    const fractions = Object.values(packagingService?.fractions || {});
    const reportTable = fractions ? { fractions } : null;

    if (currentLicense) {
      const licensePackagingService = await tx.licensePackagingService.findFirst({
        where: {
          license_id: currentLicense.id,
        },
        include: {
          volume_reports: {
            include: {
              volume_report_items: true,
            },
          },
        },
      });

      const volumeReport = licensePackagingService.volume_reports[0];

      for (const fraction of fractions) {
        if (!fraction.weight) continue;

        const currentVolumeReportItem = volumeReport?.volume_report_items.find(
          (item) => item.setup_fraction_code === fraction.code
        );
        await tx.licenseVolumeReportItem.upsert({
          where: {
            id: currentVolumeReportItem?.id || 0,
          },
          update: {
            value: fraction.weight,
          },
          create: {
            setup_fraction_id: 0,
            setup_fraction_code: fraction.code,
            setup_column_id: 0,
            setup_column_code: fraction.code,
            value: fraction.weight,
            volume_report: {
              connect: {
                id: volumeReport?.id || 0,
              },
            },
          },
        });
      }
    } else {
      let licenseCreateData: Prisma.LicenseCreateInput = {
        contract: {
          connect: {
            id: directLicenseContract.id,
          },
        },
        country_id: directLicense.country_id,
        country_code: directLicense.country_code,
        country_name: directLicense.country_name,
        country_flag: directLicense.country_flag,
        registration_number: crypto.randomUUID().split("-")[0],
        year: directLicense.year,
        start_date: new Date(directLicense.year, 0, 1),
        end_date: new Date(directLicense.year, 11, 31),
        packaging_services: {
          create: {
            name: "Direct License Packaging Service",
            description: "Direct License Packaging Service",
            setup_packaging_service_id: 0,
            report_set: {
              create: {
                setup_report_set_id: 0,
              },
            },
            report_set_frequency: {
              create: {
                setup_report_set_frequency_id: 0,
                rhythm: "ANNUALLY",
                frequency: {},
              },
            },
            volume_reports: {
              create: {
                year: directLicense.year,
                interval: String(directLicense.year),
                status: LicenseVolumeReportStatus.DONE,
                report_table: reportTable,
                volume_report_items: {
                  create: fractions.map((fraction) => ({
                    setup_fraction_id: 0,
                    setup_fraction_code: fraction.code,
                    setup_column_id: 0,
                    setup_column_code: fraction.code,
                    value: fraction.weight,
                  })),
                },
              },
            },
          },
        },
        price_list: {
          create: {
            setup_price_list_id: priceList?.id,
            name: priceList?.name,
            description: priceList?.description,
            condition_type: priceList?.condition_type,
            condition_type_value: priceList?.condition_type_value,
            start_date: priceList?.start_date || null,
            end_date: priceList?.end_date || null,
            basic_price: priceList?.basic_price,
            minimum_price: priceList?.minimum_price,
            registration_fee: null,
            handling_fee: null,
            variable_handling_fee: null,
            thresholds: priceList.thresholds,
          },
        },
      };

      if (!firstDirectLicense) {
        licenseCreateData = {
          ...licenseCreateData,
          certificates: {
            create: [
              {
                name: `Direct License Certificate`,
              },
            ],
          },
          required_informations: {
            create: [
              {
                setup_required_information_id: 1,
                name: "LUCID",
                type: LicenseRequiredInformationType.TEXT,
                description: "LUCID Number",
              },
            ],
          },
          next_steps: {
            create: [
              {
                title: "Add LUCID Number",
                available_date: nowDate,
                deadline_date: new Date(directLicense.year, 11, 31),
              },
              {
                title: "Declare Volumes Intra-year",
                available_date: nowDate,
                deadline_date: new Date(directLicense.year, 11, 31),
              },
              {
                title: "Declare Volumes - End of the year",
                available_date: nowDate,
                deadline_date: new Date(directLicense.year, 11, 31),
              },
            ],
          },
        };
      }

      currentLicense = await tx.license.create({
        data: licenseCreateData,
        include: {
          certificates: true,
          price_list: true,
        },
      });
    }

    try {
      await axios.post(
        `${CRM_API_URL}/registration-and-terminations`,
        {
          country: "Germany",
          customerId: String(shoppingCart.customer.id),
          activeYear: String(directLicense.year),
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );

      await axios.post(
        `${CRM_API_URL}/volume-reports`,
        {
          country: "Germany",
          customerId: String(shoppingCart.customer.id),
          activeYear: String(directLicense.year),
        },
        {
          headers: {
            [HEADER_SYSTEM_API_KEY]: process.env.SYSTEM_API_KEY,
            [HEADER_USER_ROLE]: Role.SYSTEM,
          },
        }
      );
    } catch (error) {
      console.error("CRM API ERROR", error);
    }

    try {
      const certificate_id = currentLicense.certificates[0].id;
      const pdfFile = await this.certificateService.generateSavePdf({
        company: {
          name: "teste",
        },
        year: directLicense.year,
        certificate_id,
      });

      await this.fileService.uploadFile(
        {
          user: {
            id: `${shoppingCart.customer.user_id}`,
            role: `customer`,
          },
          certificate_id,
          type: `CONTRACT`,
        },
        pdfFile,
        tx
      );
    } catch (error) {
      console.error("ERROR DIRECT LICENSE CERTIFICATE", error);
    }
  }

  private async handleActionGuides(
    shoppingCart: FullShoppingCart,
    contracts: FullCustomerContract[],
    tx: Prisma.TransactionClient
  ) {
    const nowDate = new Date();

    const actionGuides = shoppingCart.items.filter((item) => item.service_type === "ACTION_GUIDE");

    if (!actionGuides.length) return;

    let actionGuideContract = contracts.find((contract) => contract.type === "ACTION_GUIDE");

    if (!actionGuideContract) {
      actionGuideContract = await tx.contract.create({
        data: {
          customer_id: shoppingCart.customer.id,
          type: "ACTION_GUIDE",
          status: "ACTIVE",
          title: "Action Guide",
          start_date: new Date(nowDate.getFullYear(), 0, 1),
          end_date: new Date(nowDate.getFullYear(), 11, 31),
        },
        include: {
          licenses: {
            include: {
              price_list: true,
              certificates: true,
            },
          },
          action_guides: {
            include: {
              price_list: true,
            },
          },
        },
      });
    }

    const currentActionGuides = actionGuideContract.action_guides;

    await Promise.all(
      actionGuides
        .filter((actionGuide) => !currentActionGuides.find((l) => l.country_code === actionGuide.country_code))
        .map((actionGuide) => {
          const priceList = actionGuide.price_list as any;
          return tx.actionGuide.create({
            data: {
              contract_id: actionGuideContract.id,
              country_id: actionGuide.country_id,
              country_code: actionGuide.country_code,
              country_name: actionGuide.country_name,
              country_flag: actionGuide.country_flag,
              price_list: {
                create: {
                  setup_price_list_id: priceList.id,
                  name: priceList.name,
                  description: priceList.description,
                  price: priceList.price,
                },
              },
            },
          });
        })
    );
  }

  private async createOrder(shoppingCartId: string) {
    try {
      const response = await firstValueFrom(
        this.httpModuleService.payments({
          url: "/purchase",
          method: "POST",
          params: {
            shopping_cart_id: shoppingCartId,
          },
        })
      );

      console.log("response", response);

      if (response.status >= 400) {
        return {
          success: false,
          error: response.data?.message || "Request failed",
          statusCode: response.status,
          details: response.data,
        };
      }

      return { success: true, data: response.data } as const;
    } catch (err: any) {
      return {
        success: false,
        error: err.response?.data?.message || err.message,
        statusCode: err.response?.status || 500,
      } as const;
    }
  }

  private async sendPurchaseEmail(order: any) {
    try {
      await lastValueFrom(
        this.httpModuleService.admin({
          url: "/emails/send-message",
          params: {
            transactional_message_id: "42",
            identifiers: {
              email: order.customer.email,
            },
            to: order.customer.email,
            from: "Lizenzero <<EMAIL>>",
            message_data: {
              name: order.customer.first_name,
              url_download_invoice: `${process.env.PAYMENT_API_URL}/invoice/${order.id}/download`,
            },
            subject: "Purchase completed successfully",
          },
          method: "post",
        })
      );

      return { success: true } as const;
    } catch (error) {
      return { success: false, error: error } as const;
    }
  }

  private async generateInvoicePdf(order: any, payment: any, tx?: Prisma.TransactionClient) {
    const templateData: GeneratePurchasePdfDto = {
      order_id: order.id,
      created_at: new Date(order.created_at).toLocaleDateString(),
      tax_id: order.tax_id,
      vat_id: order.vat_id,
      vat_value: order.vat_value ? (order.vat_value / 100).toFixed(2) : null,
      vat_percentage: order.vat_percentage,
      coupon_code: order.coupon_code,
      coupon_value: order.coupon_value ? (order.coupon_value / 100).toFixed(2) : null,
      subtotal: (order.sub_amount / 100).toFixed(2),
      total: (order.amount / 100).toFixed(2),
      items: order.items.map((item, itemIndex) => ({
        index: itemIndex + 1,
        service: `${ORDER_ITEM_SERVICE_TYPE_LABEL[item.service_type]} (${item.country_name})`,
        description:
          item.service_type !== "DIRECT_LICENSE"
            ? ORDER_ITEM_SPECIFICATION_TYPE_LABEL[item.specification_type]
            : `${ORDER_ITEM_SPECIFICATION_TYPE_LABEL[item.specification_type]}\n${item.description}`,
        contract_number: item.contract_number,
        amount: (item.price / 100).toFixed(2),
      })),
      payment_method_name: payment.payment_method?.type || "-",
      customer: {
        id: order.payment_customer.customer_id,
        name: order.payment_customer.name,
        email: order.payment_customer.email,
      },
      billing: {
        full_name: order.billing.full_name,
        city: order.billing.city,
        country: order.billing.country_name,
        street_and_number: order.billing.street_and_number,
        zip_code: order.billing.zip_code,
      },
    };

    try {
      const invoicePdf = await generatePdf({
        templateFile: "invoice.hbs",
        fileName: `invoice-${order.id}.pdf`,
        data: templateData,
        margin: {
          top: 48,
          right: 80,
          bottom: 75,
          left: 80,
        },
      });

      const invoiceFile = await this.fileService.uploadFile(
        {
          user: {
            id: String(order.payment_customer.customer_id),
            role: "customer",
          },
          order_id: order.id,
          type: "INVOICE",
        },
        invoicePdf,
        tx
      );

      console.log(`http://localhost:4000/files/${invoiceFile.id}`);
      return { pdf: invoicePdf, file: invoiceFile };
    } catch (error) {
      console.log(error);
      console.error("Error generating PDF:", error);
      throw error;
    }
  }
}
