/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { GraphColors } from "@/utils/graph-colors";
import Mapbox, { StyleSpecification } from "mapbox-gl";
import Image from "next/image";
import { useState } from "react";
import Map, { Layer, LayerProps, Source } from "react-map-gl";
import countriesPolygon from "../countries.json";
import mapStyle from "../mapStyle.json";
import { formatCurrency } from "@/utils/format-currency";

interface Country {
  code: string;
  flag_url: string;
  name: string;
  revenue: number;
  total_licenses: number;
}

interface HoveredCountry {
  country: Country;
  position: {
    x: number;
    y: number;
  };
}

interface IMapCountriesProps {
  selectedCountries: Country[];
  children?: React.ReactNode;
  graphColors: GraphColors;
}

export const RevenueAndContractsCountriesMap = ({
  children,
  selectedCountries = [],
  graphColors,
}: IMapCountriesProps) => {
  const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  const [hoveredCountry, setHoveredCountry] = useState<HoveredCountry | null>(null);

  const customColorExpressions = selectedCountries
    .map((country) => [["==", ["get", "iso_a2_eh"], country.code], graphColors.getColor(country.code)])
    .flat();

  const polygonLayer: LayerProps = {
    id: "countries",
    type: "fill", // Changed from fill-extrusion to fill
    paint: {
      "fill-color": [
        "case",
        ...customColorExpressions,
        "transparent", // Default color for unselected countries
      ],
      "fill-opacity": 0.8,
    },
  };

  const lineLayer: LayerProps = {
    id: "countries-line-layer",
    type: "line",
    source: "countries",
    layout: {},
    paint: {
      "line-color": "#FFF",
      "line-width": 0.5, // Increased line width for better visibility
      "line-opacity": 0.8,
    },
  };

  function handleHoverCountry(event: mapboxgl.MapLayerMouseEvent) {
    const {
      features,
      point: { x, y },
    } = event;
    if (!features || !features[0]) {
      setHoveredCountry(null);
      return;
    }
    const feature = features[0];
    const countryCode: string = feature.properties?.iso_a2_eh;
    if (!countryCode) {
      setHoveredCountry(null);
      return;
    }
    const matchingCountry = selectedCountries.find((country) => country.code === countryCode);
    if (!matchingCountry) {
      setHoveredCountry(null);
      return;
    }
    setHoveredCountry({
      country: matchingCountry,
      position: { x, y: y - 25 },
    });
  }

  return (
    <div className="rounded-[40px] w-full h-full overflow-hidden relative">
      <Map
        mapLib={Mapbox as any}
        mapboxAccessToken={mapboxToken}
        mapStyle={mapStyle as StyleSpecification}
        initialViewState={{ latitude: 46.8182, longitude: 8.2275, zoom: 3 }}
        maxZoom={3}
        minZoom={2}
        pitch={0}
        bearing={0}
        doubleClickZoom={true}
        interactiveLayerIds={["countries"]}
        onMouseMove={handleHoverCountry}
      >
        <Source type="geojson" data={countriesPolygon as GeoJSON.GeoJSON}>
          <Layer {...polygonLayer} />
          <Layer {...lineLayer} />
        </Source>
        {hoveredCountry && (
          <div
            className="absolute bg-white py-2 px-4 rounded-lg z-50 pointer-events-none shadow-lg"
            style={{ left: hoveredCountry.position.x, top: hoveredCountry.position.y }}
          >
            <div className="flex items-center gap-2">
              <Image
                src={hoveredCountry.country.flag_url}
                alt="flag country"
                className="rounded-full w-6 h-6"
                width={24}
                height={24}
              />
              <p className="text-gray-800 text-normal font-medium">{hoveredCountry.country.name}</p>
            </div>
            <p className="text-red text-sm">{hoveredCountry.country.total_licenses} licenses</p>
            <p className="text-gray-800 text-sm">{formatCurrency(hoveredCountry.country.revenue)} revenue</p>
          </div>
        )}
        {children}
      </Map>
    </div>
  );
};
