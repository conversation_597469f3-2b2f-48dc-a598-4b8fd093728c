ARG NODE_VERSION=18.17.1

FROM nexus.interzero.de:5000/node:${NODE_VERSION}-alpine AS base
RUN yarn global add pnpm
WORKDIR /app
COPY . .

FROM base AS builder

WORKDIR /app

COPY --from=base /app/package.json /app/pnpm-lock.yaml ./

RUN pnpm install

COPY --from=base /app .
COPY --from=base /app/prisma ./prisma

ARG PORT=3000
ENV PORT=${PORT}
ARG ENVIRONMENT
ENV ENVIRONMENT=${ENVIRONMENT}

ARG SYSTEM_API_KEY
ENV SYSTEM_API_KEY=${SYSTEM_API_KEY}
ARG DATABASE_URL
ENV DATABASE_URL=${DATABASE_URL}
ARG SENDGRID_API_KEY
ENV SENDGRID_API_KEY=${SENDGRID_API_KEY}
ARG LAMBDA_REQUEST_PRESIGNED_URL
ENV LAMBDA_REQUEST_PRESIGNED_URL=${LAMBDA_REQUEST_PRESIGNED_URL}
ARG CUSTOMER_IO_KEY
ENV CUSTOMER_IO_KEY=${CUSTOMER_IO_KEY}
ARG CUSTOMER_IO_API_KEY
ENV CUSTOMER_IO_API_KEY=${CUSTOMER_IO_API_KEY}
ARG CUSTOMER_IO_SITE_ID
ENV CUSTOMER_IO_SITE_ID=${CUSTOMER_IO_SITE_ID}
ARG AUTH_API_URL
ENV AUTH_API_URL=${AUTH_API_URL}
ARG CUSTOMER_API_URL
ENV CUSTOMER_API_URL=${CUSTOMER_API_URL}

RUN pnpm prisma generate

RUN pnpm nest build

EXPOSE 3000

CMD ["node", "dist/src/main"]