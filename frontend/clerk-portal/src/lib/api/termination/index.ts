import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { CreateTerminationParams, Termination, UpdateTerminationParams } from "./types";

export async function createTermination(data: CreateTerminationParams) {
  const response = await api.post(ApiEndpoints.termination.create, data);

  return response.data;
}

export async function updateTermination(terminationId: number, data: UpdateTerminationParams) {
  const response = await api.put(ApiEndpoints.termination.update(terminationId), data);

  return response.data;
}

export async function getTermination(terminationId: number) {
  const response = await api.get<Termination>(ApiEndpoints.termination.get(terminationId));

  return response.data;
}
