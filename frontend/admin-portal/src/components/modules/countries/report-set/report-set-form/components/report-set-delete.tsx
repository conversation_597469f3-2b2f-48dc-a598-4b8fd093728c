import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogFooter,
  AlertDialogDescription,
  AlertDialogTitle,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { deleteReportSet } from "@/lib/api/report-sets";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";

interface ReportSetDeleteProps {
  reportSetId: number;
}

export function ReportSetDelete({ reportSetId }: ReportSetDeleteProps) {
  const router = useRouter();

  const { country } = useServiceSetup();
  const { mutate: deleteReport, isPending: isDeletingReportSet } = useMutation({
    mutationFn: deleteReportSet,
  });

  async function handleDeleteReportSet(e: React.MouseEvent<HTMLButtonElement>) {
    deleteReport(reportSetId, {
      onSuccess: () => {
        router.push(`/en/countries/${country.code}/service-setup?step=report-sets`);
        enqueueSnackbar("Report set deleted successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Error deleting report set", { variant: "error" });
      },
    });

    e.preventDefault();
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <button
          type="button"
          className="text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3"
          disabled={isDeletingReportSet}
        >
          {isDeletingReportSet ? "Deleting..." : "Delete"}
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete report set?</AlertDialogTitle>
          <AlertDialogDescription>
            By clicking on ”confirm” you are deleting this report set and all content within it.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Back</AlertDialogCancel>
          <AlertDialogAction onClick={handleDeleteReportSet}>
            {isDeletingReportSet ? "Deleting..." : "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
