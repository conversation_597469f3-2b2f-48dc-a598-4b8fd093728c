import { Body, Controller, Get, Param, <PERSON>, Post } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { CustomerConsentService } from "./customer-consent.service";
import { CreateCustomerConsentDto } from "./dto/create-customer-consent";
import { UpdateCustomerConsentDto } from "./dto/update-customer-consent";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("Customer Consent")
@Controller("customer-consent")
export class CustomerConsentController {
  constructor(private readonly customerConsentService: CustomerConsentService) {}

  @Post()
  @ApiOperation({ summary: "Create a customer consent" })
  create(@Body() createConsentDto: CreateCustomerConsentDto) {
    return this.customerConsentService.create(createConsentDto);
  }

  @Post("/create-many")
  @ApiOperation({ summary: "Create many customer consents" })
  createMany(@Body() data: CreateCustomerConsentDto[]) {
    return this.customerConsentService.createMany(data);
  }

  @Get()
  @ApiOperation({ summary: "Find all customer consents" })
  findAll() {
    return this.customerConsentService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Find one customer consent by id" })
  findOne(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerConsentService.findOne(+id, user);
  }

  @Get("/customer/:id")
  @ApiOperation({ summary: "Find all consents for a customer" })
  findAllByCustomerId(@Param("id") id: string, @User() user: AuthenticatedUser) {
    return this.customerConsentService.findByCustomerId(+id, user);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update one customer consent by id" })
  update(
    @Param("id") id: string,
    @Body() updateCustomerConsentDto: UpdateCustomerConsentDto,
    @User() user: AuthenticatedUser
  ) {
    return this.customerConsentService.update(+id, updateCustomerConsentDto, user);
  }

  @Patch("update-many")
  @ApiOperation({ summary: "Update multiple customer consents" })
  async updateMany(@Body() updateCustomerConsentDto: UpdateCustomerConsentDto[]) {
    return this.customerConsentService.updateMany(updateCustomerConsentDto);
  }
}
