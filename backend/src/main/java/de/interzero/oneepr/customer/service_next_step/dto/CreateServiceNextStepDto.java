package de.interzero.oneepr.customer.service_next_step.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * CreateServiceNextStepDto
 * DTO for creating the next step in a service.
 * Contains identifiers for related licenses or action guides, and details about the step's timing and title.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateServiceNextStepDto {

    @JsonProperty("license_id")
    @Schema(
            description = "The license ID. (Action Guide or License)",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer licenseId;

    @JsonProperty("action_guide_id")
    @Schema(
            description = "The action guide ID. (Action Guide or License)",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer actionGuideId;

    @JsonProperty("title")
    @Schema(
            description = "The title of the next step",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String title;

    @JsonProperty("available_date")
    @Schema(
            description = "The available date of the next step (e.g., ISO 8601 date-time string)",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String availableDate;

    @JsonProperty("deadline_date")
    @Schema(
            description = "The deadline date of the next step (e.g., ISO 8601 date-time string)",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String deadlineDate;

    @JsonProperty("done_at")
    @Schema(
            description = "The done date of the next step (e.g., ISO 8601 date-time string)",
            type = "string",
            format = "date-time",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String doneAt;
}
