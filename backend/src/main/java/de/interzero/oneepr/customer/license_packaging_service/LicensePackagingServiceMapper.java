package de.interzero.oneepr.customer.license_packaging_service;

import de.interzero.oneepr.customer.license_packaging_service.dto.LicensePackagingServiceDto;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping a {@link LicensePackagingService} entity to its DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This is a simple, direct mapping as the target DTO does not contain any
 * nested objects that require delegation to other mappers.
 */
@Mapper(componentModel = "spring")
public interface LicensePackagingServiceMapper {

    /**
     * Maps a {@link LicensePackagingService} entity to a {@link LicensePackagingServiceDto}.
     * <p>
     * MapStruct will automatically map all fields with matching names. The parent
     * 'license' link and the child collections in the entity are correctly
     * ignored as they do not exist in the DTO.
     *
     * @param licensePackagingService The source LicensePackagingService entity.
     * @return The mapped {@link LicensePackagingServiceDto}.
     */
    LicensePackagingServiceDto toDto(LicensePackagingService licensePackagingService);
}