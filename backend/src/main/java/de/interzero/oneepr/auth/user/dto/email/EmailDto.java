package de.interzero.oneepr.auth.user.dto.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Schema(description = "DTO for email input")
@Data
@EqualsAndHashCode(callSuper = true)
public class EmailDto extends BaseDto {

    @JsonProperty("email")
    @Schema(
            description = "Email address",
            example = "<EMAIL>"
    )
    private String email;
}
