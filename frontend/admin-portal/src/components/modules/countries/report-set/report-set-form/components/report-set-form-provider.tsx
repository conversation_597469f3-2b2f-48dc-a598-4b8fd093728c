import { <PERSON><PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { FullReportSet } from "@/types/service-setup";
import { useMutation } from "@tanstack/react-query";
import { updateReportSet } from "@/lib/api/report-sets";
import { ReportSetMode, UpdateReportSet } from "@/types/service-setup/report-set";
import { enqueueSnackbar } from "notistack";
import { queryClient } from "@/lib/react-query";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { generateCode } from "@/utils/generate-code";
import { format } from "date-fns";

const DEFAULT_END_DATE = "3000-12-31"; // Default end date when not specified

const baseFractionSchema = z.object({
  id: z.coerce.number().optional(),
  parent_id: z.coerce
    .number()
    .transform((value) => Number(value) || null)
    .nullable()
    .default(null),
  code: z.string().default(generateCode()),
  parent_code: z.string().nullable(),
  name: z
    .string({ message: "Name is required" })
    .min(1, { message: "Name is required" })
    .max(32, { message: "Name must be less than 32 characters" })
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  description: z
    .string()
    .max(128, { message: "Description must be less than 128 characters" })
    .refine((value) => value.trim() === "" || SPECIAL_CHARS_REGEX.test(value), {
      message: "Special characters are not allowed for this field",
    })
    .default(""),
  // icon_id: z.number().nullable().default(null),
  icon: z.string().default("aluminium"),
  fraction_icon_id: z.number(),
  fraction_icon: z.object({
    id: z.number(),
    image_url: z.string(),
  }),
  is_active: z.boolean().optional().default(true),
  level: z.number().default(1),
  order: z.number().default(1),
  children_visible: z.boolean().default(false),
  has_second_level: z.boolean().default(false),
  has_third_level: z.boolean().default(false),
});

// Fractions have 3 levels of hierarchy
const fractionSchema = baseFractionSchema.extend({
  children: z.lazy(() =>
    baseFractionSchema
      .extend({
        children: z.lazy(() => baseFractionSchema.array()),
      })
      .array()
  ),
});

export type FormFraction = z.infer<typeof fractionSchema>;

const baseColumnSchema = z.object({
  id: z.coerce.number().optional(),
  parent_id: z.coerce
    .number()
    .transform((value) => Number(value) || null)
    .nullable()
    .default(null),
  code: z.string().default(generateCode()),
  parent_code: z.string().nullable(),
  name: z
    .string({ message: "Name is required" })
    .min(1, { message: "Name is required" })
    .max(24, { message: "Name must be less than 24 characters" })
    .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
  description: z
    .string()
    .max(64, { message: "Description must be less than 64 characters" })
    .refine((value) => value.trim() === "" || SPECIAL_CHARS_REGEX.test(value), {
      message: "Special characters are not allowed for this field",
    })
    .default(""),
  unit_type: z.enum(["KG", "UNITS", "EACH"]).default("KG"),
  level: z.number().default(1),
  order: z.number().default(1),
  children_visible: z.boolean().default(false),
  fractions: z
    .array(
      z.object({
        column_code: z.string(),
        fraction_code: z.string(),
      })
    )
    .optional()
    .default([]),
});

// Columns have 2 levels of hierarchy
const columnSchema = baseColumnSchema.extend({
  children: z.lazy(() => baseColumnSchema.array()),
});

export type FormColumn = z.infer<typeof columnSchema>;

export const reportSetPriceListFormSchema = z.object({
  id: z.number().optional(),
  title: z.string().min(1, { message: "Title is required" }),
  license_year: z.number().min(1, { message: "License year is required" }),
  start_date: z.string().min(1, { message: "Start date is required" }),
  // PRICE_PER_VOLUME_MINIMUM_FEE is not use anymore, PRICE_PER_VOLUME_BASE_PRICE will include "PRICE_PER_VOLUME_BASE_PRICE" and "PRICE_PER_VOLUME_MINIMUM_FEE"
  type: z.enum(["FIXED_PRICE", "PRICE_PER_CATEGORY", "PRICE_PER_VOLUME_BASE_PRICE", "PRICE_PER_VOLUME_MINIMUM_FEE"]),
  fixed_price: z.number().nullable().default(null),
  base_price: z.number().nullable().default(null),
  minimum_fee: z.number().nullable().default(null),
  items: z
    .array(
      z.object({
        id: z.coerce.number().optional(),
        fraction_code: z.string(),
        price: z
          .number()
          .min(0, { message: "Price must be greater than 0" })
          .max(999999, { message: "Price must be less than 999999" })
          .default(0),
      })
    )
    .default([]),
});

export type FormPriceList = z.infer<typeof reportSetPriceListFormSchema>;

const reportSetFormSchema = z
  .object({
    id: z.number(),
    name: z
      .string()
      .min(1, { message: "Name is required" })
      .max(64, { message: "Name must be less than 64 characters" })
      .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field"),
    mode: z.enum([
      ReportSetMode.ON_PLATAFORM,
      ReportSetMode.BY_EXCEL,
      ReportSetMode.NO_REPORTING,
      ReportSetMode.SALES_PACKAGING,
    ]),
    sheet_file_id: z.string().nullable().default(null),
    sheet_file_description: z.string().nullable().default(null),
    fractions: z.array(fractionSchema).default([]),
    columns: z.array(columnSchema).default([]),
    price_lists: z
      .array(reportSetPriceListFormSchema, { message: "Add at least one price list" })
      .min(1, { message: "Add at least one price list" }),
  })
  .superRefine((data, ctx) => {
    if (data.mode === ReportSetMode.BY_EXCEL) {
      if (!data.sheet_file_id) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Sheet file is required",
          path: ["sheet_file_id"],
        });
      }

      if (data.sheet_file_description && !SPECIAL_CHARS_REGEX.test(data.sheet_file_description)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Sheet file description can only contain letters, numbers, spaces and question marks",
          path: ["sheet_file_description"],
        });
      }
    }

    if (data.mode === ReportSetMode.ON_PLATAFORM) {
      if (!data.fractions || !data.fractions.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Add at least one fraction",
          path: ["fractions"],
        });
      }
      if (!data.columns || !data.columns.length) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Add at least one column",
          path: ["columns"],
        });
      }

      const firstLevelFractions = data.fractions || [];

      firstLevelFractions.forEach((fraction) => {
        const isMissingSecondLevelFractions = fraction.has_second_level && !fraction.children.length;

        const isMissingThirdLevelFractions =
          fraction.has_third_level && fraction.children.some((child) => !child.children.length);

        if (isMissingSecondLevelFractions && isMissingThirdLevelFractions) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Second and third level fractions are required",
            path: ["fractions", fraction.order - 1],
          });
        }

        if (isMissingSecondLevelFractions && !isMissingThirdLevelFractions) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Second level fractions are required",
            path: ["fractions", fraction.order - 1],
          });
        }

        if (!isMissingSecondLevelFractions && isMissingThirdLevelFractions) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Third level fractions are required",
            path: ["fractions", fraction.order - 1],
          });
        }
      });
    }

    if (data.price_lists.length) {
      const currentYear = new Date().getFullYear();
      const currentYearPriceList = data.price_lists.find((priceList) => priceList.license_year === currentYear);

      if (!currentYearPriceList) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["price_lists"],
          message: `You must add a price list for the current year (${currentYear})`,
        });
      }
    }
  });

export type ReportSetFormData = z.infer<typeof reportSetFormSchema>;

interface ReportSetFormProviderProps {
  reportSet: FullReportSet;
  children: React.ReactNode;
}

export function ReportSetFormProvider({ reportSet, children }: ReportSetFormProviderProps) {
  const { country } = useServiceSetup();

  const defaultValues = {
    id: reportSet?.id,
    name: reportSet?.name || reportSet?.packaging_service?.name,
    mode: reportSet.mode,
    sheet_file_id: reportSet.sheet_file_id,
    sheet_file_description: reportSet.sheet_file_description,
    fractions: (reportSet?.fractions || []) as unknown as FormFraction[],
    columns: (reportSet?.columns || []) as unknown as FormColumn[],
    price_lists: (reportSet?.price_lists || []) as unknown as FormPriceList[],
  };

  const methods = useForm({
    resolver: zodResolver(reportSetFormSchema),
    defaultValues,
  });

  const { mutate: saveReportSet } = useMutation({
    mutationKey: ["update-report-set", reportSet.id],
    mutationFn: (data: UpdateReportSet) => updateReportSet(reportSet.id, data),
  });

  async function handleFormSubmit(data: ReportSetFormData) {
    const fractions = (data.fractions || []).reduce(
      (total, { children, ...fraction }) => {
        // Level 1
        total.push(fraction);

        // Level 2
        if (!!children.length) {
          children.forEach(({ children: secondLevelFractionChildren, ...secondLevelFraction }) => {
            total.push(secondLevelFraction);

            // Level 3
            if (!!secondLevelFractionChildren.length) {
              secondLevelFractionChildren.forEach((thirdLevelFraction) => {
                total.push(thirdLevelFraction);
              });
            }
          });
        }

        return total;
      },
      [] as Required<UpdateReportSet>["fractions"]
    );

    const columns = (data.columns || []).reduce(
      (total, { children, ...column }) => {
        // Level 1
        total.push(column);
        // Level 2
        if (children.length) {
          children.forEach((secondLevelColumn) => {
            total.push({
              ...secondLevelColumn,
              unit_type: column.unit_type,
            });
          });
        }

        return total;
      },
      [] as Required<UpdateReportSet>["columns"]
    );

    const priceLists = data.price_lists.map((priceList) => ({
      ...priceList,
      report_set_id: reportSet.id,
    }));

    saveReportSet(
      {
        name: data.name,
        fractions,
        columns,
        price_lists: priceLists.map((item) => {
          return {
            ...item,
            start_date: format(item.start_date, "yyyy-MM-dd"),
            end_date: DEFAULT_END_DATE,
          };
        }),
        sheet_file_id: data.sheet_file_id,
        sheet_file_description: data.sheet_file_description,
      },
      {
        onSuccess: () => {
          enqueueSnackbar("Report set updated successfully", { variant: "success" });
          methods.reset(data);
          queryClient.invalidateQueries({ queryKey: ["service-setup-report-set", country.code, reportSet.id] });
          queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
        },
      }
    );
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(handleFormSubmit)}>{children}</form>
    </FormProvider>
  );
}
