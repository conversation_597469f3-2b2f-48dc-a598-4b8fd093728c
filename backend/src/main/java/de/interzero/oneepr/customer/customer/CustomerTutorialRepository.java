package de.interzero.oneepr.customer.customer;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.entity.CustomerTutorial;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerTutorialRepository extends JpaRepository<CustomerTutorial, Integer> {

    List<CustomerTutorial> findByCustomerIdAndServiceTypeAndDeletedAtIsNull(Integer customerId,
                                                                            Contract.Type serviceType);

    Optional<CustomerTutorial> findByCustomerIdAndServiceType(Integer customerId,
                                                              Contract.Type serviceType);
}
