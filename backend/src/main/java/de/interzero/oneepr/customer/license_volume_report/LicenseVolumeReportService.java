package de.interzero.oneepr.customer.license_volume_report;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.decline.Decline;
import de.interzero.oneepr.customer.decline.DeclineRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingServiceRepository;
import de.interzero.oneepr.customer.license_volume_report.dto.CreateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.DeclineLicenseVolumeReportDto;
import de.interzero.oneepr.customer.license_volume_report.dto.UpdateLicenseVolumeReportDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.reason.DeclineReason;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * License Volume Report Service for managing license volume report operations.
 * Converted from TypeScript license-volume-report.service.ts
 * Maintains exact same structure, variable names, and function names.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicenseVolumeReportService {

    private final LicenseVolumeReportRepository licenseVolumeReportRepository;

    private final LicensePackagingServiceRepository licensePackagingServiceRepository;

    private final DeclineRepository declineRepository;

    private final ReasonRepository reasonRepository;

    /**
     * Create a new license volume report
     * Equivalent to TypeScript: create(createLicenseVolumeReportDto: CreateLicenseVolumeReportDto)
     *
     * @param createLicenseVolumeReportDto The DTO containing the data to create the report
     * @return The created LicenseVolumeReport
     */
    public LicenseVolumeReport create(CreateLicenseVolumeReportDto createLicenseVolumeReportDto) {
        // Find the packaging service to validate it exists
        Optional<LicensePackagingService> packagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                createLicenseVolumeReportDto.getLicensePackagingServiceId());

        if (packagingServiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
        }

        // Create new license volume report - maintaining exact same logic as TypeScript
        LicenseVolumeReport licenseVolumeReport = new LicenseVolumeReport();
        licenseVolumeReport.setPackagingService(packagingServiceOpt.get());
        licenseVolumeReport.setStatus(createLicenseVolumeReportDto.getStatus());
        licenseVolumeReport.setYear(createLicenseVolumeReportDto.getYear());
        licenseVolumeReport.setInterval(createLicenseVolumeReportDto.getInterval());
        licenseVolumeReport.setReportTable(createLicenseVolumeReportDto.getReportTable());
        licenseVolumeReport.setCreatedAt(Instant.now());
        licenseVolumeReport.setUpdatedAt(Instant.now());

        return licenseVolumeReportRepository.save(licenseVolumeReport);
    }

    /**
     * Find all license volume reports
     * Equivalent to TypeScript: findAll()
     *
     * @return List of all non-deleted LicenseVolumeReport entities
     */
    public List<LicenseVolumeReport> findAll() {
        return licenseVolumeReportRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Find one license volume report by ID with user permission validation
     * Equivalent to TypeScript: async findOne(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license volume report
     * @param user The authenticated user
     * @return Optional containing the LicenseVolumeReport if found and user has permission
     */
    public LicenseVolumeReport findOne(Integer id,
                                       AuthenticatedUser user) {
        // Validate user permission first - maintaining exact same logic as TypeScript
        validatingUserPermissionVolumeReport(id, user);

        // Return the license volume report - maintaining exact same logic as TypeScript
        return licenseVolumeReportRepository.findById(id).orElse(null);
    }

    /**
     * Update a license volume report
     * Equivalent to TypeScript: async update(id: number, updateLicenseVolumeReportDto: UpdateLicenseVolumeReportDto, user: AuthenticatedUser)
     *
     * @param id                           The ID of the license volume report to update
     * @param updateLicenseVolumeReportDto The DTO containing the update data
     * @param user                         The authenticated user
     * @return The updated LicenseVolumeReport
     */
    @Transactional
    public LicenseVolumeReport update(Integer id,
                                      UpdateLicenseVolumeReportDto updateLicenseVolumeReportDto,
                                      AuthenticatedUser user) {
        // Validate user permission first - maintaining exact same logic as TypeScript
        validatingUserPermissionVolumeReport(id, user);

        // Find the existing license volume report
        Optional<LicenseVolumeReport> licenseVolumeReportOpt = licenseVolumeReportRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licenseVolumeReportOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Volume Report not found");
        }

        LicenseVolumeReport licenseVolumeReport = licenseVolumeReportOpt.get();

        if (updateLicenseVolumeReportDto.getLicensePackagingServiceId() != null) {
            Optional<LicensePackagingService> packagingServiceOpt = licensePackagingServiceRepository.findByIdAndDeletedAtIsNull(
                    updateLicenseVolumeReportDto.getLicensePackagingServiceId());
            if (packagingServiceOpt.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Packaging Service not found");
            }
            licenseVolumeReport.setPackagingService(packagingServiceOpt.get());
        }
        // Update fields if provided - maintaining exact same logic as TypeScript
        if (updateLicenseVolumeReportDto.getStatus() != null) {
            licenseVolumeReport.setStatus(updateLicenseVolumeReportDto.getStatus());
        }
        if (updateLicenseVolumeReportDto.getYear() != null) {
            licenseVolumeReport.setYear(updateLicenseVolumeReportDto.getYear());
        }
        if (updateLicenseVolumeReportDto.getInterval() != null) {
            licenseVolumeReport.setInterval(updateLicenseVolumeReportDto.getInterval());
        }
        if (updateLicenseVolumeReportDto.getReportTable() != null) {
            licenseVolumeReport.setReportTable(updateLicenseVolumeReportDto.getReportTable());
        }

        licenseVolumeReport.setUpdatedAt(Instant.now());

        return licenseVolumeReportRepository.save(licenseVolumeReport);
    }

    /**
     * Remove (soft delete) a license volume report
     * Equivalent to TypeScript: async remove(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license volume report to remove
     * @param user The authenticated user
     * @return The soft-deleted LicenseVolumeReport
     */
    @Transactional
    public LicenseVolumeReport remove(Integer id,
                                      AuthenticatedUser user) {
        // Validate user permission first - maintaining exact same logic as TypeScript
        validatingUserPermissionVolumeReport(id, user);

        // Find the existing license volume report
        Optional<LicenseVolumeReport> licenseVolumeReportOpt = licenseVolumeReportRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licenseVolumeReportOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Volume Report not found");
        }

        LicenseVolumeReport licenseVolumeReport = licenseVolumeReportOpt.get();

        // Soft delete - maintaining exact same logic as TypeScript
        licenseVolumeReport.setDeletedAt(LocalDate.now());
        licenseVolumeReport.setUpdatedAt(Instant.now());

        return licenseVolumeReportRepository.save(licenseVolumeReport);
    }

    /**
     * Decline a license volume report with reasons
     * Equivalent to TypeScript: async decline(id: number, data: DeclineLicenseVolumeReportDto, user: AuthenticatedUser)
     *
     * @param id   The ID of the license volume report to decline
     * @param data The DTO containing decline reasons and title
     * @param user The authenticated user
     * @return The declined LicenseVolumeReport
     */
    @Transactional
    public LicenseVolumeReport decline(Integer id,
                                       DeclineLicenseVolumeReportDto data,
                                       AuthenticatedUser user) {
        // Validate ID - maintaining exact same logic as TypeScript
        if (id == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Volume Report ID is invalid");
        }

        // Validate reason IDs - maintaining exact same logic as TypeScript
        if (data.getReasonIds() == null || data.getReasonIds().isEmpty()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "At least one reason ID is required");
        }

        // Find the license volume report - maintaining exact same logic as TypeScript
        Optional<LicenseVolumeReport> licenseVolumeReportOpt = licenseVolumeReportRepository.findByIdAndDeletedAtIsNull(
                id);

        if (licenseVolumeReportOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Volume Report not found");
        }

        LicenseVolumeReport licenseVolumeReport = licenseVolumeReportOpt.get();

        // Validate user permission - maintaining exact same logic as TypeScript
        validatingUserPermissionVolumeReport(id, user);

        // Validate that all reason IDs exist - maintaining exact same logic as TypeScript
        List<Reason> reasons = reasonRepository.findAllById(data.getReasonIds());
        if (reasons.size() != data.getReasonIds().size()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "One or more reason IDs are invalid");
        }

        // Create decline record - maintaining exact same logic as TypeScript
        Decline decline = new Decline();
        decline.setTitle(data.getTitle());
        decline.setLicenseVolumeReport(licenseVolumeReport);
        decline.setCreatedAt(Instant.now());
        decline.setUpdatedAt(Instant.now());

        decline = declineRepository.save(decline);

        // Create decline reasons - maintaining exact same logic as TypeScript
        for (Reason reason : reasons) {
            DeclineReason declineReason = new DeclineReason();
            declineReason.setDecline(decline);
            declineReason.setReason(reason);
            declineReason.setCreatedAt(Instant.now());
            declineReason.setUpdatedAt(Instant.now());
            decline.addDeclineReason(declineReason);
        }

        // Update license volume report status to DECLINED - maintaining exact same logic as TypeScript
        licenseVolumeReport.setStatus(LicenseVolumeReport.Status.DECLINED);
        licenseVolumeReport.setUpdatedAt(Instant.now());

        return licenseVolumeReportRepository.save(licenseVolumeReport);
    }

    /**
     * Validate user permission for accessing a license volume report
     * Equivalent to TypeScript: async validatingUserPermissionVolumeReport(id: number, user: AuthenticatedUser)
     *
     * @param id   The ID of the license volume report
     * @param user The authenticated user
     * @throws ResponseStatusException if user doesn't have permission
     */
    private void validatingUserPermissionVolumeReport(Integer id,
                                                      AuthenticatedUser user) {

        // For customer role, validate ownership - maintaining exact same logic as TypeScript
        Optional<LicenseVolumeReport> licenseVolumeReportOpt = licenseVolumeReportRepository.findWithPermissionsById(id);

        LicenseVolumeReport report = licenseVolumeReportOpt.orElseThrow(() -> new ResponseStatusException(
                HttpStatus.NOT_FOUND,
                                                                                                          "License Volume Report not found"));

        LicensePackagingService licensePackagingService = report.getPackagingService();
        if (licensePackagingService == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Packaging service not found");
        }

        License license = licensePackagingService.getLicense();
        if (license == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        Contract contract = license.getContract();
        if (contract == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract not found");
        }

        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(Integer.valueOf(user.getId()))) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this volume report");
        }
    }
}
