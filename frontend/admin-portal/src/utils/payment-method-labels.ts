interface PaymentMethodLabels {
  [key: string]: string;
}

const PAYMENT_METHODS_LABELS: PaymentMethodLabels = {
  CREDIT_CARD: "Credit Card",
  BANK_TRANSFER: "Bank Transfer",
  PAYPAL: "PayPal",
  APPLE_PAY: "Apple Pay",
  KLARNA: "Klarna",
  EPS: "EPS",
  IDEAL: "iDEAL",
};

export function formatPaymentMethodLabel(
  method: { label: string; percentage: number },
  includePercentage = true
): string {
  if (!includePercentage) {
    return PAYMENT_METHODS_LABELS[method.label] || method.label;
  }
  return `${PAYMENT_METHODS_LABELS[method.label] || method.label}; ${method.percentage}%`;
}
