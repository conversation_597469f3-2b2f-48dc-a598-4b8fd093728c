"use client";

import { CountrySelect } from "@/components/common/country-select";
import { Divider } from "@/components/common/divider";
import { PhoneInput } from "@/components/common/phone-input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Check } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Controller, useFormContext } from "react-hook-form";
import { CreatePartnerParams } from "..";
import { partnerRegistrationFormSchemaBase } from "../partner-registration-form-schema";

const today = new Date().toISOString().split("T")[0];

export function CompanyInformation() {
  const form = useFormContext<CreatePartnerParams>();

  return (
    <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
      <p className="text-2xl font-bold text-primary">Company Information</p>
      <Controller
        control={form.control}
        name="step_1.companyName"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Company Name"
            placeholder="Enter company name"
            variant={error ? "error" : "default"}
            rightIcon={
              partnerRegistrationFormSchemaBase.shape.step_1.shape.companyName.safeParse(field.value).success &&
              field.value && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            errorMessage={error?.message}
          />
        )}
      />
      <Controller
        control={form.control}
        name="step_1.industrySector"
        render={({ field, fieldState: { error } }) => (
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <SelectGroup>
              <SelectLabel className="text-primary text-base font-normal pl-0">Industry Sector</SelectLabel>
              <SelectTrigger invalid={!!error}>
                <SelectValue placeholder="Select industry sector" />
              </SelectTrigger>
            </SelectGroup>
            <SelectContent defaultValue="AHK / IHK">
              <SelectItem value="AHK / IHK">AHK / IHK</SelectItem>
              <SelectItem value="Association">Association</SelectItem>
              <SelectItem value="E-Commerce">E-Commerce</SelectItem>
              <SelectItem value="Fulfilment">Fulfilment</SelectItem>
              <SelectItem value="Logistics">Logistics</SelectItem>
              <SelectItem value="Marketplaces">Marketplaces</SelectItem>
              <SelectItem value="Consulting">Consulting</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
            {error && <p className="text-error text-sm block -mt-2">{error.message}</p>}
          </Select>
        )}
      />
      <Controller
        control={form.control}
        name="step_1.startingDateOfCooperation"
        render={({ field, fieldState: { error } }) => (
          <div className="w-full max-w-80">
            <Input
              {...field}
              label="Starting Date of Cooperation"
              type="date"
              placeholder="Select date"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
              max={today}
            />
          </div>
        )}
      />
      <Controller
        control={form.control}
        name="step_1.partnerOfferWebsite"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Partner Offer Website"
            placeholder="Enter website URL"
            variant={error ? "error" : "default"}
            errorMessage={error?.message}
            rightIcon={
              partnerRegistrationFormSchemaBase.shape.step_1.shape.partnerOfferWebsite.safeParse(field.value).success &&
              field.value && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
          />
        )}
      />
      <Controller
        control={form.control}
        name="step_1.description"
        render={({ field, fieldState: { error } }) => (
          <Textarea
            {...field}
            label="Description"
            placeholder="Enter description"
            className="h-20"
            errorMessage={error?.message}
          />
        )}
      />
      <Controller
        control={form.control}
        name="step_1.directorOrCeoName"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Director or CEO Name"
            placeholder="Enter name"
            variant={error ? "error" : "default"}
            errorMessage={error?.message}
            rightIcon={
              partnerRegistrationFormSchemaBase.shape.step_1.shape.directorOrCeoName.safeParse(field.value).success &&
              field.value && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
          />
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <Controller
          control={form.control}
          name="step_1.country"
          render={({ field, fieldState: { error } }) => (
            <div className="flex flex-col gap-2">
              <label htmlFor="country" className="text-primary">
                Country
              </label>
              <CountrySelect
                value={field.value}
                onValueChange={field.onChange}
                placeholder="Select country"
                errorMessage={error?.message}
              />
            </div>
          )}
        />
        {/* <Controller
          control={form.control}
          name="step_1.federalState"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="Federal State"
              placeholder="Enter federal state"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
            />
          )}
        /> */}
        <Controller
          control={form.control}
          name="step_1.city"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="City"
              placeholder="Enter city"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
            />
          )}
        />
        <Controller
          control={form.control}
          name="step_1.zipCode"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="Zip Code"
              placeholder="Enter zip code"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
            />
          )}
        />
        <Controller
          control={form.control}
          name="step_1.streetAndNumber"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="Street and Number"
              placeholder="Enter street and number"
              variant={error ? "error" : "default"}
              errorMessage={error?.message}
            />
          )}
        />
        <Controller
          control={form.control}
          name="step_1.additionalAddressLine"
          render={({ field }) => (
            <Input {...field} label="Additional Address Line" placeholder="Enter additional address line" />
          )}
        />
      </div>
      <Divider className="my-4" initialMarginDisabled />
      <p className="text-2xl font-bold text-tonal-dark-cream-30">Contact</p>
      <Controller
        control={form.control}
        name="step_1.contact.name"
        render={({ field, fieldState: { error } }) => (
          <Input
            {...field}
            label="Contact Name"
            placeholder="Enter contact name"
            variant={error ? "error" : "default"}
            rightIcon={
              partnerRegistrationFormSchemaBase.shape.step_1.shape.contact.shape.name.safeParse(field.value).success &&
              field.value && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            errorMessage={error?.message}
          />
        )}
      />
      <div className="grid grid-cols-2 gap-4">
        <Controller
          control={form.control}
          name="step_1.contact.email"
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label="Email"
              placeholder="Enter email"
              variant={error ? "error" : "default"}
              rightIcon={
                partnerRegistrationFormSchemaBase.shape.step_1.shape.contact.shape.email.safeParse(field.value)
                  .success &&
                field.value && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={error?.message}
            />
          )}
        />
        <Controller
          control={form.control}
          name="step_1.contact.phone"
          render={({ field, fieldState: { error } }) => (
            <div className="flex flex-col gap-2">
              <label htmlFor="phone" className="text-primary">
                Phone
              </label>
              <PhoneInput
                id="phone"
                name="phone"
                placeholder="Enter phone"
                valueSetter={field.onChange}
                errorSetter={field.onBlur}
                defaultValue={form.watch("step_1.contact.phone")}
                isError={!!error}
              />
            </div>
          )}
        />
      </div>
    </div>
  );
}
