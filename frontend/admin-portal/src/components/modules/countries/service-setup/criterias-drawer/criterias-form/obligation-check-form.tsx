import { useServiceSetup } from "@/hooks/use-service-setup";
import {
  getServiceSetupPackagingServices,
  getObligationCheckSectionsByCountryCode,
  createObligationCheckSection,
  deleteObligationCheckSection,
} from "@/lib/api/service-setups";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { FormProvider, useForm } from "react-hook-form";
import { CriteriaType } from "@/types/service-setup/criteria";
import { useEffect, useState } from "react";
import { createCriteria, updateCriteria, getCriteriasBySectionId } from "@/lib/api/criterias";
import { queryClient } from "@/lib/react-query";
import { z } from "zod";
import { ObligationQuestionCard, ObligationQuestionCardSkeleton } from "./obligation-question-card";
import { enqueueSnackbar } from "notistack";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { CHARACTER_COUNTER_DEFAULTS } from "@/components/ui/character-counter";

import { Add } from "@interzero/oneepr-react-ui/Icon";
import { X } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { invalidateSubQueries } from ".";

// Define obligation question structure
export interface ObligationQuestion {
  id?: number;
  title: string;
  help_text?: string; // Made optional to match Zod schema
  section_id: string; // Track which section this question belongs to
  yes_action: "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation";
  yes_target_question_id?: number;
  yes_obligation_result?: "OBLIGED" | "NOT_OBLIGED";
  yes_services?: string[];
  no_action: "show_conditional" | "indicates_obligation" | "does_not_indicate_obligation";
  no_target_question_id?: number;
  no_obligation_result?: "OBLIGED" | "NOT_OBLIGED";
  no_services?: string[];
}

export const TITLE_MAX_LENGTH = CHARACTER_COUNTER_DEFAULTS.MAX_TITLE_LENGTH || 150;
export const HELP_TEXT_MAX_LENGTH = CHARACTER_COUNTER_DEFAULTS.MAX_HELP_TEXT_LENGTH || 500;

// Define Zod schema for ObligationQuestion with detailed validation
const obligationQuestionSchema = z
  .object({
    id: z.number().optional(),
    title: z
      .string()
      .min(1, "Title is required")
      .max(TITLE_MAX_LENGTH, `Title must be ${TITLE_MAX_LENGTH} characters or less`)
      .refine((val) => SPECIAL_CHARS_REGEX.test(val), {
        message: "Title can only contain letters, numbers, spaces and question marks",
      }),
    help_text: z
      .string()
      .max(HELP_TEXT_MAX_LENGTH, `Help text must be ${HELP_TEXT_MAX_LENGTH} characters or less`)
      .refine((val) => !val || SPECIAL_CHARS_REGEX.test(val), {
        message: "Help text can only contain letters, numbers, spaces and question marks",
      })
      .optional(),
    section_id: z.string(),
    yes_action: z.enum(["show_conditional", "indicates_obligation", "does_not_indicate_obligation"]),
    yes_target_question_id: z.number().optional(),
    yes_obligation_result: z.enum(["OBLIGED", "NOT_OBLIGED"]).optional(),
    yes_services: z.array(z.string()).optional(),
    no_action: z.enum(["show_conditional", "indicates_obligation", "does_not_indicate_obligation"]),
    no_target_question_id: z.number().optional(),
    no_obligation_result: z.enum(["OBLIGED", "NOT_OBLIGED"]).optional(),
    no_services: z.array(z.string()).optional(),
  })
  .superRefine((data, ctx) => {
    // Validate yes action dependencies
    if (data.yes_action === "show_conditional" && !data.yes_target_question_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Target question is required when using conditional action",
        path: ["yes_target_question_id"],
      });
    }

    // Validate no action dependencies
    if (data.no_action === "show_conditional" && !data.no_target_question_id) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Target question is required when using conditional action",
        path: ["no_target_question_id"],
      });
    }

    // Validate services are selected for obligation actions
    if (data.yes_action === "indicates_obligation" && (!data.yes_services || data.yes_services.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "At least one service must be selected when indicating obligation",
        path: ["yes_services"],
      });
    }

    if (data.no_action === "indicates_obligation" && (!data.no_services || data.no_services.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "At least one service must be selected when indicating obligation",
        path: ["no_services"],
      });
    }
  });

// Schema for obligation check form with sections
export const obligationCheckFormSchema = z
  .object({
    sections: z.array(
      z.object({
        id: z.string(),
        name: z.string(),
        questions: z.array(obligationQuestionSchema),
      })
    ),
  })
  .superRefine((data, ctx) => {
    // Validate that conditional questions reference questions in the same section
    data.sections.forEach((section, sectionIndex) => {
      section.questions.forEach((question: ObligationQuestion, questionIndex: number) => {
        // Check yes_action conditional references
        if (question.yes_action === "show_conditional" && question.yes_target_question_id) {
          const targetQuestionExists = section.questions.some(
            (q: ObligationQuestion) => q.id === question.yes_target_question_id
          );
          if (!targetQuestionExists) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Conditional questions must reference questions within the same section",
              path: ["sections", sectionIndex, "questions", questionIndex, "yes_target_question_id"],
            });
          }
        }

        // Check no_action conditional references
        if (question.no_action === "show_conditional" && question.no_target_question_id) {
          const targetQuestionExists = section.questions.some(
            (q: ObligationQuestion) => q.id === question.no_target_question_id
          );
          if (!targetQuestionExists) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Conditional questions must reference questions within the same section",
              path: ["sections", sectionIndex, "questions", questionIndex, "no_target_question_id"],
            });
          }
        }
      });
    });
  });

export type ObligationCheckFormData = z.infer<typeof obligationCheckFormSchema>;

interface ObligationCheckFormProps {
  type: CriteriaType;
  packagingServiceId?: number;
  requiredInformationId?: number;
  onFormChange?: (hasChanges: boolean) => void;
}

export function ObligationCheckForm({
  type,
  packagingServiceId,
  requiredInformationId,
  onFormChange,
}: ObligationCheckFormProps) {
  const { country } = useServiceSetup();
  const [activeSection, setActiveSection] = useState("section-1");

  // legacy queryKey not needed with sections-based fetching

  const { data: sections } = useQuery({
    queryKey: ["obligation-check-sections", country.code],
    queryFn: () => getObligationCheckSectionsByCountryCode(country.code),
  });

  // Helper to parse UI section id to backend id
  const parseSectionNumericId = (uiSectionId: string) => {
    const match = uiSectionId.match(/section-(\d+)/);
    return match ? Number(match[1]) : undefined;
  };

  // Get all packaging services for the dropdown
  const { data: allPackagingServices } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const methods = useForm<ObligationCheckFormData>({
    resolver: zodResolver(obligationCheckFormSchema),
    defaultValues: {
      sections: [
        {
          id: "section-1",
          name: "Section 1",
          questions: [],
        },
      ],
    },
  });

  useEffect(() => {
    (async () => {
      if (!sections) return;
      // Fetch criteria per section
      if (sections.length === 0) {
        // Auto-create a default section when none exist
        try {
          const created = await createObligationCheckSection({
            title: "Section 1",
            display_order: 1,
            country_code: country.code,
          });
          // Initialize form with the newly created section
          const initial = [
            {
              id: `section-${created.id}`,
              name: created.title || "Section 1",
              questions: [],
            },
          ];
          methods.reset({ sections: initial });
          setActiveSection(initial[0].id);
          queryClient.invalidateQueries({ queryKey: ["obligation-check-sections", country.code] });
          return;
        } catch {
          // Fallback to local state if creation fails
          methods.reset({
            sections: [{ id: "section-temp", name: "Section 1", questions: [] }],
          });
          setActiveSection("section-temp");
          return;
        }
      }

      const sectionsWithQuestions = await Promise.all(
        sections.map(async (sec, index) => {
          const criterias = await getCriteriasBySectionId(sec.id);
          const transformedQuestions: ObligationQuestion[] = (criterias || []).map((criteria) => {
            const options = Array.isArray(criteria.options) ? criteria.options : [];
            const yesOption = options.find((opt) => opt.option_value === "YES");
            const noOption = options.find((opt) => opt.option_value === "NO");

            let yes_action: ObligationQuestion["yes_action"] = "does_not_indicate_obligation";
            let yes_target_question_id: number | undefined;
            let yes_obligation_result: "OBLIGED" | "NOT_OBLIGED" | undefined;
            let yes_services: string[] = [];

            if (yesOption) {
              if (yesOption.value === "CONDITIONAL" && yesOption.conditional_criteria_id) {
                yes_action = "show_conditional";
                yes_target_question_id = yesOption.conditional_criteria_id;
              } else if (yesOption.value === "OBLIGED" || yesOption.value === "NOT_OBLIGED") {
                // Map NOT_OBLIGED to does_not_indicate_obligation per UI simplification
                if (yesOption.value === "NOT_OBLIGED") {
                  yes_action = "does_not_indicate_obligation";
                } else {
                  yes_action = "indicates_obligation";
                  yes_obligation_result = "OBLIGED";
                }
                // Handle both packaging_services (array of objects) and packaging_service_ids (array of IDs) for backwards compatibility
                if (yesOption?.packaging_services && yesOption.packaging_services.length > 0) {
                  yes_services = yesOption.packaging_services.map((service) => service.name);
                } else if (yesOption?.packaging_service_ids && allPackagingServices) {
                  yes_services = allPackagingServices
                    .filter((service) => yesOption?.packaging_service_ids?.includes(service.id))
                    .map((service) => service.name);
                }
              }
            }

            let no_action: ObligationQuestion["no_action"] = "does_not_indicate_obligation";
            let no_target_question_id: number | undefined;
            let no_obligation_result: "OBLIGED" | "NOT_OBLIGED" | undefined;
            let no_services: string[] = [];

            if (noOption) {
              if (noOption.value === "CONDITIONAL" && noOption.conditional_criteria_id) {
                no_action = "show_conditional";
                no_target_question_id = noOption.conditional_criteria_id;
              } else if (noOption.value === "OBLIGED" || noOption.value === "NOT_OBLIGED") {
                if (noOption.value === "NOT_OBLIGED") {
                  no_action = "does_not_indicate_obligation";
                } else {
                  no_action = "indicates_obligation";
                  no_obligation_result = "OBLIGED";
                }
                // Handle both packaging_services (array of objects) and packaging_service_ids (array of IDs) for backwards compatibility
                if (noOption?.packaging_services && noOption.packaging_services.length > 0) {
                  no_services = noOption.packaging_services.map((service) => service.name);
                } else if (noOption?.packaging_service_ids && allPackagingServices) {
                  no_services = allPackagingServices
                    .filter((service) => noOption?.packaging_service_ids?.includes(service.id))
                    .map((service) => service.name);
                }
              }
            }

            return {
              id: criteria.id,
              title: criteria.title || "",
              help_text: criteria.help_text || "",
              section_id: `section-${sec.id}`,
              yes_action,
              yes_target_question_id,
              yes_obligation_result,
              yes_services,
              no_action,
              no_target_question_id,
              no_obligation_result,
              no_services,
            };
          });
          return {
            id: `section-${sec.id}`,
            name: sec.title || `Section ${index + 1}`,
            questions: transformedQuestions,
          };
        })
      );

      methods.reset({ sections: sectionsWithQuestions });

      // Preserve current active tab if still present; otherwise fallback to first section
      setActiveSection((prev) => {
        const exists = sectionsWithQuestions.some((s) => s.id === prev);
        return exists ? prev : sectionsWithQuestions[0]?.id || "section-1";
      });
      // counter not used
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sections, allPackagingServices]);

  const watchedSections = methods.watch("sections");

  // Track form changes using isDirty like criterias form
  useEffect(() => {
    if (onFormChange) {
      onFormChange(methods.formState.isDirty);
    }
  }, [methods.formState.isDirty, onFormChange]);

  async function handleAddSection() {
    try {
      // Find the smallest missing positive display_order (1..n)
      const existingOrders = (sections || []).map((s) => s.display_order).filter((n) => typeof n === "number");
      let nextOrder = 1;
      if (existingOrders.length > 0) {
        const orderSet = new Set(existingOrders);
        while (orderSet.has(nextOrder)) nextOrder += 1;
      }
      const title = `Section ${nextOrder}`;
      const created = await createObligationCheckSection({
        title,
        display_order: nextOrder,
        country_code: country.code,
      });
      queryClient.invalidateQueries({ queryKey: ["obligation-check-sections", country.code] });
      setActiveSection(`section-${created.id}`);
    } catch {
      enqueueSnackbar("Failed to create section", { variant: "error" });
    }
  }

  async function handleRemoveSection(sectionId: string) {
    const numericId = parseSectionNumericId(sectionId);
    if (!numericId) return;
    try {
      await deleteObligationCheckSection(numericId);
      queryClient.invalidateQueries({ queryKey: ["obligation-check-sections", country.code] });
      const currentSections = methods.getValues("sections");
      const filteredSections = currentSections.filter((s) => s.id !== sectionId);
      methods.setValue("sections", filteredSections);
      if (activeSection === sectionId) {
        setActiveSection(filteredSections[0]?.id || "section-1");
      }
      enqueueSnackbar("Section removed successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to remove section", { variant: "error" });
    }
  }

  function handleAddQuestion(sectionId: string) {
    const currentSections = methods.getValues("sections");
    const sectionIndex = currentSections.findIndex((s) => s.id === sectionId);

    if (sectionIndex !== -1) {
      const newQuestion: ObligationQuestion = {
        title: "",
        help_text: "",
        section_id: sectionId,
        yes_action: "does_not_indicate_obligation",
        yes_obligation_result: "NOT_OBLIGED",
        yes_services: [],
        no_action: "does_not_indicate_obligation",
        no_obligation_result: "NOT_OBLIGED",
        no_services: [],
      };

      const updatedSections = [...currentSections];
      updatedSections[sectionIndex].questions.push(newQuestion);
      methods.setValue("sections", updatedSections);
    }
  }

  function handleRemoveQuestion(sectionId: string, questionIndex: number) {
    const currentSections = methods.getValues("sections");
    const sectionIndex = currentSections.findIndex((s) => s.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      updatedSections[sectionIndex].questions.splice(questionIndex, 1);
      methods.setValue("sections", updatedSections);
    }
  }

  function handleUpdateQuestion(sectionId: string, questionIndex: number, data: Partial<ObligationQuestion>) {
    const currentSections = methods.getValues("sections");
    const sectionIndex = currentSections.findIndex((s) => s.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      updatedSections[sectionIndex].questions[questionIndex] = {
        ...updatedSections[sectionIndex].questions[questionIndex],
        ...data,
      };

      methods.setValue("sections", updatedSections);
    }
  }

  async function handleFormSubmit(data: ObligationCheckFormData) {
    try {
      const allQuestions = data.sections.flatMap((section) => section.questions);

      if (!allQuestions.length) return;

      // Convert questions to criteria format for API
      const promises = allQuestions.map(async (question) => {
        // Build options based on the question's action configuration
        const options = [];

        // YES option
        if (question.yes_action === "show_conditional") {
          options.push({
            option_value: "YES",
            option_to_value: null,
            value: "CONDITIONAL",
            conditional_criteria_id: question.yes_target_question_id || null,
          });
        } else if (question.yes_action === "indicates_obligation") {
          const serviceIds = question.yes_services
            ? (allPackagingServices || [])
                .filter((service) => question.yes_services!.includes(service.name))
                .map((service) => service.id)
            : [];

          options.push({
            option_value: "YES",
            option_to_value: null,
            value: "OBLIGED",
            packaging_service_ids: serviceIds,
          });
        } else {
          // does_not_indicate_obligation
          options.push({
            option_value: "YES",
            option_to_value: null,
            value: "NOT_OBLIGED",
          });
        }

        // NO option
        if (question.no_action === "show_conditional") {
          options.push({
            option_value: "NO",
            option_to_value: null,
            value: "CONDITIONAL",
            conditional_criteria_id: question.no_target_question_id || null,
          });
        } else if (question.no_action === "indicates_obligation") {
          const serviceIds = question.no_services
            ? (allPackagingServices || [])
                .filter((service) => question.no_services!.includes(service.name))
                .map((service) => service.id)
            : [];

          options.push({
            option_value: "NO",
            option_to_value: null,
            value: "OBLIGED",
            packaging_service_ids: serviceIds,
          });
        } else {
          // does_not_indicate_obligation
          options.push({
            option_value: "NO",
            option_to_value: null,
            value: "NOT_OBLIGED",
          });
        }

        const criteriaData = {
          mode: "COMMITMENT" as const,
          type: "PACKAGING_SERVICE" as const,
          title: question.title,
          help_text: question.help_text || null,
          input_type: "YES_NO" as const,
          calculator_type: null,
          country_id: country.id,
          packaging_service_id: packagingServiceId || null,
          required_information_id: requiredInformationId || null,
          // new linkage to obligation check section
          obligation_check_section_id: parseSectionNumericId(question.section_id) || null,
          options,
        };

        if (!question.id) {
          return createCriteria(criteriaData);
        }
        return updateCriteria(question.id, criteriaData);
      });

      const responses = await Promise.allSettled(promises);

      // Update the form with the saved IDs
      let questionIndex = 0;
      const updatedSections = data.sections.map((section) => ({
        ...section,
        questions: section.questions.map((question) => {
          const response = responses[questionIndex++];
          if (response.status === "fulfilled") {
            return { ...question, id: response.value.id };
          }
          return question;
        }),
      }));

      methods.setValue("sections", updatedSections);

      // Reset form dirty state after successful save
      methods.reset({ sections: updatedSections });

      enqueueSnackbar("Form saved successfully", { variant: "success" });
      queryClient.invalidateQueries({ queryKey: ["criterias", country.code, type, packagingServiceId] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-packaging-services", country.code] });

      invalidateSubQueries(type, country.code, packagingServiceId, requiredInformationId);
    } catch {
      enqueueSnackbar("Error saving form. Please try again.", { variant: "error" });
    }
  }

  // Calculate continuous question numbering across all sections
  function getQuestionNumber(targetSectionId: string, questionIndex: number): number {
    let questionNumber = 1;

    for (const section of watchedSections) {
      if (section.id === targetSectionId) {
        return questionNumber + questionIndex;
      }
      questionNumber += section.questions.length;
    }

    return questionNumber + questionIndex;
  }

  if (!sections) {
    return (
      <div className="space-y-10">
        <ObligationQuestionCardSkeleton />
        <ObligationQuestionCardSkeleton />
      </div>
    );
  }

  return (
    <FormProvider {...methods}>
      <form className="space-y-10" onSubmit={methods.handleSubmit(handleFormSubmit)}>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <h3 className="text-primary text-xl">2. Obligation check</h3>
            <h2 className="text-primary text-3xl font-bold">Set up obligation check</h2>
          </div>
        </div>

        <div className="space-y-6">
          <Tabs value={activeSection} onValueChange={setActiveSection} className="w-full">
            <TabsList className="justify-start">
              {watchedSections.map((section) => (
                <div key={section.id} className="relative inline-flex items-center mr-3">
                  <TabsTrigger value={section.id} className="pr-10 bg-tonal-dark-blue-96">
                    {section.name}
                  </TabsTrigger>
                  {watchedSections.length > 1 && activeSection !== section.id && (
                    <Button
                      type="button"
                      aria-label={`Remove ${section.name}`}
                      variant="text"
                      color="dark-blue"
                      size="iconSmall"
                      className="absolute right-2 top-1/2 -translate-y-1/2 p-0 h-5 w-5"
                      onClick={() => handleRemoveSection(section.id)}
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="text"
                color="dark-blue"
                size="medium"
                leadingIcon={<Add />}
                onClick={handleAddSection}
                className="inline-flex items-center justify-center whitespace-nowrap rounded-2xl px-5 py-2 text-lg font-bold bg-tonal-dark-blue-96 text-primary"
              >
                Add a section
              </Button>
            </TabsList>

            {watchedSections.map((section, sectionIndex) => (
              <TabsContent key={section.id} value={section.id} className="space-y-6 mt-10">
                {section.questions.map((question, questionIndex) => {
                  const questionNumber = getQuestionNumber(section.id, questionIndex);

                  // Build list of ALL questions in the same section for conditional logic
                  // Current question will be shown but disabled in the dropdown
                  const availableQuestions = section.questions
                    .map((q, idx) => ({
                      id: q.id,
                      title: q.title,
                      displayNumber: getQuestionNumber(section.id, idx),
                      isCurrent: idx === questionIndex,
                    }))
                    // Show only previously created questions (have id) plus the current question (disabled)
                    .filter((q) => q.id !== undefined || q.isCurrent);

                  return (
                    <ObligationQuestionCard
                      key={`${section.id}-${questionIndex}`}
                      sectionId={section.id}
                      sectionIndex={sectionIndex}
                      questionIndex={questionIndex}
                      question={question}
                      questionNumber={questionNumber}
                      availableQuestions={availableQuestions}
                      availableServices={allPackagingServices || []}
                      packagingServiceId={packagingServiceId}
                      requiredInformationId={requiredInformationId}
                      onUpdate={handleUpdateQuestion}
                      onRemove={handleRemoveQuestion}
                    />
                  );
                })}

                {/* Add question button after the questions */}
                <div className="pt-4">
                  <Button
                    type="button"
                    variant="text"
                    color="light-blue"
                    size="medium"
                    leadingIcon={<Add />}
                    onClick={() => handleAddQuestion(section.id)}
                  >
                    Add question
                  </Button>

                  {section.questions.length === 0 && (
                    <div className="text-center py-8 text-tonal-dark-cream-40">
                      No questions in this section yet. Click &quot;Add question&quot; to get started.
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
        <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
        <div className="flex items-center justify-end">
          {watchedSections.some((section) => section.questions.length > 0) && (
            <Button
              variant="filled"
              color={!!Object.keys(methods.formState.errors).length ? "red" : "yellow"}
              size="medium"
              disabled={methods.formState.isSubmitting}
            >
              {methods.formState.isSubmitting ? "Saving..." : "Save obligation check"}
            </Button>
          )}
        </div>
      </form>
    </FormProvider>
  );
}
