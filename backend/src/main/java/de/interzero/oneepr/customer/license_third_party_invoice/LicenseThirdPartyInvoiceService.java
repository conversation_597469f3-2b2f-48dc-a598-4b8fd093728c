package de.interzero.oneepr.customer.license_third_party_invoice;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.file.FileRepository;
import de.interzero.oneepr.customer.http.CrmInterface;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.CreateLicenseThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.FindAllThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.license_third_party_invoice.dto.UpdateLicenseThirdPartyInvoiceDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service layer for managing Third Party License Invoices.
 * Mirrors the structure and logic of the original NestJS service.
 */
@Service
@RequiredArgsConstructor
public class LicenseThirdPartyInvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(LicenseThirdPartyInvoiceService.class);

    private final LicenseThirdPartyInvoiceRepository licenseThirdPartyInvoiceRepository;

    private final LicenseRepository licenseRepository;

    private final FileRepository fileRepository;

    private final CrmInterface crmInterface;


    /**
     * Finds all non-deleted third-party invoices, with filtering and ordering.
     * Includes associated files (non-deleted) and license (with its non-deleted files).
     *
     * @param data DTO containing filter criteria (license_id, from_date, to_date).
     * @return A list of {@link LicenseThirdPartyInvoice} objects.
     */
    @Transactional(readOnly = true)
    public List<LicenseThirdPartyInvoice> findAll(FindAllThirdPartyInvoiceDto data) {
        Instant fromDate = null;
        Instant toDate = null;
        try {
            if (data.getFromDate() != null && !data.getFromDate().isEmpty()) {
                fromDate = LocalDate.parse(data.getFromDate()).atStartOfDay().toInstant(ZoneOffset.UTC);
            }
            if (data.getToDate() != null && !data.getToDate().isEmpty()) {
                toDate = LocalDate.parse(data.getToDate()).atTime(23, 59, 59).toInstant(ZoneOffset.UTC);
            }
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid date format in query parameters.", e);
        }

        return licenseThirdPartyInvoiceRepository.findWithFilters(
                data.getLicenseId() != null ? data.getLicenseId() : null,
                fromDate,
                toDate);
    }

    /**
     * Finds a single third-party invoice by ID, ensuring it's not soft-deleted.
     * Includes associated files (non-deleted) and license.
     * Validates user permission before returning.
     *
     * @param id   The ID of the invoice.
     * @param user The authenticated user.
     * @return The {@link LicenseThirdPartyInvoice}.
     * @throws ResponseStatusException if ID is invalid, invoice not found, or permission denied.
     */
    @Transactional(readOnly = true)
    public LicenseThirdPartyInvoice findOne(Integer id,
                                            AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Third Party Invoice ID is invalid");
        }

        validatingUserPermissionThirdPartyInvoice(id, user);

        Optional<LicenseThirdPartyInvoice> invoiceOpt = licenseThirdPartyInvoiceRepository.findActiveByIdWithDetails(id);

        if (invoiceOpt.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License Third Party Invoice not found");
        }
        return invoiceOpt.get();
    }

    /**
     * Creates a new third-party license invoice, interacts with the CRM via CrmInterface,
     * updates the invoice with a CRM reference, and links a file if provided.
     * This entire operation is performed within a transaction.
     *
     * @param createDto DTO containing invoice creation data.
     * @return The created and updated {@link LicenseThirdPartyInvoice}.
     * @throws ResponseStatusException for various errors.
     */
    @Transactional
    public LicenseThirdPartyInvoice create(CreateLicenseThirdPartyInvoiceDto createDto) {
        LicenseThirdPartyInvoice invoiceToCreate = new LicenseThirdPartyInvoice();
        if (createDto.getLicenseId() != null) {
            License license = licenseRepository.findById(createDto.getLicenseId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "License not found with ID: " + createDto.getLicenseId()));
            invoiceToCreate.setLicense(license);
        }
        invoiceToCreate.setStatus(createDto.getStatus());
        invoiceToCreate.setTitle(createDto.getTitle());
        invoiceToCreate.setIssuer(createDto.getIssuer());
        try {
            invoiceToCreate.setIssuedAt(Instant.parse(createDto.getIssuedAt()));
            invoiceToCreate.setDueDate(Instant.parse(createDto.getDueDate()));
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid date format for issued_at or due_date",
                                              e);
        }
        invoiceToCreate.setPrice(createDto.getPrice());

        Instant now = Instant.now();
        invoiceToCreate.setCreatedAt(now);
        invoiceToCreate.setUpdatedAt(now);

        LicenseThirdPartyInvoice savedInvoice = licenseThirdPartyInvoiceRepository.save(invoiceToCreate);

        License licenseForCrm = savedInvoice.getLicense();
        if (licenseForCrm == null || licenseForCrm.getContract() == null || licenseForCrm.getContract()
                .getCustomer() == null) {
            LicenseThirdPartyInvoice fullyLoadedInvoice = licenseThirdPartyInvoiceRepository.findActiveByIdWithDetails(
                            savedInvoice.getId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Failed to reload created invoice for CRM call."));
            licenseForCrm = fullyLoadedInvoice.getLicense();

            if (licenseForCrm == null || licenseForCrm.getContract() == null || licenseForCrm.getContract()
                    .getCustomer() == null) {
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Required license/contract/customer data missing for CRM integration even after reload.");
            }
        }

        Long mondayItemId;
        try {
            String country = licenseForCrm.getCountryName();
            String customerIdForCrm = String.valueOf(licenseForCrm.getContract().getCustomer().getId());
            Instant timeline = savedInvoice.getDueDate();

            mondayItemId = crmInterface.thirdPartyInvoices(country, customerIdForCrm, timeline);
            logger.info("CRM interaction returned mondayItemId: {}", mondayItemId);

        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.SERVICE_UNAVAILABLE,
                                              "Error communicating with CRM: " + e.getMessage(),
                                              e);
        }

        if (mondayItemId != null) {
            savedInvoice.setThirdPartyInvoiceMondayRef(Math.toIntExact(mondayItemId));
            savedInvoice = licenseThirdPartyInvoiceRepository.save(savedInvoice);
        }

        if (createDto.getFileId() != null && !createDto.getFileId().isEmpty()) {
            File fileToUpdate = fileRepository.findById(createDto.getFileId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "File not found with ID: " + createDto.getFileId()));
            fileToUpdate.setThirdPartyInvoice(savedInvoice);
            fileRepository.save(fileToUpdate);
        }
        return savedInvoice;
    }


    /**
     * Updates an existing third-party invoice.
     *
     * @param id        The ID of the invoice to update.
     * @param updateDto DTO containing update data.
     * @param user      The authenticated user.
     * @return The updated {@link LicenseThirdPartyInvoice}.
     * @throws ResponseStatusException if ID invalid, invoice not found, or permission denied.
     * @ts-legacy Updating 'files' collection via a single 'file_id' in UpdateLicenseThirdPartyInvoiceDto is ambiguous with the current entity structure (List<File> files). Methods should not perform too many tasks
     */
    @Transactional
    public LicenseThirdPartyInvoice update(Integer id,
                                           UpdateLicenseThirdPartyInvoiceDto updateDto,
                                           AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Third Party Invoice ID is invalid");
        }

        validatingUserPermissionThirdPartyInvoice(id, user);

        LicenseThirdPartyInvoice invoice = licenseThirdPartyInvoiceRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Third Party Invoice not found"));

        boolean updated = false;
        if (updateDto.getTitle() != null) {
            invoice.setTitle(updateDto.getTitle());
            updated = true;
        }
        if (updateDto.getStatus() != null) {
            invoice.setStatus(updateDto.getStatus());
            updated = true;
        }
        if (updateDto.getPrice() != null) {
            invoice.setPrice(updateDto.getPrice());
            updated = true;
        }
        if (updateDto.getIssuer() != null) {
            invoice.setIssuer(updateDto.getIssuer());
            updated = true;
        }
        if (updateDto.getIssuedAt() != null && !updateDto.getIssuedAt().isEmpty()) {
            try {
                invoice.setIssuedAt(Instant.parse(updateDto.getIssuedAt()));
                updated = true;
            } catch (Exception e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid issued_at format", e);
            }
        }
        if (updateDto.getDueDate() != null && !updateDto.getDueDate().isEmpty()) {
            try {
                invoice.setDueDate(Instant.parse(updateDto.getDueDate()));
                updated = true;
            } catch (Exception e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid due_date format", e);
            }
        }
        //Updating 'files' collection via a single 'file_id' in UpdateLicenseThirdPartyInvoiceDto is ambiguous with the current entity structure (List<File> files).
        if (updateDto.getFileId() != null) {
            updated = true;

            // 1. Dissociate all currently linked files
            if (invoice.getFiles() != null && !invoice.getFiles().isEmpty()) {
                // Iterate over a copy if modifying the collection while iterating,
                // or manage by setting the inverse side to null and saving the File entities.
                for (File oldFile : List.copyOf(invoice.getFiles())) {
                    oldFile.setThirdPartyInvoice(null);
                    fileRepository.save(oldFile);
                }
                invoice.getFiles().clear(); // Clear the collection on the invoice side
            } else {
                invoice.setFiles(new ArrayList<>()); // Ensure the list is initialized
            }

            // 2. If a new file_id is provided (and not empty), associate the new file
            if (!updateDto.getFileId().isEmpty()) {
                File newFile = fileRepository.findById(updateDto.getFileId())
                        .orElseThrow(() -> new ResponseStatusException(
                                HttpStatus.NOT_FOUND,
                                "New file to associate not found with ID: " + updateDto.getFileId()));

                newFile.setThirdPartyInvoice(invoice);
                fileRepository.save(newFile);
                invoice.getFiles().add(newFile);
            }
            // If updateDto.getFileId() was an empty string, all files were dissociated, and no new file is added.
        }


        if (updated) {
            invoice.setUpdatedAt(Instant.now());
        }

        return licenseThirdPartyInvoiceRepository.save(invoice);
    }

    /**
     * Soft-deletes a third-party invoice.
     *
     * @param id   The ID of the invoice to delete.
     * @param user The authenticated user.
     * @return The soft-deleted {@link LicenseThirdPartyInvoice}.
     * @throws ResponseStatusException if ID invalid, invoice not found, or permission denied.
     */
    @Transactional
    public LicenseThirdPartyInvoice remove(Integer id,
                                           AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "License Third Party Invoice ID is invalid");
        }

        validatingUserPermissionThirdPartyInvoice(id, user);

        LicenseThirdPartyInvoice invoice = licenseThirdPartyInvoiceRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Third Party Invoice not found for deletion"));

        invoice.setDeletedAt(LocalDate.now());
        invoice.setUpdatedAt(Instant.now());

        return licenseThirdPartyInvoiceRepository.save(invoice);
    }

    /**
     * Validates user permission to access a specific third-party invoice.
     * Fetches invoice with deeply nested related data (license, contract, customer).
     *
     * @param id   The ID of the invoice.
     * @param user The authenticated user.
     * @throws ResponseStatusException if data not found or access forbidden.
     */
    public void validatingUserPermissionThirdPartyInvoice(Integer id,
                                                          AuthenticatedUser user) {
        LicenseThirdPartyInvoice invoice = licenseThirdPartyInvoiceRepository.findActiveByIdWithDetails(id)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.NOT_FOUND,
                        "License Third Party Invoice not found with ID: " + id));

        License license = invoice.getLicense();
        if (license == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License associated with invoice not found.");
        }
        if (license.getContract() == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Contract associated with license not found.");
        }
        Customer customer = license.getContract().getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer associated with contract not found.");
        }

        int authenticatedUserIdAsInt;
        try {
            authenticatedUserIdAsInt = Integer.parseInt(user.getId());
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Authenticated user ID format is invalid: " + user.getId(),
                                              e);
        }

        if (user.getRole() == Role.CUSTOMER && !customer.getUserId().equals(authenticatedUserIdAsInt)) {
            throw new ResponseStatusException(
                    HttpStatus.FORBIDDEN,
                                              "You do not have permission to access this third party invoice");
        }
    }
}