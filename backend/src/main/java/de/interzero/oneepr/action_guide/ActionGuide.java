package de.interzero.oneepr.action_guide;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.entity.ActionGuidePriceList;
import de.interzero.oneepr.customer.service_next_step.ServiceNextStep;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "action_guide",
        schema = "public"
)
public class ActionGuide {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "action_guide_id_gen"
    )
    @SequenceGenerator(
            name = "action_guide_id_gen",
            sequenceName = "action_guide_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "contract_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("contract")
    private Contract contract;

    @NotNull
    @Column(
            name = "country_id",
            nullable = false
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @NotNull
    @Column(
            name = "country_code",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_code")
    private String countryCode;

    @NotNull
    @Column(
            name = "country_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_name")
    private String countryName;

    @NotNull
    @Column(
            name = "country_flag",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("country_flag")
    private String countryFlag;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "termination_id")
    @JsonIgnore
    @JsonProperty("termination")
    private Termination termination;

    @OneToMany(
            mappedBy = "actionGuide",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("next_steps")
    private List<ServiceNextStep> nextSteps;

    @OneToMany(
            mappedBy = "actionGuide",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("price_list")
    private List<ActionGuidePriceList> priceList;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "contract_status",
            columnDefinition = "enum_contract_status not null"
    )
    @JsonProperty("contract_status")
    private Contract.Status contractStatus = Contract.Status.ACTIVE;

    @Transient
    @JsonProperty("contract_id")
    public Integer getContractId() {
        return contract != null ? contract.getId() : null;
    }

    @Transient
    @JsonProperty("termination_id")
    public Integer getTerminationId() {
        return termination != null ? termination.getId() : null;
    }

}
