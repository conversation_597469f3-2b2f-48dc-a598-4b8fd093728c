package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Top-level Data Transfer Object for the detailed user profile view.
 * <p>
 * This DTO is the root of the response body for the {@code customer.service.ts#findByUserId}
 * method. It aggregates the customer's base information along with a deep and detailed
 * view of their associated companies, phones, and contracts.
 */
@Data
@NoArgsConstructor
public class CustomerProfileDto {

    @Schema(description = "Unique identifier of the customer.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "Customer's first name.")
    @JsonProperty("first_name")
    private String firstName;

    @Schema(description = "Customer's last name.")
    @JsonProperty("last_name")
    private String lastName;

    @Schema(description = "Customer's salutation (e.g., Mr., Ms.).")
    @JsonProperty("salutation")
    private String salutation;

    @Schema(description = "Customer's unique email address.")
    @JsonProperty("email")
    private String email;

    @Schema(description = "The type of the customer (e.g., REGULAR, PREMIUM).")
    @JsonProperty("type")
    private Customer.Type type;

    @Schema(description = "The unique identifier of the associated user in the auth system.")
    @JsonProperty("user_id")
    private Integer userId;

    @Schema(description = "Flag indicating if the customer account is active.")
    @JsonProperty("is_active")
    private Boolean isActive;

    @Schema(description = "Optional ID of a related document.")
    @JsonProperty("document_id")
    private Integer documentId;

    @Schema(description = "Optional Stripe customer ID.")
    @JsonProperty("id_stripe")
    private String idStripe;

    @Schema(description = "Timestamp of when the customer record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the customer record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Optional company name associated with the customer.")
    @JsonProperty("company_name")
    private String companyName;

    @Schema(description = "Customer's preferred language.")
    @JsonProperty("language")
    private String language;

    @Schema(description = "Customer's preferred currency.")
    @JsonProperty("currency")
    private String currency;

    @Schema(description = "List of companies associated with the customer.")
    @JsonProperty("companies")
    private List<CompanyDetailsDto> companies;

    @Schema(description = "List of phone numbers associated with the customer.")
    @JsonProperty("phones")
    private List<CustomerPhoneDto> phones;

    @Schema(description = "List of contracts associated with the customer, with detailed nested data.")
    @JsonProperty("contracts")
    private List<UserViewContractDto> contracts;
}