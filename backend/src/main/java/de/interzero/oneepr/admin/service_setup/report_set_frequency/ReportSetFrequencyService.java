package de.interzero.oneepr.admin.service_setup.report_set_frequency;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.CreateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.UpdateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service class for managing Report Set Frequency business logic.
 */
@Service
@RequiredArgsConstructor
public class ReportSetFrequencyService {

    private final ReportSetFrequencyRepository reportSetFrequencyRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private final ModelMapper modelMapper;

    private final ObjectMapper objectMapper;

    private static final String FREQUENCY_NOT_FOUND = "Report set frequency not found";

    private static final String PACKAGING_SERVICE_NOT_FOUND = "Packaging service not found";

    /**
     * Creates a new ReportSetFrequency after validating its parent entities.
     *
     * @param createDto The DTO containing data for the new frequency.
     * @return The newly created and persisted ReportSetFrequency entity.
     */
    @Transactional
    public ReportSetFrequency create(CreateReportSetFrequencyDto createDto) {
        var packagingService = packagingServiceRepository.findById(createDto.getPackagingServiceId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));

        ReportSetFrequency newFrequency = modelMapper.map(createDto, ReportSetFrequency.class);
        newFrequency.setPackagingService(packagingService);

        try {
            newFrequency.setFrequency(objectMapper.writeValueAsString(createDto.getFrequency()));
        } catch (JsonProcessingException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid frequency JSON format", e);
        }

        return reportSetFrequencyRepository.save(newFrequency);
    }

    /**
     * Retrieves all non-deleted report set frequencies.
     *
     * @return A list of active ReportSetFrequency entities.
     */
    @Transactional(readOnly = true)
    public List<ReportSetFrequency> findAll() {
        return reportSetFrequencyRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted report set frequency by its ID.
     *
     * @param id The ID of the report set frequency to retrieve.
     * @return The found ReportSetFrequency entity.
     */
    @Transactional(readOnly = true)
    public ReportSetFrequency findOne(Integer id) {
        return reportSetFrequencyRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FREQUENCY_NOT_FOUND));
    }

    /**
     * Partially updates an existing report set frequency.
     *
     * @param id        The ID of the frequency to update.
     * @param updateDto DTO containing the fields to update.
     * @return The updated ReportSetFrequency entity.
     */
    @Transactional
    public ReportSetFrequency update(Integer id,
                                     UpdateReportSetFrequencyDto updateDto) {
        ReportSetFrequency frequency = reportSetFrequencyRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FREQUENCY_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, frequency);

        if (updateDto.getFrequency() != null) {
            try {
                frequency.setFrequency(objectMapper.writeValueAsString(updateDto.getFrequency()));
            } catch (JsonProcessingException e) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid frequency JSON format", e);
            }
        }

        if (updateDto.getPackagingServiceId() != null) {
            var packagingService = packagingServiceRepository.findById(updateDto.getPackagingServiceId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PACKAGING_SERVICE_NOT_FOUND));
            frequency.setPackagingService(packagingService);
        }

        return reportSetFrequencyRepository.save(frequency);
    }

    /**
     * Soft-deletes a report set frequency.
     *
     * @param id The ID of the frequency to soft-delete.
     */
    @Transactional
    public void remove(Integer id) {
        ReportSetFrequency frequency = reportSetFrequencyRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FREQUENCY_NOT_FOUND));

        frequency.setDeletedAt(Instant.now());
        reportSetFrequencyRepository.save(frequency);
    }
}