package de.interzero.oneepr.customer.company;

import de.interzero.oneepr.customer.entity.CompanyContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for managing CompanyContact entities.
 */
@Repository
public interface CompanyContactRepository extends JpaRepository<CompanyContact, Integer> {

    /**
     * Delete all company contacts by company ID.
     * Used in update operations to replace contact information.
     * Equivalent to TypeScript: deleteMany({ where: { company_id: companyId } })
     *
     * @param companyId Company ID
     */
    @Modifying
    @Query("DELETE FROM CompanyContact c WHERE c.company.id = :companyId")
    void deleteByCompany_Id(Integer companyId);

    /**
     * find company contact by company ID
     * @param companyId company ID
     * @return company contact
     */
    Optional<CompanyContact> findCompanyContactByCompany_Id(Integer companyId);
}
