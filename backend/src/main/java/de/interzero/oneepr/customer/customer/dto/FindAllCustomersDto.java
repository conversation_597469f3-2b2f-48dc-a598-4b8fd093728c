package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for filtering and paginating the customer list.
 * <p>
 * This DTO is a direct translation of the {@code FindAllCustomersDto}
 * from {@code src/customer/dto/find-all-customer.dto.ts}. It is used as a
 * query parameter object in the {@code findAll} service and controller methods.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FindAllCustomersDto {
    @Schema(description = "Page number")
    @JsonProperty("page")
    private Integer page;

    @Schema(description = "Page size")
    @JsonProperty("limit")
    private Integer limit;

    @Schema(description = "Order by")
    @JsonProperty("order")
    private Order order;

    @Schema(description = "Action performed")
    @JsonProperty("search")
    private String search;

    @Schema(description = "Customer service type")
    @JsonProperty("service_type")
    private ServiceType serviceType;

    @Schema(description = "Customer status")
    @JsonProperty("status")
    private Status status;

    @Schema(
            description = "Customer country code",
            example = "FR"
    )
    private String countryCode;

    public enum Order {
        ASC,
        DESC,
        LAST_MODIFIED,
        FIRST_MODIFIED
    }

    public enum ServiceType {
        EU_LICENSE,
        DIRECT_LICENSE,
        ACTION_GUIDE
    }

    public enum Status {
        ACTIVE,
        TERMINATED,
        TERMINATION_PROCESS
    }
}