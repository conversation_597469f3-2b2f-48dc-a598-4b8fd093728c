package de.interzero.oneepr.admin.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(
        name = "notifications",
        schema = "public"
)
public class Notification {

    @Id
    @Column(
            name = "id",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String id;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false
    )
    private Integer userId;

}