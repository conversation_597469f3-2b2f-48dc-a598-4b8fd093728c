package de.interzero.oneepr.customer.customer;

import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.dto.CompanyDetailsDto;
import de.interzero.oneepr.customer.customer.dto.ContractDetailsDto;
import de.interzero.oneepr.customer.customer.dto.CustomerPhoneDto;
import de.interzero.oneepr.customer.customer.dto.FindAllCustomersResponseDto;
import de.interzero.oneepr.customer.customer_phone.CustomerPhone;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * Mapper for converting Customer entities to their DTO representations.
 * This mapper is configured as a Spring component and will be automatically
 * available for dependency injection.
 */
@Mapper(componentModel = "spring")
public interface CustomerMapper {

    /**
     * Maps a Customer entity to the FindAllCustomersResponseDto.
     * MapStruct will automatically use the other methods in this interface
     * to map the nested collections (companies, phones, contracts).
     *
     * @param customer The source Customer entity.
     * @return The mapped FindAllCustomersResponseDto.
     */
    FindAllCustomersResponseDto toFindAllCustomersResponseDto(Customer customer);

    // --- Helper methods for nested collections ---

    /**
     * Maps a Company entity to its detailed DTO.
     */
    CompanyDetailsDto toDto(Company company);

    /**
     * Maps a CustomerPhone entity to its DTO.
     */
    CustomerPhoneDto toDto(CustomerPhone customerPhone);

    /**
     * Maps a Contract entity to its detailed DTO.
     */
    ContractDetailsDto toDto(Contract contract);
}