package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for EVATR (German Federal Central Tax Office) response.
 * Converted from TypeScript EvatValidationResponse interface.
 */
@Schema(description = "Response from EVATR VAT validation service")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvatrResponse {

    @JsonProperty("own_vat_id")
    @Schema(
            description = "Own VAT ID used for validation",
            example = "DE257906838"
    )
    private String ownVatId;

    @JsonProperty("partner_vat_id")
    @Schema(
            description = "Partner VAT ID being validated",
            example = "DE123456789"
    )
    private String partnerVatId;

    @JsonProperty("error_code")
    @Schema(
            description = "Error code from EVATR",
            example = "200"
    )
    private String errorCode;

    @JsonProperty("print")
    @Schema(
            description = "Print flag",
            example = "yes"
    )
    private String print;

    @JsonProperty("result_postal_code")
    @Schema(
            description = "Result postal code",
            example = "12345"
    )
    private String resultPostalCode;

    @JsonProperty("city")
    @Schema(
            description = "City",
            example = "Berlin"
    )
    private String city;

    @JsonProperty("date")
    @Schema(
            description = "Date of validation",
            example = "01.12.2023"
    )
    private String date;

    @JsonProperty("postal_code")
    @Schema(
            description = "Postal code",
            example = "12345"
    )
    private String postalCode;

    @JsonProperty("result_city")
    @Schema(
            description = "Result city",
            example = "Berlin"
    )
    private String resultCity;

    @JsonProperty("time")
    @Schema(
            description = "Time of validation",
            example = "14:30:00"
    )
    private String time;

    @JsonProperty("result_name")
    @Schema(
            description = "Result name",
            example = "Example Company GmbH"
    )
    private String resultName;

    @JsonProperty("valid_from")
    @Schema(
            description = "Valid from date",
            example = "01.01.2020"
    )
    private String validFrom;

    @JsonProperty("valid_until")
    @Schema(
            description = "Valid until date",
            example = "31.12.2025"
    )
    private String validUntil;

    @JsonProperty("street")
    @Schema(
            description = "Street",
            example = "Example Street 123"
    )
    private String street;

    @JsonProperty("company_name")
    @Schema(
            description = "Company name",
            example = "Example Company GmbH"
    )
    private String companyName;

    @JsonProperty("result_street")
    @Schema(
            description = "Result street",
            example = "Example Street 123"
    )
    private String resultStreet;
}
