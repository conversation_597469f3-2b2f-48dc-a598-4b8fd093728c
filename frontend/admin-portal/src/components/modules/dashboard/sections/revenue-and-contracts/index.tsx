"use client";

import { RevenueAndContractsCountriesMap } from "@/components/common/map/revenue-and-contracts-map";
import { DashboardPlaceHolder } from "@/components/modules/dashboard/placeholder/placeholder";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { getRevenueAndContracts } from "@/lib/api/dashboard/revenue-and-contracts";
import { GraphColors } from "@/utils/graph-colors";
import { KeyboardArrowDown, Sort } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { CountryLicenseItem } from "./country-license-item";

const LICENSE_YEAR_FILTERS = [
  { label: "2025", value: "2025" },
  { label: "2024", value: "2024" },
  { label: "2023", value: "2023" },
];

const LICENSE_SORT = [
  { label: "Number of contracts", value: "contracts_number" },
  { label: "Revenue", value: "total_revenue" },
];

// TODO:loading state
export function RevenueAndContracts() {
  const [selectedLicenseYear, setSelectedLicenseYear] = useState(LICENSE_YEAR_FILTERS[0]);
  const [selectedSort, setSelectedSort] = useState(LICENSE_SORT[0]);
  const { data, isSuccess, isLoading } = useQuery({
    queryKey: ["revenue-and-contracts", selectedLicenseYear.value, selectedSort.value],
    queryFn: () => getRevenueAndContracts({ year: +selectedLicenseYear.value, order_by: selectedSort.value }),
  });
  const countriesToShow = isSuccess ? data.countries?.slice(0, 4) : [];
  const showPlaceHolder = isSuccess && countriesToShow.length === 0;
  const graphColors = isSuccess
    ? new GraphColors(
        countriesToShow.map((c) => c.code),
        true
      )
    : null;

  function handleChangeLicenseYear(newOrder: (typeof LICENSE_YEAR_FILTERS)[number]) {
    setSelectedLicenseYear(newOrder);
  }

  function handleChangeLicenseSort(newOrder: (typeof LICENSE_SORT)[number]) {
    setSelectedSort(newOrder);
  }

  return (
    <div id="revenue-and-contracts" className="w-full bg-white rounded-3xl p-6 flex flex-col gap-6">
      <div className="flex flex-row justify-between items-center">
        <h3 className="text-primary text-2xl font-bold">Revenue and contracts</h3>

        <div className="flex flex-row gap-6 items-center">
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedLicenseYear.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_YEAR_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseYear(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="h-[420px]">
          {isLoading && <Skeleton className="h-[420px] rounded-[40px]" />}
          {graphColors && isSuccess && (
            <RevenueAndContractsCountriesMap
              graphColors={graphColors}
              selectedCountries={countriesToShow.map((c) => ({
                code: c.code,
                flag_url: c.country_flag,
                name: c.country_name,
                revenue: c.total_revenue / 100,
                total_licenses: c.contracts_number,
              }))}
            />
          )}
        </div>
        <div>
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedSort.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {LICENSE_SORT.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeLicenseSort(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown>
          {isLoading && (
            <>
              <Skeleton className="h-20" />
              <Skeleton className="h-20 mt-2" />
              <Skeleton className="h-20 mt-2" />
              <Skeleton className="h-20 mt-2" />
            </>
          )}
          {showPlaceHolder && <DashboardPlaceHolder itemName="countries" className="max-h-[390px]" />}
          {isSuccess &&
            countriesToShow.length > 0 &&
            graphColors &&
            countriesToShow.map((item, idx) => (
              <CountryLicenseItem
                key={`country-revenue-item-${idx}`}
                position={idx + 1}
                label={item.country_name}
                revenue={item.total_revenue}
                licensesCount={item.contracts_number}
                flag={item.country_flag}
                color={graphColors.getColor(item.code)}
                license={item.code === "DE" ? "Direct license" : "EU license"}
              />
            ))}
        </div>
      </div>
    </div>
  );
}
