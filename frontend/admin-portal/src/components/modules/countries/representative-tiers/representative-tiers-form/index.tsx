"use client";

import { But<PERSON> } from "@interzero/oneepr-react-ui/Button";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { useEffect } from "react";
import { queryClient } from "@/lib/react-query";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Add } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import {
  createRepresentativeTier,
  deleteRepresentativeTier,
  updateRepresentativeTier,
} from "@/lib/api/representative-tiers";
import { FractionInput } from "@/components/ui/fraction-input";
import { RepresentativeTier } from "@/types/service-setup/representative-tier";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { enqueueSnackbar } from "notistack";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";

const representativeTierSchema = z.object({
  id: z.coerce
    .number()
    .transform((value) => Number(value) || undefined)
    .optional(),
  name: z.string().min(1, "Name is required").regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed"),
  price: z.coerce.number().min(1, "Price is required"),
});

const representativeTiersFormSchema = z.object({
  representativeTiers: z.array(representativeTierSchema),
});

type RepresentativeTiersFormData = z.infer<typeof representativeTiersFormSchema>;

interface RepresentativeTiersFormProps {
  representativeTiers: RepresentativeTier[];
}

export function RepresentativeTiersForm({ representativeTiers }: RepresentativeTiersFormProps) {
  const { country } = useServiceSetup();

  const {
    control,
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
    setValue,
    reset,
  } = useForm<RepresentativeTiersFormData>({
    resolver: zodResolver(representativeTiersFormSchema),
    defaultValues: {
      representativeTiers,
    },
  });

  useEffect(() => {
    reset({ representativeTiers });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [representativeTiers]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "representativeTiers",
    keyName: "key",
  });

  async function handleFormSubmit(data: RepresentativeTiersFormData) {
    try {
      if (!data.representativeTiers.length) return;

      const promises = data.representativeTiers.map(async (representativeTier) => {
        if (!representativeTier.id) {
          return createRepresentativeTier({ ...representativeTier, country_id: country.id });
        }

        return updateRepresentativeTier(representativeTier.id, representativeTier);
      });

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        setValue(`representativeTiers.${index}.id`, response.value.id);
      });

      queryClient.invalidateQueries({ queryKey: ["service-setup-representative-tiers", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });

      enqueueSnackbar("Representative tiers saved successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to save representative tiers", { variant: "error" });
    }
  }

  async function handleDeleteRepresentativeTier(index: number) {
    const representativeTier = fields[index];

    if (!representativeTier.id) {
      remove(index);
      enqueueSnackbar("Representative tier removed successfully", { variant: "success" });
      return;
    }

    try {
      await deleteRepresentativeTier(representativeTier.id);

      remove(index);

      queryClient.invalidateQueries({ queryKey: ["service-setup-representative-tiers", country.code] });
      enqueueSnackbar("Representative tier removed successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Failed to remove representative tier. Please try again.", { variant: "error" });
    }
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-10">
      {fields.map((field, index) => (
        <div key={field.key} className="flex flex-col gap-6 bg-background py-6 px-5 rounded-3xl">
          <input type="hidden" className="hidden" {...register(`representativeTiers.${index}.id`)} />
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-primary">Tier {index + 1}</p>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <button
                    type="button"
                    className="text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3"
                  >
                    Remove tier
                  </button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete representative tier?</AlertDialogTitle>
                    <AlertDialogDescription>
                      By clicking on ”confirm” you are deleting this representative tier.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Back</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleDeleteRepresentativeTier(index)}>Confirm</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
          <div className="w-full grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="col-span-3">
              <Input
                label="Name *"
                placeholder="Name for the service"
                {...register(`representativeTiers.${index}.name`)}
                variant={errors.representativeTiers?.[index]?.name ? "error" : "default"}
                errorMessage={errors.representativeTiers?.[index]?.name?.message}
              />
            </div>
            <div className="col-span-1">
              <Controller
                name={`representativeTiers.${index}.price`}
                control={control}
                render={({ field }) => (
                  <FractionInput
                    label="Price *"
                    {...field}
                    type="currency"
                    error={errors.representativeTiers?.[index]?.price?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>
      ))}
      <div className="flex items-center justify-between">
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<Add />}
          onClick={() => append({ name: "", price: 0 })}
        >
          Add tier
        </Button>
        <div className="flex items-center justify-end gap-10">
          <Button type="submit" variant="filled" color="dark-blue" size="medium" className="w-60">
            {isSubmitting ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
    </form>
  );
}
