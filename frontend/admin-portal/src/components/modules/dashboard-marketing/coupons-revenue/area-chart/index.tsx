import React, { PureComponent } from "react";
import { AreaChart, Area, <PERSON>Axis, <PERSON><PERSON><PERSON>s, CartesianGrid, ResponsiveContainer } from "recharts";

const data = [
  {
    name: "Quarter 1",
    uv: 500,
  },
  {
    name: "Quarter 2",
    uv: 100,
  },
  {
    name: "Quarter 3",
    uv: 1000,
  },
  {
    name: "Quarter 4",
    uv: 800,
  },
];

export default class AreaChartComponent extends PureComponent {
  render() {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          width={500}
          height={400}
          data={data}
          margin={{
            top: 0,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          <CartesianGrid vertical={false} />
          <XAxis dataKey="name" tick={{ fontSize: 16, fill: "#73706E" }} />
          <YAxis />
          <Area type="basis" dataKey="uv" stroke="#CD2C19" fill="#CD2C1926" />
        </AreaChart>
      </ResponsiveContainer>
    );
  }
}
