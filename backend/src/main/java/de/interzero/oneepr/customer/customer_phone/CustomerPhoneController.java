package de.interzero.oneepr.customer.customer_phone;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.consent.dto.UpdateConsentDto;
import de.interzero.oneepr.customer.customer_phone.dto.CreateCustomerPhoneDto;
import de.interzero.oneepr.customer.customer_phone.dto.UpdateCustomerPhoneDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

import static de.interzero.oneepr.common.string.Role.*;

@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@Tag(
        name = "customer-phone",
        description = "APIs for managing customer phone numbers"
)
@RestController
@RequestMapping(Api.CUSTOMER_PHONE)
@RequiredArgsConstructor
public class CustomerPhoneController {

    private final CustomerPhoneService customerPhoneService;

    @PostMapping
    @Operation(summary = "Create customer phone")
    @ResponseStatus(HttpStatus.CREATED)
    @ApiResponse(
            responseCode = "201",
            description = "The customer phone has been successfully created."
    )
    public ResponseEntity<CustomerPhone> create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateCustomerPhoneDto.class)
            )
    ) @RequestBody CreateCustomerPhoneDto createCustomerPhoneDto) {
        CustomerPhone createdPhone = this.customerPhoneService.create(createCustomerPhoneDto);
        return new ResponseEntity<>(createdPhone, HttpStatus.CREATED);
    }

    /**
     * Retrieves all customer phone records using a projection.
     *
     * @return ResponseEntity containing a list of {@link CustomerPhone}.
     */
    @ApiResponse(
            responseCode = "200",
            description = "List of customer phones."
    )
    @GetMapping
    @Operation(summary = "Get all customer phones")
    public ResponseEntity<List<CustomerPhone>> findAll() {
        return new ResponseEntity<>(this.customerPhoneService.findAll(), HttpStatus.OK);
    }

    /**
     * Retrieves a specific customer phone by its ID using a projection.
     * Returns 200 OK with phone details, or 200 OK with a null body if not found.
     *
     * @param id The ID of the customer phone to retrieve.
     * @return ResponseEntity containing {@link CustomerPhone} or null.
     */
    @ApiResponse(
            responseCode = "200",
            description = "The customer phone details."
    )
    @GetMapping("/{id}")
    @Operation(summary = "Get customer phone by id")
    public ResponseEntity<CustomerPhone> findOne(@Parameter(description = "ID of phone to get") @PathVariable("id") Integer id) {
        Optional<CustomerPhone> customerPhone = this.customerPhoneService.findOne(
                id,
                AuthUtil.getRelevantUserDetails());
        return customerPhone.map(c -> new ResponseEntity<>(c, HttpStatus.OK))
                .orElseGet(() -> ResponseEntity.status(HttpStatus.OK).body(null));
    }

    /**
     * Updates an existing customer phone record identified by its ID.
     * Requires authenticated user details for the operation.
     *
     * @param id                     The ID of the customer phone to update.
     * @param updateCustomerPhoneDto DTO containing the updated phone information.
     * @return The updated customer phone details
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update customer phone by id")
    @ApiResponse(
            responseCode = "200",
            description = "The customer phone has been successfully updated."
    )
    public Object update(@Parameter(description = "ID of phone to update") @PathVariable("id") Integer id,
                         @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                 required = true,
                                 content = @Content(
                                         mediaType = "application/json",
                                         schema = @Schema(implementation = UpdateConsentDto.class)
                                 )
                         ) @RequestBody UpdateCustomerPhoneDto updateCustomerPhoneDto) {
        return this.customerPhoneService.update(id, updateCustomerPhoneDto, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Deletes a customer phone record identified by its ID.
     * Requires authenticated user details for the operation.
     *
     * @param id The ID of the customer phone to delete.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete customer phone by id")
    @ApiResponse(
            responseCode = "200",
            description = "The customer phone has been successfully deleted."
    )
    public void remove(@Parameter(description = "ID of phone to delete") @PathVariable("id") Integer id) {
        this.customerPhoneService.remove(id, AuthUtil.getRelevantUserDetails());
    }
}