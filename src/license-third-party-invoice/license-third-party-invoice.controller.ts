import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { ApiQuery } from "@nestjs/swagger";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { LicenseThirdPartyInvoiceService } from "./license-third-party-invoice.service";
import { UpdateLicenseThirdPartyInvoiceDto } from "./dto/update-license-third-party-invoice.dto";
import { CreateLicenseThirdPartyInvoiceDto } from "./dto/create-license-third-party-invoice.dto";
import { FindAllThirdPartyInvoiceDto } from "./dto/find-all-third-party-invoice.dto";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Controller("third-party-invoices")
@ApiTags("third-party-invoices")
export class LicenseThirdPartyInvoiceController {
  constructor(private readonly licenseThirdPartyInvoiceService: LicenseThirdPartyInvoiceService) {}

  @Get()
  @ApiQuery({ name: "license_id", required: false, type: Number })
  @ApiOperation({ summary: "Get all third party invoices" })
  findAll(@Query() data: FindAllThirdPartyInvoiceDto) {
    return this.licenseThirdPartyInvoiceService.findAll(data);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a third party invoice by id" })
  findOne(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseThirdPartyInvoiceService.findOne(id, user);
  }

  @Post()
  @ApiOperation({ summary: "Create a third party invoice" })
  create(@Body() data: CreateLicenseThirdPartyInvoiceDto) {
    return this.licenseThirdPartyInvoiceService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a third party invoice" })
  update(@Param("id") id: number, @Body() data: UpdateLicenseThirdPartyInvoiceDto, @User() user: AuthenticatedUser) {
    return this.licenseThirdPartyInvoiceService.update(id, data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a third party invoice" })
  remove(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.licenseThirdPartyInvoiceService.remove(id, user);
  }
}
