package de.interzero.oneepr.customer.shopping_cart;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.integration.MondayService;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartItemDto;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartItemDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ShoppingCartServiceTest {

    @Mock
    private ShoppingCartRepository shoppingCartRepository;

    @Mock
    private ShoppingCartItemRepository shoppingCartItemRepository;

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private MondayService mondayService;

    @Spy
    @InjectMocks
    ShoppingCartService service;

    @Spy
    @InjectMocks
    private ShoppingCartService shoppingCartService;

    private CreateShoppingCartItemDto buildDto() {
        CreateShoppingCartItemDto dto = new CreateShoppingCartItemDto();
        dto.setCountryId(1);
        dto.setCountryCode("DE");
        dto.setCountryName("Germany");
        dto.setCountryFlag("DE");
        dto.setYear(2025);
        dto.setServiceType(Contract.Type.DIRECT_LICENSE);
        return dto;
    }

    private ShoppingCart newOpenCart(String id,
                                     String email) {
        ShoppingCart c = new ShoppingCart();
        c.setId(id);
        c.setEmail(email);
        c.setJourney(ShoppingCart.Journey.DIRECT_LICENSE);
        c.setStatus(ShoppingCart.Status.OPEN);
        c.setSubtotal(0);
        c.setTotal(0);
        c.setVatPercentage(0);
        c.setVatValue(0);
        c.setCouponValue(0);
        c.setCreatedAt(Instant.now());
        c.setUpdatedAt(Instant.now());
        return c;
    }

    @BeforeEach
    void stubInternals() {
        lenient().doNothing().when(shoppingCartService).updateCartItemCalculator(anyString(), anyInt());
        lenient().doAnswer(inv -> shoppingCartRepository.findById(inv.getArgument(0)).orElse(null))
                .when(shoppingCartService)
                .refreshShoppingCartTotal(anyString());
    }

    /**
     * Success: item is created, packagingServices is set for DIRECT_LICENSE, result is refreshShoppingCartTotal(...)
     */
    @Test
    void addItem_success_shouldCreateDirectLicenseItem_andReturnRefreshedCart() {
        String cartId = "c1";
        String email = "<EMAIL>";

        ShoppingCart cart = newOpenCart(cartId, email);

        when(shoppingCartRepository.findById(cartId)).thenReturn(Optional.of(cart));
        when(shoppingCartItemRepository.findFirstByShoppingCartIdAndYearAndServiceTypeAndCountryCode(
                cartId,
                2025,
                Contract.Type.DIRECT_LICENSE,
                "DE")).thenReturn(Optional.empty());

        Map<String, Object> stubPriceList = Map.of(
                "handling_fee",
                0,
                "registration_fee",
                0,
                "variable_handling_fee",
                0);
        doReturn(stubPriceList).when(shoppingCartService).getServicePriceList(Contract.Type.DIRECT_LICENSE, 2025);

        ArgumentCaptor<ShoppingCartItem> itemCaptor = ArgumentCaptor.forClass(ShoppingCartItem.class);
        when(shoppingCartItemRepository.save(itemCaptor.capture())).thenAnswer(inv -> {
            ShoppingCartItem it = inv.getArgument(0);
            it.setId(100);
            return it;
        });

        ShoppingCart refreshed = newOpenCart(cartId, email);
        refreshed.getItems().add(new ShoppingCartItem());
        doReturn(refreshed).when(shoppingCartService).refreshShoppingCartTotal(cartId);

        AuthenticatedUser user = new AuthenticatedUser("u1", Role.CUSTOMER, email);

        ShoppingCart result = shoppingCartService.addItem(cartId, buildDto(), user);

        assertThat(result).isSameAs(refreshed);

        ShoppingCartItem saved = itemCaptor.getValue();
        assertThat(saved.getShoppingCart()).isSameAs(cart);
        assertThat(saved.getCountryId()).isEqualTo(1);
        assertThat(saved.getCountryCode()).isEqualTo("DE");
        assertThat(saved.getCountryName()).isEqualTo("Germany");
        assertThat(saved.getCountryFlag()).isEqualTo("DE");
        assertThat(saved.getServiceType()).isEqualTo(Contract.Type.DIRECT_LICENSE);
        assertThat(saved.getYear()).isEqualTo(2025);
        assertThat(saved.getPriceList()).isEqualTo(stubPriceList);
        assertThat(saved.getPackagingServices()).isInstanceOfAny(java.util.List.class);
    }

    /**
     * Cart not found -> 404 NOT_FOUND
     */
    @Test
    void addItem_shouldThrow404_whenCartNotFound() {
        String cartId = "missing";
        when(shoppingCartRepository.findById(cartId)).thenReturn(Optional.empty());

        var dto = buildDto();
        var user = new AuthenticatedUser("u1", Role.ADMIN, "a@b");

        assertThatThrownBy(() -> shoppingCartService.addItem(
                cartId,
                dto,
                user)).isInstanceOf(ResponseStatusException.class)
                .satisfies(ex -> assertThat(((ResponseStatusException) ex).getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND));
    }

    /**
     * Duplicate item -> 400 BAD_REQUEST
     */
    @Test
    void addItem_shouldThrow400_whenDuplicateItem() {
        String cartId = "c1";
        ShoppingCart cart = newOpenCart(cartId, "<EMAIL>");
        when(shoppingCartRepository.findById(cartId)).thenReturn(Optional.of(cart));

        ShoppingCartItem existing = new ShoppingCartItem();
        when(shoppingCartItemRepository.findFirstByShoppingCartIdAndYearAndServiceTypeAndCountryCode(
                cartId,
                2025,
                Contract.Type.DIRECT_LICENSE,
                "DE")).thenReturn(Optional.of(existing));

        var dto = buildDto();
        var user = new AuthenticatedUser("u1", Role.CUSTOMER, "<EMAIL>");
        assertThatThrownBy(() -> shoppingCartService.addItem(
                cartId,
                dto,
                user)).isInstanceOf(ResponseStatusException.class)
                .satisfies(ex -> assertThat(((ResponseStatusException) ex).getStatusCode()).isEqualTo(HttpStatus.BAD_REQUEST));

        verify(shoppingCartItemRepository, never()).save(any());
        verify(shoppingCartService, never()).updateCartItemCalculator(anyString(), anyInt());
    }

    /**
     * Access forbidden for CUSTOMER with different email -> 403 FORBIDDEN
     */
    @Test
    void addItem_shouldThrow403_whenCustomerEmailMismatch() {
        String cartId = "c1";
        ShoppingCart cart = newOpenCart(cartId, "<EMAIL>");
        when(shoppingCartRepository.findById(cartId)).thenReturn(Optional.of(cart));
        when(shoppingCartItemRepository.findFirstByShoppingCartIdAndYearAndServiceTypeAndCountryCode(
                anyString(),
                anyInt(),
                any(),
                anyString())).thenReturn(Optional.empty());

        AuthenticatedUser intruder = new AuthenticatedUser("u2", Role.CUSTOMER, "<EMAIL>");

        var dto = buildDto();

        assertThatThrownBy(() -> shoppingCartService.addItem(cartId, dto, intruder)).isInstanceOf(
                        ResponseStatusException.class)
                .satisfies(ex -> assertThat(((ResponseStatusException) ex).getStatusCode()).isEqualTo(HttpStatus.FORBIDDEN));

        verify(shoppingCartItemRepository, never()).save(any());
        verify(shoppingCartService, never()).updateCartItemCalculator(anyString(), anyInt());
    }

    /**
     * Success: changes the item's year; fetches a new price list for DIRECT_LICENSE;
     * applies default packagingServices when none provided;
     * calls updateCartItemCalculator(...) and returns the result of refreshShoppingCartTotal(...).
     */
    @Test
    void updateItem_success_changesYear_updatesPriceList_andDefaultPackagingForDirect() {
        String cartId = "c1";
        Integer itemId = 10;

        ShoppingCart cart = new ShoppingCart();
        cart.setId(cartId);
        cart.setEmail(null);

        ShoppingCartItem item = new ShoppingCartItem();
        item.setId(itemId);
        item.setShoppingCart(cart);
        item.setCountryCode("DE");
        item.setCountryName("Germany");
        item.setServiceType(Contract.Type.DIRECT_LICENSE);
        item.setYear(2024);
        item.setPriceList(Map.of("handling_fee", 1));

        when(shoppingCartItemRepository.findByIdAndShoppingCartId(itemId, cartId)).thenReturn(Optional.of(item));

        Map<String, Object> newPrice = Map.of("handling_fee", 0, "registration_fee", 0, "variable_handling_fee", 0);
        doReturn(newPrice).when(service).getServicePriceList(Contract.Type.DIRECT_LICENSE, 2025);
        doNothing().when(service).updateCartItemCalculator(cartId, itemId);

        ShoppingCart refreshed = new ShoppingCart();
        refreshed.setId(cartId);
        doReturn(refreshed).when(service).refreshShoppingCartTotal(cartId);

        UpdateShoppingCartItemDto data = new UpdateShoppingCartItemDto();
        data.setYear(2025);
        data.setPackagingServices(null);

        ShoppingCart result = service.updateItem(
                cartId,
                itemId,
                data,
                new AuthenticatedUser("u1", Role.CUSTOMER, "<EMAIL>"));

        assertThat(result).isSameAs(refreshed);
        assertThat(item.getYear()).isEqualTo(2025);
        assertThat(item.getPriceList()).isEqualTo(newPrice);
        assertThat(item.getPackagingServices()).isInstanceOf(List.class);
    }

    /**
     * Success: removes an existing cart item, invokes mondayService.removeItem(...) when a customer is present,
     * deletes the item from the repository, and returns the cart from refreshShoppingCartTotal(...).
     */
    @Test
    void removeItem_success_deletesItem_callsMondayAndReturnsRefreshedCart() {
        String cartId = "c1";
        Integer itemId = 10;

        ShoppingCart cart = new ShoppingCart();
        cart.setId(cartId);
        cart.setEmail("<EMAIL>");

        ShoppingCartItem item = new ShoppingCartItem();
        item.setId(itemId);
        item.setShoppingCart(cart);
        item.setCountryName("Germany");

        when(shoppingCartItemRepository.findByIdAndShoppingCartIdAndShoppingCartDeletedAtIsNull(
                itemId,
                cartId)).thenReturn(
                Optional.of(item));

        Customer customer = new Customer();
        customer.setId(123);
        customer.setEmail("<EMAIL>");
        when(customerRepository.findByEmailAndDeletedAtIsNull("<EMAIL>")).thenReturn(Optional.of(customer));

        doNothing().when(shoppingCartItemRepository).deleteByIdAndShoppingCartId(itemId, cartId);

        ShoppingCart refreshed = new ShoppingCart();
        refreshed.setId(cartId);
        doReturn(refreshed).when(shoppingCartService).refreshShoppingCartTotal(cartId);

        AuthenticatedUser user = new AuthenticatedUser("u1", Role.CUSTOMER, "<EMAIL>");

        ShoppingCart result = shoppingCartService.removeItem(cartId, itemId, user);

        assertThat(result).isSameAs(refreshed);

        verify(mondayService).removeItem(123, "Germany");
        verify(shoppingCartItemRepository).deleteByIdAndShoppingCartId(itemId, cartId);
        verify(shoppingCartService).refreshShoppingCartTotal(cartId);
    }

    /**
     * 404 NOT FOUND: when the shopping cart item is not found by itemId and cartId,
     * removeItem(...) should throw ResponseStatusException with status 404.
     */
    @Test
    void removeItem_shouldThrow404_whenItemNotFound() {
        when(shoppingCartItemRepository.findByIdAndShoppingCartIdAndShoppingCartDeletedAtIsNull(10, "c1")).thenReturn(
                Optional.empty());

        AuthenticatedUser intruder = new AuthenticatedUser("u", Role.ADMIN, "a@b");
        ResponseStatusException ex = org.junit.jupiter.api.Assertions.assertThrows(
                ResponseStatusException.class,
                () -> shoppingCartService.removeItem(
                        "c1",
                        10,
                        intruder));
        assertThat(ex.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);

        verify(shoppingCartItemRepository, never()).deleteByIdAndShoppingCartId(any(), anyString());
        verify(mondayService, never()).removeItem(any(), anyString());
    }

    /**
     * 403 FORBIDDEN: when the authenticated CUSTOMER has an email different from
     * the shopping cart owner, removeItem(...) should throw ResponseStatusException with status 403.
     */
    @Test
    void removeItem_shouldThrow403_whenCustomerEmailMismatch() {
        String cartId = "c1";
        Integer itemId = 10;

        ShoppingCart cart = new ShoppingCart();
        cart.setId(cartId);
        cart.setEmail("<EMAIL>");

        ShoppingCartItem item = new ShoppingCartItem();
        item.setId(itemId);
        item.setShoppingCart(cart);
        item.setCountryName("Germany");

        when(shoppingCartItemRepository.findByIdAndShoppingCartIdAndShoppingCartDeletedAtIsNull(
                itemId,
                cartId)).thenReturn(
                Optional.of(item));

        AuthenticatedUser intruder = new AuthenticatedUser("u2", Role.CUSTOMER, "<EMAIL>");

        assertThatThrownBy(() -> shoppingCartService.removeItem(cartId, itemId, intruder)).isInstanceOf(
                        ResponseStatusException.class)
                .satisfies(ex -> assertThat(((ResponseStatusException) ex).getStatusCode()).isEqualTo(HttpStatus.FORBIDDEN));

        verify(shoppingCartItemRepository, never()).deleteByIdAndShoppingCartId(any(), anyString());
        verify(mondayService, never()).removeItem(any(), anyString());
    }

}
