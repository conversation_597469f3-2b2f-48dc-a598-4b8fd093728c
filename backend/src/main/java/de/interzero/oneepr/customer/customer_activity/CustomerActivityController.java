package de.interzero.oneepr.customer.customer_activity;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.customer_activity.dto.CreateCustomerActivityDto;
import de.interzero.oneepr.customer.customer_activity.dto.FindAllCustomerActivityDto;
import de.interzero.oneepr.customer.customer_activity.dto.UpdateCustomerActivityDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "customer-activity",
        description = "Operations related to customer activities"
)
@RestController
@RequestMapping(Api.CUSTOMER_ACTIVITY)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class CustomerActivityController {

    private final CustomerActivityService customerActivityService;

    /**
     * Parses a string ID to an Integer, throwing a BadRequestException if parsing fails.
     *
     * @param idRaw The raw string ID from the path.
     * @return The parsed Integer ID.
     * @ts-legacy Original NestJS code used Number(id) which might have different error handling.
     * This method provides explicit parsing and error response.
     */
    private Integer parseId(String idRaw) {
        try {
            return Integer.parseInt(idRaw);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Invalid format: '" + idRaw + "' must be an integer.");
        }
    }

    /**
     * Creates a new customer activity.
     *
     * @param createCustomerActivityDto DTO containing information for the new activity.
     * @return The created customer activity.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
            summary = "Create customer activity",
            responses = {@ApiResponse(
                    responseCode = "201",
                    description = "The customer activity has been successfully created.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerActivity.class)
                    )
            )}
    )
    public CustomerActivity create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Data to create a new customer activity.",
            required = true,
            content = @Content(schema = @Schema(implementation = CreateCustomerActivityDto.class))
    ) @RequestBody CreateCustomerActivityDto createCustomerActivityDto) {
        return this.customerActivityService.create(createCustomerActivityDto);
    }

    /**
     * Retrieves all customer activities, optionally filtered by query parameters.
     *
     * @param query DTO containing filter parameters (e.g., customerId).
     * @return A list of customer activities.
     */
    @GetMapping
    @Operation(
            summary = "Get all customer activities",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "List of customer activities",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(
                                    implementation = List.class,
                                    subTypes = {CustomerActivity.class}
                            )
                    )
            )}
    )
    public List<CustomerActivity> findAll(@RequestBody FindAllCustomerActivityDto query) {
        return this.customerActivityService.findAll(query);
    }

    /**
     * Retrieves a specific customer activity by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw The string ID of the customer activity from the path.
     * @return The customer activity details.
     */
    @GetMapping("/{id}")
    @Operation(
            summary = "Get customer activity by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer activity details",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerActivity.class)
                    )
            )}
    )
    public CustomerActivity findOne(@Parameter(description = "ID of the customer activity to retrieve.") @PathVariable("id") String idRaw) {
        Integer id = parseId(idRaw);
        return this.customerActivityService.findOne(id, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Updates an existing customer activity by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw                     The string ID of the customer activity to update.
     * @param updateCustomerActivityDto DTO containing updated data.
     * @return The updated customer activity.
     * @ts-legacy Original Swagger summary was "Update customer phone by id", corrected to activity.
     */
    @PutMapping("/{id}")
    @Operation(
            summary = "Update customer activity by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer activity has been successfully updated.",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CustomerActivity.class)
                    )
            )}
    )
    public CustomerActivity update(@Parameter(description = "ID of the customer activity to update.") @PathVariable("id") String idRaw,
                                   @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                           description = "Data to update the customer activity.",
                                           required = true,
                                           content = @Content(schema = @Schema(implementation = UpdateCustomerActivityDto.class))
                                   ) @RequestBody UpdateCustomerActivityDto updateCustomerActivityDto) {
        Integer id = parseId(idRaw);
        return this.customerActivityService.update(id, updateCustomerActivityDto, AuthUtil.getRelevantUserDetails());
    }

    /**
     * Deletes (soft delete) a customer activity by its ID.
     * User details are obtained internally via AuthUtil and passed directly to the service.
     *
     * @param idRaw The string ID of the customer activity to delete.
     * @ts-legacy Returns HTTP 200 OK on successful soft delete.
     */
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(
            summary = "Delete customer activity by id",
            responses = {@ApiResponse(
                    responseCode = "200",
                    description = "The customer activity has been successfully marked as deleted."
            )}
    )
    public void remove(@Parameter(description = "ID of the customer activity to delete.") @PathVariable("id") String idRaw) {
        Integer id = parseId(idRaw);
        this.customerActivityService.remove(id, AuthUtil.getRelevantUserDetails());
    }
}