# Build the Next.js app
ARG NODE_VERSION=20.18.1

FROM node:${NODE_VERSION}-alpine

ARG API="http://host.docker.internal:8080"

# Install dependencies
RUN apk add --no-cache libc6-compat

WORKDIR /app

RUN npm install -g corepack --verbose

# Install dependencies based on the preferred package manager
COPY package.json .npmrc pnpm-lock.yaml* ./
RUN corepack enable && \
    corepack prepare pnpm@9.12.2 --activate && \
    pnpm config set store-dir /tmp/.pnpm-store;

RUN pnpm i --frozen-lockfile

# Copy the rest of the app's source code
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN echo "$API"

# Build the app
RUN pnpm build


# Runner
FROM node:${NODE_VERSION}-alpine

WORKDIR /app

ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=0 /app/public ./public
COPY --from=0 --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=0 --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME 0.0.0.0
ENV NODE_ENV=${NODE_ENV}
CMD ["node", "server.js"]
