import { Providers } from "@/components/providers/providers";
import { routing } from "@/i18n/routing";
import { cn } from "@/lib/utils";
import "@/styles/globals.css";
import type { Metadata } from "next";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { getMessages } from "next-intl/server";
import localFont from "next/font/local";
import { notFound } from "next/navigation";

const centraNo2 = localFont({
  src: [
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Thin.woff",
      weight: "100",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Light.woff",
      weight: "300",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Book.woff",
      weight: "400",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Medium.woff",
      weight: "500",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Bold.woff",
      weight: "700",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Extrabold.woff",
      weight: "800",
    },
    {
      path: "../../../public/fonts/CentraNo2/CentraNo2-Black.woff",
      weight: "900",
    },
  ],
  variable: "--font-centra",
});

export const metadata: Metadata = {
  title: {
    default: "Lizenzero | Clerk",
    template: `%s - Lizenzero | Clerk`,
  },
  description: "Generated by create next app",
};

interface RootLayoutProps {
  children: React.ReactNode;
  params: { locale: string; session: any };
}

export default async function RootLayout({ children, params: { locale } }: RootLayoutProps) {
  if (!hasLocale(routing.locales, locale)) return notFound();

  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={cn("leading-none bg-background", centraNo2.className)}>
        <NextIntlClientProvider messages={messages}>
          <Providers>{children}</Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
