import { UserTypes } from "@/utils/user";

export type FileType =
  | "GENERAL_INFORMATION"
  | "REQUIRED_INFORMATION"
  | "CONTRACT"
  | "CONTRACT_TERMINATION"
  | "LICENSE_CONTRACT"
  | "CERTIFICATE"
  | "INVOICE"
  | "PAYMENT"
  | "LICENSE_PROOF_OF_REGISTRATION"
  | "PROOF_OF_TERMINATION";

export type UploadFileParams = {
  file: File;
  type: FileType;
  user_id: string | number;
  required_information_id?: number;
  contract_id?: number;
  certificate_id?: number;
  license_id?: number;
  termination_id?: number;
  general_information_id?: number;
};

export type UploadAdminFileParams = {
  file: File;
  document_type: FileType;
  user_id: number;
  user_role: UserTypes;
  country_id?: number;
};

export interface UploadedFile {
  id: string;
  name: string;
  original_name: string;
  extension: string;
  size: string;
  type: FileType;
  created_at: string;
  updated_at: string;
  required_information_id: number;
  contract_id: number | null;
  certificate_id: number | null;
  license_id: number | null;
  termination_id: number | null;
  general_information_id: number | null;
}
