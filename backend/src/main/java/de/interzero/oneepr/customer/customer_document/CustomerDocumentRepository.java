package de.interzero.oneepr.customer.customer_document;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerDocumentRepository extends JpaRepository<CustomerDocument, Integer> {

    /**
     * Finds a CustomerDocument by its ID and fetches its associated Customer eagerly.
     * Used for permission validation where customer details are needed.
     *
     * @param id The ID of the CustomerDocument.
     * @return An Optional containing the CustomerDocument with its Customer, if found.
     */
    @Query("SELECT cd FROM CustomerDocument cd LEFT JOIN cd.customer c WHERE cd.id = :id")
    Optional<CustomerDocument> findByIdCustomer(@Param("id") Integer id);

}