"use client";

import { useState } from "react";
import { FileWithPath } from "react-dropzone";
import { enqueueSnackbar } from "notistack";
import { useMutation } from "@tanstack/react-query";

import { Button } from "@interzero/oneepr-react-ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { queryClient } from "@/lib/react-query";

import { UploadCertificateTemplateButton } from "./upload-certificate-template-button";

interface ReuploadCertificateDialogProps {
  certificateId: string | number;
  children: React.ReactNode;
}

export function ReuploadCertificateDialog({ certificateId, children }: ReuploadCertificateDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<FileWithPath>();

  const { mutate, isPending } = useMutation({
    mutationFn: () => {
      // return reuploadCertificateTemplate(certificateId, selectedFile)
      return new Promise((resolve) => setTimeout(resolve, 3000));
    },
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ["certificate", certificateId] });
      enqueueSnackbar("Certificate reuploaded successfully", { variant: "success" });
      setIsOpen(false);
    },
    onError: (error) => {
      enqueueSnackbar(error.message, { variant: "error" });
    },
  });

  async function handleUpload() {
    mutate();
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl">
        <DialogHeader>
          <DialogTitle className="font-bold sm:text-[1.75rem]">Reupload</DialogTitle>
          <DialogDescription className="text-tonal-dark-cream-20 font-normal">
            Upload a certificate to replace the current one.
          </DialogDescription>
        </DialogHeader>
        <div className="mb-12">
          <UploadCertificateTemplateButton selectedFile={selectedFile} setSelectedFile={setSelectedFile} />
        </div>
        <DialogFooter>
          <Button variant="filled" color="dark-blue" size="medium" onClick={handleUpload} disabled={isPending}>
            {isPending ? "Uploading..." : "Upload"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
