import { ApiProperty } from "@nestjs/swagger";
import { CreateCompanyContactDto } from "./create-companycontact.dto";

export class CreateCompanyDto {
  @ApiProperty({
    description: "The ID of the customer",
  })
  customer_id: number;

  @ApiProperty()
  partner_id: number;

  @ApiProperty({
    description: "The name of the company",
  })
  name: string;

  @ApiProperty({
    description: "The description of the company",
  })
  description?: string;

  @ApiProperty({
    description: "The address of the company",
  })
  address: {
    country_code: string;
    address_line: string;
    city: string;
    zip_code: string;
    street_and_number: string;
    additional_address: string;
    place_id: string;
  };

  @ApiProperty({
    description: "The VAT ID of the company",
  })
  vat: string;

  @ApiProperty({
    description: "The TIN of the company",
  })
  tin: string;

  @ApiProperty({
    description: "The Lucid ID of the company",
  })
  lucid: string;

  @ApiProperty({
    description: "The contact of the company",
  })
  contact: CreateCompanyContactDto;

  @ApiProperty({
    description: "The emails of the company",
  })
  emails?: string[];

  @ApiProperty({
    description: "The managing director of the company",
  })
  managing_director?: string;

  @ApiProperty({
    description: "The starting date of the company",
  })
  starting?: Date;

  @ApiProperty({
    description: "The website of the company",
  })
  website?: string;

  @ApiProperty({
    description: "The billing of the company",
  })
  billing?: {
    full_name: string;
    country_code: string;
    country_name: string;
    company_name: string;
    street_and_number: string;
    city: string;
    zip_code: string;
  };
}
