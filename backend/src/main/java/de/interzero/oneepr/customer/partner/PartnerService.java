package de.interzero.oneepr.customer.partner;

import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.company.CompanyRepository;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.CouponRepository;
import de.interzero.oneepr.customer.entity.*;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.file.FileService;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.market_material.MarketMaterialRepository;
import de.interzero.oneepr.customer.market_material.MarketingMaterial;
import de.interzero.oneepr.customer.market_material.MarketingMaterialService;
import de.interzero.oneepr.customer.market_material.dto.CreateMarketingMaterialParams;
import de.interzero.oneepr.customer.market_material.dto.SimpleUser;
import de.interzero.oneepr.customer.partner.dto.UpdatePartnerDto;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.time.Instant;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class PartnerService {

    private final PartnerRepository partnerRepository;

    private final PartnerBankingRepository partnerBankingRepository;

    private final CouponPartnerRepository couponPartnerRepository;

    private final CouponRepository couponRepository;

    private final MarketingMaterialPartnerRepository marketingMaterialPartnerRepository;

    private final MarketMaterialRepository marketMaterialRepository;

    private final PartnerContractRepository partnerContractRepository;

    private final CompanyRepository companyRepository;

    private final MarketingMaterialService marketingMaterialService;

    private final FileService fileService;

    /**
     * Finds a partner by ID, including all related entities:
     * banking info, contract with files and changes, companies with address and contacts,
     * coupons with uses, and marketing materials.
     *
     * @param id the ID of the partner to retrieve
     * @return the found Partner entity with all relations
     * @throws ResponseStatusException if the partner is not found
     */
    public Partner findOne(Integer id) {
        return partnerRepository.findByIdAndDeletedAtIsNullWithAllRelations(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Partner not found"));
    }


    /**
     * Finds a partner by their email address.
     * <p>
     * Mirrors the original Node.js method {@code findOneByEmail(email: string)}.
     *
     * @param email the email address to search for
     * @return the partner entity
     * @throws ResponseStatusException if email is null/blank or partner not found
     */
    @Transactional(readOnly = true)
    public Partner findOneByEmail(String email) {
        if (email == null || email.isBlank()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Bad Request");
        }

        return partnerRepository.findFirstByEmailIgnoreCaseAndDeletedAtIsNull(email)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Partner not found"));
    }

    /**
     * Updates a partner and its related entities (banking, coupons, marketing, contract, company).
     *
     * <p>Mirrors the original NestJS logic exactly. Performs all updates within a single transaction.
     * If contract or marketing material does not exist, it creates them.
     * Also updates related banking and company data, if applicable.</p>
     *
     * @param partnerId ID of the partner to update
     * @param dto       updated data for the partner
     * @param user      simplified user object with id and role (can be null for system usage)
     * @throws ResponseStatusException if the partner or related company is not found
     */
    @Transactional
    @SuppressWarnings({"java:S3776", "java:S6541"})
    public void update(Integer partnerId,
                       UpdatePartnerDto dto,
                       SimpleUser user) throws IOException {
        Partner partner = findOne(partnerId);

        String userId = user != null && user.getId() != null ? user.getId() : String.valueOf(partner.getUserId());

        String role = user != null && user.getRole() != null ? user.getRole() : Role.PARTNER.toString();

        SimpleUser usedUser = new SimpleUser(userId, role);

        if (dto.getBanking() != null) {
            PartnerBanking banking = partner.getPartnerBanking() != null ? partner.getPartnerBanking() : new PartnerBanking();
            banking.setInternationalAccountNumber(dto.getBanking().getInternationalAccountNumber());
            banking.setBusinessIdentifierCode(dto.getBanking().getBusinessIdentifierCode());
            banking.setPartner(partner);
            partnerBankingRepository.save(banking);
        }

        if (dto.getCoupons() != null && !dto.getCoupons().isEmpty()) {
            couponPartnerRepository.deleteAllByPartner_Id(partner.getId());

            List<CouponPartner> newCouponPartners = dto.getCoupons().stream().map(couponId -> {
                Coupon coupon = couponRepository.findById(couponId)
                        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Coupon not found"));
                CouponPartner cp = new CouponPartner();
                cp.setCoupon(coupon);
                cp.setPartner(partner);
                return cp;
            }).toList();

            couponPartnerRepository.saveAll(newCouponPartners);
        }

        Integer marketingMaterialIdFromPartner = partner.getMarketingMaterialPartners()
                .stream()
                .findFirst()
                .map(m -> m.getMarketingMaterial().getId())
                .orElse(null);

        if (dto.getMarketingMaterialId() != null && !dto.getMarketingMaterialId()
                .equals(marketingMaterialIdFromPartner)) {
            marketingMaterialPartnerRepository.deleteAllByPartner_Id(partner.getId());

            MarketingMaterial material = marketMaterialRepository.findById(dto.getMarketingMaterialId())
                    .orElseThrow(() -> new ResponseStatusException(
                            HttpStatus.NOT_FOUND,
                            "Marketing material not found"));

            MarketingMaterialPartner mmp = new MarketingMaterialPartner();
            mmp.setMarketingMaterial(material);
            mmp.setPartner(partner);
            marketingMaterialPartnerRepository.save(mmp);
        }

        if (dto.getNewMarketingMaterialName() != null) {
            CreateMarketingMaterialParams createDto = new CreateMarketingMaterialParams();
            createDto.setName(dto.getNewMarketingMaterialName());
            createDto.setCategory(MarketingMaterial.Category.STANDARD);
            createDto.setPartnerRestriction(MarketingMaterial.PartnerRestriction.SPECIFIC);
            createDto.setFiles(dto.getNewMarketingMaterialFiles());
            createDto.setUser(usedUser);
            createDto.setPartners(String.valueOf(partner.getId()));

            MarketingMaterial newMaterial = marketingMaterialService.create(createDto);

            marketingMaterialPartnerRepository.deleteAllByPartner_Id(partner.getId());

            MarketingMaterialPartner mmp = new MarketingMaterialPartner();
            mmp.setMarketingMaterial(newMaterial);
            mmp.setPartner(partner);
            marketingMaterialPartnerRepository.save(mmp);
        }

        if (dto.getContractFile() != null && partner.getPartnerContract() == null) {
            PartnerContract contract = new PartnerContract();
            contract.setPartner(partner);
            contract.setStatus(PartnerContract.Status.TO_BE_SIGNED);
            contract.setStartDate(Instant.now());
            partnerContractRepository.save(contract);

            CreateFileDto fileDto = new CreateFileDto();
            fileDto.setType(File.Type.PARTNER_CONTRACT);
            fileDto.setPartnerContractId(contract.getId());

            fileService.uploadFile(fileDto, dto.getContractFile(), userId, role);

        }

        if (dto.getCompany() != null) {
            Company company = partner.getCompanies()
                    .stream()
                    .filter(c -> String.valueOf(c.getId()).equals(dto.getCompany().getId()))
                    .findFirst()
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found"));

            company.setName(dto.getCompany().getName());
            company.setIndustrySector(dto.getCompany().getIndustrySector());
            company.setStarting(Instant.parse(dto.getCompany().getStartingDate()));
            company.setWebsite(dto.getCompany().getWebsite());
            company.setDescription(dto.getCompany().getDescription());
            company.setOwnerName(dto.getCompany().getOwnerName());
            company.setUpdatedAt(Instant.now());

            company.getAddress().setStreetAndNumber(dto.getCompany().getStreetAndNumber());
            company.getAddress().setCity(dto.getCompany().getCity());
            company.getAddress().setZipCode(dto.getCompany().getZipCode());
            company.getAddress().setCountryCode(dto.getCompany().getCountryCode());
            company.getAddress().setAdditionalAddress(dto.getCompany().getAdditionalAddress());
            company.getAddress()
                    .setAddressLine(dto.getCompany().getStreetAndNumber() + " " + dto.getCompany()
                            .getAdditionalAddress());
            company.getAddress().setUpdatedAt(Instant.now());

            company.getContacts().setName(dto.getCompany().getContactName());
            company.getContacts().setEmail(dto.getCompany().getContactEmail());
            company.getContacts().setPhoneMobile(dto.getCompany().getContactPhone());
            company.getContacts().setUpdatedAt(Instant.now());

            companyRepository.save(company);
        }

        partner.setFirstName(dto.getPartnerFirstname());
        partner.setLastName(dto.getPartnerLastname());
        partner.setEmail(dto.getPartnerEmail());
        partner.setCommissionMode(dto.getCommissionMode());
        partner.setPayoutCycle(dto.getPayoutCycle());
        partner.setNoProvisionNegotiated(dto.getNoProvisionNegotiated());

        partnerRepository.save(partner);
    }


}
