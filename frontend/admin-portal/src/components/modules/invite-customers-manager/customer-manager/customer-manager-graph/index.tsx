"use client";
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON> } from "recharts";

const chartData = [
  { month: "jan", Coupons: 50, Link: 36 },
  { month: "fev", Coupons: 100, <PERSON>: 55 },
  { month: "marc", Coupons: 80, <PERSON>: 37 },
  { month: "april", Coupons: 50, <PERSON>: 14 },
  { month: "may", Coupons: 70, Link: 14 },
  { month: "jun", Coupons: 50, Link: 36 },
  { month: "july", Coupons: 80, Link: 55 },
  { month: "agst", Coupons: 80, <PERSON>: 37 },
  { month: "sept", Coupons: 50, Link: 14 },
  { month: "oct", Coupons: 0, Link: 0 },
  { month: "nov", Coupons: 0, Link: 0 },
  { month: "dec", Coupons: 0, Link: 0 },
];

export function CustomerManagerChart() {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={chartData}>
        <CartesianGrid vertical={false} />
        <Tooltip />
        <YAxis tickLine={false} axisLine={false} />
        <XAxis dataKey="month" />
        <Area dataKey="Coupons" type="linear" stroke="#339933" fill="#9DE7DF" fillOpacity={0.4} />
        <Area dataKey="Link" type="linear" stroke="#FFCE00" fill="#FFEB99" fillOpacity={0.4} />
      </AreaChart>
    </ResponsiveContainer>
  );
}
