import { Controller, useFormContext, useWatch } from "react-hook-form";
import { PriceListFormData } from "../price-list-form-provider";
import { GERMANY_FRACTIONS } from "./germany";
import { FractionInput } from "@/components/ui/fraction-input";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Delete, Question } from "@interzero/oneepr-react-ui/Icon";
import { AddThresholdDialog, AddThresholdFormData } from "./add-threshold-dialog";
import { FractionIcon } from "@/components/ui/fraction-icon";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatCurrency } from "@/utils/format-currency";
import { RefreshCcwIcon } from "lucide-react";

export function PriceListFormFractions() {
  const {
    control,
    formState: { errors },
    setValue,
  } = useFormContext<PriceListFormData>();

  const thresholds = useWatch({
    control,
    name: "thresholds",
  });

  function handleDeleteThreshold(thresholdIndex: number) {
    const updatedThresholds = thresholds?.filter((_, index) => index !== thresholdIndex);

    setValue("thresholds", updatedThresholds);
  }

  function handleAddThreshold(data: AddThresholdFormData) {
    if (!thresholds) return;

    setValue("thresholds", [
      ...thresholds,
      {
        ...data,
        helper_text: data.helper_text || null,
        fractions: GERMANY_FRACTIONS.reduce(
          (acc, fraction) => {
            acc[fraction.code] = {
              code: fraction.code,
              name: fraction.name,
              value: 0,
            };
            return acc;
          },
          {} as Record<string, { code: string; name: string; value: number }>
        ),
      },
    ]);
  }

  return (
    <div className="bg-white rounded-3xl py-9 px-8">
      <div className="flex items-start justify-between">
        <h3 className="text-primary text-2xl font-bold mb-6">Price by fraction (EUR/kg)</h3>
        <Button
          variant="text"
          color="light-blue"
          size="small"
          leadingIcon={<RefreshCcwIcon className="text-support-blue size-5" />}
          type="button"
        >
          History
        </Button>
      </div>
      <div className="w-full">
        <div className="w-full flex items-stretch gap-8">
          <div className="w-64">
            <p className="text-primary text-base">Fraction Variant (KG)</p>
            {GERMANY_FRACTIONS.map((fraction) => (
              <div key={`base-${fraction.name}`} className="h-20 py-3 flex flex-col justify-center">
                <div className="flex items-center gap-3 py-2">
                  <FractionIcon size="medium" iconUrl="/assets/svg/aluminium.svg" className="bg-transparent" />
                  <p className="text-tonal-dark-cream-10">{fraction.name}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="flex items-stretch gap-8 flex-1 overflow-x-auto">
            {thresholds?.map((threshold, thresholdIndex) => (
              <div key={threshold.title} className="w-32 flex-none">
                <div className="flex items-center gap-2">
                  <Button
                    variant="text"
                    color="light-blue"
                    size="iconXSmall"
                    leadingIcon={<Delete className="fill-support-blue" />}
                    onClick={() => handleDeleteThreshold(thresholdIndex)}
                  />
                  <p className="text-primary text-base font-bold text-nowrap">{threshold.title}</p>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Question className="size-5 fill-secondary transition-all duration-300" />
                      </TooltipTrigger>
                      <TooltipContent side="bottom" className="bg-white shadow-elevation-04-1 p-5 w-64">
                        <div className="flex flex-col">
                          <div className="flex items-center justify-between">
                            <p className="text-primary text-lg font-bold">{threshold.title}</p>
                            <p className="text-primary text-lg font-bold">{formatCurrency(threshold.value)}</p>
                          </div>
                          {threshold.helper_text && (
                            <p className="text-primary text-sm py-4">{threshold.helper_text}</p>
                          )}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                {GERMANY_FRACTIONS.map((fraction) => (
                  <div key={fraction.code} className="h-20 py-3 flex justify-center items-center">
                    <Controller
                      name={`thresholds.${thresholdIndex}.fractions.${fraction.code}.value`}
                      control={control}
                      render={({ field }) => (
                        <FractionInput
                          {...field}
                          type="fraction-currency"
                          error={errors.thresholds?.[thresholdIndex]?.fractions?.[fraction.code]?.value?.message}
                        />
                      )}
                    />
                  </div>
                ))}
              </div>
            ))}
            <div className="flex-none w-32 h-full">
              <AddThresholdDialog onAdd={handleAddThreshold} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
