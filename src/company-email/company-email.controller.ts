import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { CompanyEmailService } from "./company-email.service";
import { CreateCompanyEmailDto } from "./dto/create-company-email.dto";
import { UpdateCompanyEmailDto } from "./dto/update-company-email.dto";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@ApiTags("CompanyEmail")
@Controller("CompanyEmail")
export class CompanyEmailController {
  constructor(private readonly companyEmailService: CompanyEmailService) {}

  @Post()
  @ApiOperation({ summary: "Create Company Email " })
  @ApiResponse({
    status: 201,
    description: "The CompanyEmail  has been successfully created.",
  })
  @ApiBody({ type: CreateCompanyEmailDto })
  create(@Body() createCompanyEmailDto: CreateCompanyEmailDto) {
    return this.companyEmailService.create(createCompanyEmailDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all Company Emails" })
  @ApiResponse({ status: 200, description: "List of Company Emails" })
  findAll() {
    return this.companyEmailService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Company Email  by id" })
  @ApiResponse({ status: 200, description: "The Company Email  details" })
  findOne(@Param("id") id: string) {
    return this.companyEmailService.findOne(Number(id));
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Company Email  by id" })
  @ApiResponse({
    status: 200,
    description: "The Company Email  has been successfully updated.",
  })
  @ApiBody({ type: UpdateCompanyEmailDto })
  update(@Param("id") id: string, @Body() UpdateCompanyEmailDto: UpdateCompanyEmailDto) {
    return this.companyEmailService.update(Number(id), UpdateCompanyEmailDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete CompanyEmail  by id" })
  @ApiResponse({
    status: 200,
    description: "The CompanyEmail  has been successfully deleted.",
  })
  remove(@Param("id") id: string) {
    return this.companyEmailService.remove(Number(id));
  }
}
