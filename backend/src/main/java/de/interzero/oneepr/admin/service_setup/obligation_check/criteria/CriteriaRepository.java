package de.interzero.oneepr.admin.service_setup.obligation_check.criteria;

import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.packaging_service.PackagingService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface CriteriaRepository extends JpaRepository<Criteria, Integer>, JpaSpecificationExecutor<Criteria> {

    List<Criteria> findAllByDeletedAtIsNull();

    Optional<Criteria> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * Soft-deletes all Criteria entities associated with a specific packaging service ID.
     *
     * @param packagingServiceId The ID of the parent packaging service.
     * @param now                The current timestamp to set as the deleted_at value.
     */
    @Modifying
    @Query("UPDATE Criteria c SET c.deletedAt = :now WHERE c.packagingService.id = :packagingServiceId")
    void softDeleteByPackagingServiceId(@Param("packagingServiceId") Integer packagingServiceId,
                                        @Param("now") Instant now);

    List<Criteria> findByRequiredInformation_Id(Integer requiredInformationId);

    /**
     * Finds all active Criteria for a given Country ID that match a list of types.
     * This query performs the filtering at the database level.
     *
     * @param countryId The ID of the parent Country.
     * @param types     A collection of Criteria.Type enums to filter by.
     * @return A list of matching Criteria entities.
     */
    @Query(
            "SELECT c FROM Criteria c  WHERE c.country.id = :countryId  AND c.deletedAt IS NULL  AND c.type IN :types"
    )
    List<Criteria> findActiveByCountryAndTypeIn(@Param("countryId") Integer countryId,
                                                @Param("types") Collection<Criteria.Type> types);

    List<Criteria> findByPackagingService_IdAndModeAndDeletedAtIsNull(Integer packagingServiceId,
                                                                      Criteria.Mode mode);

    List<Criteria> findByPackagingService_IdInAndDeletedAtIsNullAndModeAndTypeIn(List<Integer> packagingServiceIds,
                                                                                 Criteria.Mode mode,
                                                                                 List<Criteria.Type> types);

    @Query(
            "SELECT c FROM Criteria c  JOIN c.packagingService ps  JOIN ps.country co  WHERE co.code = :countryCode  AND c.deletedAt IS NULL  AND c.mode = :mode  AND c.type = :type"
    )
    List<Criteria> findCommitmentCriteriaByCountryAndType(@Param("countryCode") String countryCode,
                                                          @Param("mode") Criteria.Mode mode,
                                                          @Param("type") Criteria.Type type);

    /**
     * Counts the number of active (non-deleted) Criteria entities that match a specific
     * packaging service ID, criteria type, and criteria mode.
     *
     * @param packagingServiceId The ID of the associated PackagingService to filter by.
     * @param type               The {@link Criteria.Type} to filter by.
     * @param mode               The {@link Criteria.Mode} to filter by.
     * @return The total count of matching criteria.
     */
    long countByPackagingService_IdAndDeletedAtIsNullAndTypeAndMode(Integer packagingServiceId,
                                                                    Criteria.Type type,
                                                                    Criteria.Mode mode);

    /**
     * Finds all active (non-deleted) criteria of a specific type and mode that are associated
     * with any of the packaging services in the provided list.
     * <p>
     * This method is designed to efficiently retrieve criteria in bulk by using an IN clause,
     * avoiding multiple database queries for different services.
     *
     * @param services The list of {@link PackagingService} entities to check for associations.
     * @param type     The {@link Criteria.Type} to filter by (e.g., {@code Criteria.Type.REPORT_FREQUENCY}).
     * @param mode     The {@link Criteria.Mode} to filter by (e.g., {@code Criteria.Mode.COMMITMENT}).
     * @return A list of matching {@link Criteria} entities.
     */
    @Query("SELECT c FROM Criteria c WHERE c.packagingService IN :services AND c.deletedAt IS NULL AND c.type = :type AND c.mode = :mode")
    List<Criteria> findReportFrequencyCommitmentCriteriaForServices(@Param("services") List<PackagingService> services,
                                                                    @Param("type") Criteria.Type type,
                                                                    @Param("mode") Criteria.Mode mode);

    /**
     * Finds all active (non-deleted) criteria for a given country code that match a specific
     * criteria type and mode.
     *
     * @param countryCode The unique code of the country for which to retrieve criteria.
     * @param type        The {@link Criteria.Type} to filter by.
     * @param mode        The {@link Criteria.Mode} to filter by.
     * @return A list of matching {@link Criteria} entities.
     */
    @Query("SELECT c FROM Criteria c JOIN c.country country WHERE country.code = :countryCode AND c.deletedAt IS NULL AND c.type = :type AND c.mode = :mode")
    List<Criteria> findCommitmentCriteriaByCountryCode(@Param("countryCode") String countryCode,
                                                       @Param("type") Criteria.Type type,
                                                       @Param("mode") Criteria.Mode mode);

    /**
     * Finds all active (non-deleted) criteria of a specific type and mode that are associated
     * with any of the required information items in the provided list.
     * <p>
     * This method is designed to efficiently retrieve criteria in bulk by using an IN clause,
     * avoiding multiple database queries for different required information entities.
     *
     * @param reqInfos The list of {@link RequiredInformation} entities to check for associations.
     * @param type     The {@link Criteria.Type} to filter by.
     * @param mode     The {@link Criteria.Mode} to filter by.
     * @return A list of matching {@link Criteria} entities.
     */
    @Query("SELECT c FROM Criteria c WHERE c.requiredInformation IN :reqInfos AND c.deletedAt IS NULL AND c.type = :type AND c.mode = :mode")
    List<Criteria> findCommitmentCriteriaForRequiredInformations(@Param("reqInfos") List<RequiredInformation> reqInfos,
                                                                 @Param("type") Criteria.Type type,
                                                                 @Param("mode") Criteria.Mode mode);

    /**
     * Finds all active (non-deleted) criteria for a specific country code and mode,
     * eagerly fetching the associated options for each criterion.
     * <p>
     * This method uses a {@code LEFT JOIN FETCH} to ensure that the {@code options} collection
     * of each returned {@code Criteria} object is fully initialized in a single query,
     * preventing potential N+1 performance issues.
     *
     * @param countryCode The unique code of the country to filter by.
     * @param mode        The {@link Criteria.Mode} to filter by (e.g., COMMITMENT, CALCULATOR).
     * @return A list of {@link Criteria} entities with their associated {@code options} collections
     * eagerly loaded.
     */
    @Query(
            "SELECT c FROM Criteria c LEFT JOIN FETCH c.options o JOIN c.country co  WHERE co.code = :countryCode AND c.mode = :mode AND c.deletedAt IS NULL"
    )
    List<Criteria> findByCountryCodeAndModeWithWithOptions(@Param("countryCode") String countryCode,
                                                           @Param("mode") Criteria.Mode mode);

    /**
     * Finds all active (non-deleted) criteria for a given mode and country code,
     * eagerly fetching their associated options collection.
     * <p>
     * This method uses a {@code LEFT JOIN FETCH} to ensure that the {@code options}
     * collection of each returned {@code Criteria} object is fully initialized in a
     * single query, preventing potential N+1 performance issues when the options
     * are accessed later.
     *
     * @param mode        The {@link Criteria.Mode} to filter by (e.g., COMMITMENT, CALCULATOR).
     * @param countryCode The unique code of the country for which to retrieve criteria.
     * @return A list of matching {@link Criteria} entities with their {@code options}
     * collections eagerly loaded.
     */
    @Query("SELECT c FROM Criteria c LEFT JOIN FETCH c.options WHERE c.mode = :mode AND c.country.code = :countryCode AND c.deletedAt IS NULL")
    List<Criteria> findByModeAndCountry_CodeAndDeletedAtIsNull(@Param("mode") Criteria.Mode mode,
                                                               @Param("countryCode") String countryCode);

    /**
     * Finds all active (non-deleted) criteria for a specific mode and country code,
     * eagerly fetching their associated {@code options} and {@code requiredInformation} relations.
     * <p>
     * This method uses {@code LEFT JOIN FETCH} to load all required relations in a single
     * database query, which is crucial for preventing N+1 performance issues when these
     * related entities are accessed later.
     *
     * @param mode        The {@link Criteria.Mode} to filter by (e.g., COMMITMENT, CALCULATOR).
     * @param countryCode The unique code of the country for which to retrieve criteria.
     * @return A list of {@link Criteria} entities with their {@code options} and
     * {@code requiredInformation} relations fully initialized.
     */
    @Query(
            "SELECT c FROM Criteria c  LEFT JOIN FETCH c.options  LEFT JOIN FETCH c.requiredInformation  WHERE c.mode = :mode AND c.country.code = :countryCode AND c.deletedAt IS NULL"
    )
    List<Criteria> findByModeAndCountryCodeWithRelations(@Param("mode") Criteria.Mode mode,
                                                         @Param("countryCode") String countryCode);

    List<Criteria> findByObligationCheckSection_IdAndDeletedAtIsNull(Long id);
}