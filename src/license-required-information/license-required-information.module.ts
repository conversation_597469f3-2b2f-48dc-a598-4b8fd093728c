import { Module } from "@nestjs/common";
import { LicenseRequiredInformationController } from "./license-required-information.controller";
import { LicenseRequiredInformationService } from "./license-required-information.service";
import { CustomerIoModule } from "@/customer-io/customer-io.module";
import { HttpApiModule } from "@/http/http.module";

@Module({
  imports: [CustomerIoModule, HttpApiModule],
  controllers: [LicenseRequiredInformationController],
  providers: [LicenseRequiredInformationService],
})
export class LicenseRequiredInformationModule {}
