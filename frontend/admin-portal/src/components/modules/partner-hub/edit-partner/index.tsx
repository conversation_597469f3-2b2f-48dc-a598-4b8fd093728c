"use client";

import { AutoComplete } from "@/components/common/autocomplete";
import { CountrySelect } from "@/components/common/country-select";
import { Divider } from "@/components/common/divider";
import { ModuleContent } from "@/components/common/module-content";
import { ModuleTitle } from "@/components/common/module-title";
import { PhoneInput } from "@/components/common/phone-input";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CheckboxInput } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { getAllCoupons } from "@/lib/api/coupon";
import { Coupon } from "@/lib/api/coupon/types";
import { getAllMarketingMaterials } from "@/lib/api/marketing-materials";
import { getPart<PERSON>, update<PERSON>art<PERSON> } from "@/lib/api/partner";
import { cn } from "@/utils/cn";
import { dateManager } from "@/utils/date-manager";
import { formatCurrency } from "@/utils/format-currency";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { AdsClick, Delete, East, KeyboardArrowLeft, LocalOffer } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2Icon } from "lucide-react";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z
  .object({
    companyName: z.string({ required_error: "Required field" }).min(1, { message: "Required field" }),
    industrySector: z.string({ required_error: "Required field" }).min(1, { message: "Required field" }),
    startingDateOfCooperation: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" })
      .refine(
        (val) => {
          const date = new Date(val);
          return !isNaN(date.getTime());
        },
        { message: "Invalid date" }
      ),
    partnerOfferWebsite: z
      .string({
        required_error: "Required field",
      })
      .url({ message: "Invalid URL" }),
    description: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    directorOrCeoName: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    country: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    city: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    zipCode: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    streetAndNumber: z
      .string({
        required_error: "Required field",
      })
      .min(1, { message: "Required field" }),
    additionalAddressLine: z.string().optional(),
    contact: z.object({
      name: z.string({
        required_error: "Required field",
      }),
      email: z
        .string({
          required_error: "Required field",
        })
        .email({ message: "Invalid email" }),
      phone: z
        .string({
          required_error: "Required field",
        })
        .min(1, { message: "Required field" }),
    }),
    marketingMaterial: z
      .object({
        id: z.number().optional(),
        isNewMarketingMaterial: z.boolean().default(false),
      })
      .optional(),
    noProvisionNegotiated: z.boolean().default(false),
    commissionTerms: z
      .object({
        payoutCicle: z.string().optional().or(z.literal("")),
        commissionMode: z.string().optional().or(z.literal("")),
        // commissionAmount: z.coerce.number(),
      })
      .optional(),
    discounts: z.array(
      z.object({
        id: z.string(),
        code: z.string(),
        coupon: z.any(),
      })
    ),
  })
  .refine(
    (data) => {
      if (
        !data.noProvisionNegotiated &&
        (!data.commissionTerms?.payoutCicle || !data.commissionTerms?.commissionMode)
        // || !data.commissionTerms?.commissionAmount
      ) {
        return false;
      }
      return true;
    },
    {
      message: "Required field",
      path: ["commissionTerms.payoutCicle", "commissionTerms.commissionMode"],
    }
  );

type CreatePartnerParams = z.infer<typeof formSchema>;

export function EditPartnerModule({ partnerId }: { partnerId: number }) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const [formStep, setFormStep] = useState<number>(1);

  const { data: dataPartner, isLoading: isLoadingPartner } = useQuery({
    queryKey: ["partner", partnerId],
    queryFn: async () => {
      const response = await getPartner(partnerId);

      form.reset({
        companyName: response?.companies?.[0].name,
        industrySector: response?.companies?.[0].industry_sector,
        startingDateOfCooperation: response?.companies?.[0].starting
          ? new Date(response?.companies?.[0].starting).toISOString().split("T")[0]
          : "",
        partnerOfferWebsite: response?.companies?.[0].website,
        description: response?.companies?.[0].description,
        directorOrCeoName: response?.companies?.[0].owner_name,
        country: response?.companies?.[0].address.country_code,
        city: response?.companies?.[0].address.city,
        zipCode: response?.companies?.[0].address.zip_code,
        streetAndNumber: response?.companies?.[0].address.street_and_number,
        additionalAddressLine: response?.companies?.[0].address.additional_address,
        contact: {
          name: response?.companies?.[0].contacts?.name,
          email: response?.companies?.[0].contacts?.email,
          phone: response?.companies?.[0].contacts?.phone_mobile,
        },
        discounts: response?.coupons?.map((coupon) => ({
          id: coupon.id.toString(),
          code: coupon.coupon.code,
          coupon: coupon,
        })),
        commissionTerms: {
          payoutCicle: response?.payout_cycle || "",
          commissionMode: response?.commission_mode || "",
          // commissionAmount: (response?.commission_amount ?? 0) / 100,
        },
        marketingMaterial: {
          id: response?.marketing_material_partners?.[0]?.marketing_material_id,
          isNewMarketingMaterial: false,
        },
        noProvisionNegotiated: response?.no_provision_negotiated || false,
      });
      return response;
    },
  });

  const form = useForm<CreatePartnerParams>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onBlur",
    mode: "onBlur",
    disabled: isLoadingPartner,
  });

  const handleNextStep = async () => {
    const isValid = await form.trigger();
    if (!isValid && Object.keys(form.formState.errors).length) {
      if (form.formState.errors.commissionTerms) {
        setFormStep(2);
        return;
      }
      const firstError = Object.keys(form.formState.errors)[0] as keyof z.infer<typeof formSchema>;
      form.setFocus(firstError);
      const errorRef = form.formState.errors[firstError]?.ref;
      if (errorRef && "current" in errorRef) {
        (errorRef as { current: HTMLElement }).current?.scrollIntoView();
      }
      return;
    }
    setFormStep(2);
  };

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreatePartnerParams) => {
      await updatePartner(partnerId, {
        company: {
          id: dataPartner?.companies?.[0]?.id,
          name: data.companyName,
          industry_sector: data.industrySector,
          starting_date: dateManager(data.startingDateOfCooperation).toDate().toISOString(),
          website: data.partnerOfferWebsite,
          description: data.description,
          owner_name: data.directorOrCeoName,
          country_code: data.country,
          city: data.city,
          zip_code: data.zipCode,
          street_and_number: data.streetAndNumber,
          additional_address_line: data.additionalAddressLine,
          contact_name: data.contact.name,
          contact_email: data.contact.email,
          contact_phone: data.contact.phone,
        },
        partner_firstname: data.directorOrCeoName.split(" ")[0],
        partner_lastname: data.directorOrCeoName.split(" ")[1],
        commission_mode: data.commissionTerms?.commissionMode,
        no_provision_negotiated: data.noProvisionNegotiated,
        payout_cycle: data.commissionTerms?.payoutCicle || "",
        coupons: data.discounts?.map((discount) => Number(discount.id)) ?? undefined,
        marketing_material_id: data.marketingMaterial?.id,
      });
    },
    onSuccess: () => {
      enqueueSnackbar("Partner created successfully", { variant: "success" });
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      router.push("/partner-hub");
    },
    onError: (error) => {
      // TODO: Remove, it could log sensitive data in production
      // eslint-disable-next-line no-console
      console.error(error);
      enqueueSnackbar("Failed to create partner", { variant: "error" });
    },
  });

  function onSubmit(data: CreatePartnerParams) {
    mutate(data);
  }

  const [searchCampaign, setSearchCampaign] = useState<string>("");
  const { data: marketingMaterials, isLoading: isLoadingMarketingMaterials } = useQuery({
    queryKey: ["marketingMaterials"],
    queryFn: async () => {
      const response = await getAllMarketingMaterials({
        name: searchCampaign,
      });
      return response.marketingMaterials.map((material) => ({
        label: material.name,
        value: material.id.toString(),
      }));
    },
  });

  const [searchCode, setSearchCode] = useState<string>("");
  const [currentSelectedCoupon, setCurrentSelectedCoupon] = useState<string>("");
  const { data: coupons, isLoading: isLoadingCoupons } = useQuery({
    queryKey: ["coupons", searchCode],
    queryFn: async () => {
      const response = await getAllCoupons({ code: searchCode });
      return response.coupons.map((coupon) => ({
        label: coupon.code,
        value: coupon.id.toString(),
        coupon,
      }));
    },
  });

  return (
    <ModuleContent containerClassName="bg-white">
      <div className="flex items-start justify-between">
        <ModuleTitle
          title="Edit Partner"
          description="Edit the partner information, marketing material and commission."
        />
      </div>
      {isLoadingPartner && (
        <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
          <div className="flex flex-col gap-6">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-80" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-12 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
            <Divider className="my-4" initialMarginDisabled />
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-12 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-12 w-full" />
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 mt-6">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-6 w-24" />
            </div>
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-6 w-48" />
          </div>
          <div className="flex items-center w-full justify-end gap-6">
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      )}
      {!isLoadingPartner && formStep === 1 && (
        <>
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
            <p className="text-2xl font-bold text-primary">Company Information</p>
            <Controller
              control={form.control}
              name="companyName"
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label="Company Name"
                  placeholder="Enter company name"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                />
              )}
            />
            <Controller
              control={form.control}
              name="industrySector"
              render={({ field, fieldState: { error } }) => (
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <SelectGroup>
                    <SelectLabel className="text-primary text-base font-normal pl-0">Industry Sector</SelectLabel>
                    <SelectTrigger invalid={!!error}>
                      <SelectValue placeholder="Select industry sector" />
                    </SelectTrigger>
                  </SelectGroup>
                  <SelectContent defaultValue="AHK / IHK">
                    <SelectItem value="AHK / IHK">AHK / IHK</SelectItem>
                    <SelectItem value="Association">Association</SelectItem>
                    <SelectItem value="E-Commerce">E-Commerce</SelectItem>
                    <SelectItem value="Fulfilment">Fulfilment</SelectItem>
                    <SelectItem value="Logistics">Logistics</SelectItem>
                    <SelectItem value="Marketplaces">Marketplaces</SelectItem>
                    <SelectItem value="Consulting">Consulting</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                  {error && <p className="text-error text-sm block -mt-2">{error.message}</p>}
                </Select>
              )}
            />
            <Controller
              control={form.control}
              name="startingDateOfCooperation"
              render={({ field, fieldState: { error } }) => (
                <div className="w-full max-w-80">
                  <Input
                    {...field}
                    label="Starting Date of Cooperation"
                    type="date"
                    placeholder="Select date"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                </div>
              )}
            />
            <Controller
              control={form.control}
              name="partnerOfferWebsite"
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label="Partner Offer Website"
                  placeholder="Enter website URL"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                />
              )}
            />
            <Controller
              control={form.control}
              name="description"
              render={({ field, fieldState: { error } }) => (
                <Textarea
                  {...field}
                  label="Description"
                  placeholder="Enter description"
                  className="h-20"
                  errorMessage={error?.message}
                />
              )}
            />
            <Controller
              control={form.control}
              name="directorOrCeoName"
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label="Director or CEO Name"
                  placeholder="Enter name"
                  variant={error ? "error" : "default"}
                  errorMessage={error?.message}
                />
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <Controller
                control={form.control}
                name="country"
                render={({ field, fieldState: { error } }) => (
                  <div className="flex flex-col gap-2">
                    <label htmlFor="country" className="text-primary">
                      Country
                    </label>
                    <CountrySelect
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Select country"
                      errorMessage={error?.message}
                    />
                  </div>
                )}
              />
              {/* <Controller
                control={form.control}
                name="federalState"
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label="Federal State"
                    placeholder="Enter federal state"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                )}
              /> */}
              <Controller
                control={form.control}
                name="city"
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label="City"
                    placeholder="Enter city"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                )}
              />
              <Controller
                control={form.control}
                name="zipCode"
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label="Zip Code"
                    placeholder="Enter zip code"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                )}
              />
              <Controller
                control={form.control}
                name="streetAndNumber"
                render={({ field, fieldState: { error } }) => (
                  <Input
                    {...field}
                    label="Street and Number"
                    placeholder="Enter street and number"
                    variant={error ? "error" : "default"}
                    errorMessage={error?.message}
                  />
                )}
              />
              <Controller
                control={form.control}
                name="additionalAddressLine"
                render={({ field }) => (
                  <Input {...field} label="Additional Address Line" placeholder="Enter additional address line" />
                )}
              />
            </div>
            <Divider className="my-4" initialMarginDisabled />
            <p className="text-2xl font-bold text-tonal-dark-cream-30">Contact</p>
            <Controller
              control={form.control}
              name="contact.name"
              render={({ field, fieldState: { error } }) => (
                <Input
                  {...field}
                  label="Contact Name"
                  placeholder="Enter contact name"
                  variant={error ? "error" : "default"}
                />
              )}
            />
            <div className="grid grid-cols-2 gap-4">
              <Controller
                control={form.control}
                name="contact.email"
                render={({ field, fieldState: { error } }) => (
                  <Input {...field} label="Email" placeholder="Enter email" variant={error ? "error" : "default"} />
                )}
              />
              <Controller
                control={form.control}
                name="contact.phone"
                render={({ field, fieldState: { error } }) => (
                  <div className="flex flex-col gap-2">
                    <label htmlFor="phone" className="text-primary">
                      Phone
                    </label>
                    <PhoneInput
                      id="phone"
                      name="phone"
                      placeholder="Enter phone"
                      valueSetter={field.onChange}
                      errorSetter={field.onBlur}
                      defaultValue={form.watch("contact.phone")}
                      isError={!!error}
                    />
                  </div>
                )}
              />
            </div>
          </div>
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 mt-6">
            <p className="text-2xl font-bold text-primary">
              Marketing Material
              <span className="ml-4 text-base italic font-normal text-tonal-dark-cream-30">Optional</span>
            </p>
            <AutoComplete
              items={marketingMaterials ?? []}
              onChange={(value) => {
                form.setValue("marketingMaterial.id", Number(value));
              }}
              onSearchValueChange={setSearchCampaign}
              searchValue={searchCampaign}
              value={form.watch("marketingMaterial.id")?.toString() ?? ""}
              isLoading={isLoadingMarketingMaterials}
              placeholder="Search"
              label="Search for campaign"
            />
            <CheckboxInput
              label="New Campaign"
              checked={form.watch("marketingMaterial.isNewMarketingMaterial")}
              onChange={(e) => {
                form.setValue("marketingMaterial.isNewMarketingMaterial", e.target.checked);
              }}
            />
          </div>
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
            <p className="text-2xl font-bold text-primary">Partnership commission</p>
            <Controller
              control={form.control}
              name="noProvisionNegotiated"
              render={({ field }) => (
                <CheckboxInput label="No provision negotiated" checked={field.value} onChange={field.onChange} />
              )}
            />
          </div>
          <div className="flex items-center w-full justify-end gap-6">
            <Button
              type="button"
              color="dark-blue"
              size="medium"
              variant="outlined"
              onClick={() => {
                router.back();
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              color="yellow"
              size="medium"
              variant="filled"
              trailingIcon={form.formState.isSubmitting ? <Loader2Icon className="animate-spin" /> : <East />}
              disabled={form.formState.isSubmitting}
              onClick={handleNextStep}
            >
              Next Step
            </Button>
          </div>
        </>
      )}
      {!isLoadingPartner && formStep === 2 && (
        <>
          <Button
            type="button"
            color="light-blue"
            size="medium"
            variant="text"
            onClick={() => setFormStep(1)}
            className="mb-4"
            leadingIcon={<KeyboardArrowLeft />}
          >
            Back
          </Button>
          {!form.watch("noProvisionNegotiated") && (
            <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10">
              <p className="text-2xl font-bold text-primary">Commission Terms</p>
              <div className="grid grid-cols-2 gap-4">
                <Controller
                  control={form.control}
                  name="commissionTerms.payoutCicle"
                  render={({ field, fieldState }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectGroup>
                        <SelectLabel className="text-primary text-base font-normal pl-0">Payout Cycle</SelectLabel>
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select payout cycle" />
                        </SelectTrigger>
                      </SelectGroup>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                <Controller
                  control={form.control}
                  name="commissionTerms.commissionMode"
                  render={({ field, fieldState }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectGroup>
                        <SelectLabel className="text-primary text-base font-normal pl-0">Commission Mode</SelectLabel>
                        <SelectTrigger invalid={!!fieldState.error}>
                          <SelectValue placeholder="Select commission mode" />
                        </SelectTrigger>
                      </SelectGroup>
                      <SelectContent>
                        <SelectItem value="yearly">Yearly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {/* <Controller
                  control={form.control}
                  name="commissionTerms.commissionAmount"
                  render={({ field, fieldState }) => (
                    <Input
                      {...field}
                      type="number"
                      label="Commission Amount"
                      placeholder="In percentage"
                      variant={fieldState.error ? "error" : "default"}
                      className="col-span-2"
                    />
                  )}
                /> */}
              </div>
            </div>
          )}
          <div className="flex flex-col gap-6 rounded-[40px] bg-surface-02 p-10 my-12">
            <p className="text-2xl font-bold text-primary">Atribute Discount</p>
            <AutoComplete
              items={coupons ?? []}
              onChange={setCurrentSelectedCoupon}
              onSearchValueChange={setSearchCode}
              searchValue={searchCode}
              value={currentSelectedCoupon}
              isLoading={isLoadingCoupons}
              placeholder="Search for discounts"
              hasError={!form.watch("discounts")?.length}
            />
            <div className="flex items-center justify-end gap-2">
              <Button
                type="button"
                color="dark-blue"
                size="small"
                variant="outlined"
                onClick={() => {
                  setCurrentSelectedCoupon("");
                  setSearchCode("");
                  form.setValue("discounts", []);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                color="dark-blue"
                size="small"
                variant="filled"
                onClick={() => {
                  if (!currentSelectedCoupon) return;
                  const coupon = coupons?.find((coupon) => coupon.value === currentSelectedCoupon);
                  if (!coupon) return;
                  const isAlreadyAdded = form
                    .getValues("discounts")
                    ?.some((discount) => discount.code === coupon.label);
                  if (isAlreadyAdded) return;
                  form.setValue("discounts", [
                    ...(form.getValues("discounts") ?? []),
                    { id: coupon.value, code: coupon.label, coupon: coupon.coupon },
                  ]);
                  setCurrentSelectedCoupon("");
                  setSearchCode("");
                }}
              >
                Apply Coupon
              </Button>
            </div>
            {form.watch("discounts")?.map((discount: { id: string; code: string; coupon?: Coupon }) => {
              const elegibleProducts = discount.coupon?.elegible_products
                ? (Object.keys(discount.coupon.elegible_products) as ["direct_license", "eu_license", "action_guide"])
                : undefined;

              return (
                <Accordion type="single" collapsible key={discount.id}>
                  <AccordionItem value={discount.id}>
                    <AccordionTrigger
                      className={cn(
                        "bg-white rounded-2xl px-4 w-full aria-expanded:rounded-t-2xl aria-expanded:rounded-b-none"
                      )}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <span className="text-tonal-dark-green-30 text-base uppercase">{discount.code}</span>
                          <LocalOffer className="fill-tonal-dark-green-30 size-6" />
                          <AdsClick className="fill-tonal-dark-green-30 size-6" />
                        </div>
                        <Button
                          type="button"
                          color="light-blue"
                          size="iconMedium"
                          variant="text"
                          leadingIcon={<Delete className="fill-primary" />}
                          onClick={(e) => {
                            e.stopPropagation();
                            const formValue = form.getValues("discounts") ?? [];
                            form.setValue(
                              "discounts",
                              formValue.filter((d) => d.id !== discount.id)
                            );
                          }}
                        />
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="bg-white rounded-b-2xl px-4">
                      <Divider initialMarginDisabled className="mb-4" />
                      <div className="flex flex-col gap-4">
                        <div className="flex items-center gap-2">
                          <p className="text-primary font-medium text-base">Period:</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">Starts:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.start_date
                              ? dateManager(discount.coupon.start_date).format("DD.MM.YYYY")
                              : "-"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">End:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.end_date
                              ? dateManager(discount.coupon.end_date).format("DD.MM.YYYY")
                              : "-"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-primary font-medium text-base">Parameters:</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">Maximum order value:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.max_amount ? formatCurrency(discount.coupon.max_amount) : "-"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">Minimum order value:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.min_amount ? formatCurrency(discount.coupon.min_amount) : "-"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">Number of vouchers:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.max_uses ? discount.coupon.max_uses : "-"}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-tonal-dark-cream-20 font-medium text-base">Commission amount:</p>
                          <p className="text-primary text-base">
                            {discount.coupon?.commission_percentage ? `${discount.coupon.commission_percentage}%` : "-"}
                          </p>
                        </div>
                        {elegibleProducts?.length && (
                          <>
                            <div className="flex items-center gap-2">
                              <p className="text-primary text-base">Products Included:</p>
                            </div>
                            <div className="flex flex-col gap-2">
                              <CheckboxInput
                                label="EU complaince service licesing"
                                checked={elegibleProducts.includes("eu_license")}
                                disabled
                              />
                              <CheckboxInput
                                label="DE direct licensing"
                                checked={elegibleProducts.includes("direct_license")}
                                disabled
                              />
                              <CheckboxInput
                                label="Action Guide"
                                checked={elegibleProducts.includes("action_guide")}
                                disabled
                              />
                            </div>
                          </>
                        )}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              );
            })}
          </div>
          <div className="flex items-center w-full justify-end gap-6">
            <Button
              type="button"
              color="dark-blue"
              size="medium"
              variant="outlined"
              onClick={() => {
                setFormStep(1);
              }}
            >
              Cancel
            </Button>
            <Button
              type="button"
              color="yellow"
              size="medium"
              variant="filled"
              disabled={isPending}
              {...(isPending && {
                leadingIcon: <Loader2Icon className="animate-spin" />,
              })}
              onClick={async () => {
                const isValid = await form.trigger();
                if (isValid) {
                  onSubmit(form.getValues());
                }
              }}
            >
              Edit Partner
            </Button>
          </div>
        </>
      )}
    </ModuleContent>
  );
}
