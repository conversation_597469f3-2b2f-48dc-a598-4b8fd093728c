"use client";

import { useForm } from "react-hook-form";
import { Form, FormControl, FormField, FormItem } from "../../../ui/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useEffect, useState } from "react";
import { RadioGroup, RadioGroupItem } from "../../../ui/radio-group";
import { Label } from "../../../ui/label";

export enum MyCompanyFormModes {
  CREATE = "CREATE",
  UPDATE = "UPDATE",
  VIEW = "VIEW",
}

interface MyCompanyFormProps {
  mode: MyCompanyFormModes;
  company?: {
    companyName: string;
    registrationNumber: string;
    vatNumber?: string;
    tax?: string;
    address: string;
    city: string;
    contactName: string;
    email: string;
    phoneNumber?: string;
  };
  handleCancelButton?: () => void;
  handleEditButton?: () => void;
  onSubmit: (values: z.infer<typeof formSchema>) => void;
}

const formSchema = z.object({
  companyName: z.string(),
  registrationNumber: z.string(),
  vatNumber: z.string().optional(),
  tax: z.string().optional(),
  address: z.string(),
  city: z.string(),
  contactName: z.string(),
  email: z.string().email(),
  phoneNumber: z.string().optional(),
});

export function MyCompanyForm({ mode, company, handleCancelButton, onSubmit, handleEditButton }: MyCompanyFormProps) {
  const [isFormValid, setIsFormValid] = useState(false);
  const [selectedField, setSelectedField] = useState<string | null>(null);
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: company ?? undefined,
  });

  const formValues = form.watch();

  useEffect(() => {
    const { vatNumber, tax, ...otherFields } = formValues;

    const vatOrTaxFilled = Boolean(vatNumber?.trim()) || Boolean(tax?.trim());

    const allOtherFieldsFilled = Object.values(otherFields).every((val) => val && val.trim() !== "");

    setIsFormValid(vatOrTaxFilled && allOtherFieldsFilled);
  }, [formValues, isFormValid]);

  return (
    <>
      {mode === MyCompanyFormModes.CREATE || mode === MyCompanyFormModes.UPDATE ? (
        <>
          <div className="bg-white mt-10 p-8 rounded-3xl">
            <div>
              <h3 className="text-primary text-2xl font-bold mb-1">New company</h3>
              <p className="text-[#808FA9] text-sm mb-5">* Mandatory Fields</p>
            </div>
            <Form {...form}>
              <RadioGroup onValueChange={(value) => setSelectedField(value)} className="">
                <form onSubmit={form.handleSubmit(onSubmit)} className="grid grid-cols-2 gap-8">
                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="Company name *"
                            placeholder="Company name"
                            {...field}
                            variant={errors.companyName ? "error" : "default"}
                            errorMessage={errors.companyName?.message}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="registrationNumber"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="Registration number *"
                            placeholder="Registration number"
                            {...field}
                            variant={errors.registrationNumber ? "error" : "default"}
                            errorMessage={errors.registrationNumber?.message}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="VAT" id="option-one" />
                      <Label htmlFor="option-one" className="text-[#002652]">
                        VAT-ID
                      </Label>
                    </div>
                    <FormField
                      control={form.control}
                      name="vatNumber"
                      render={({ field, formState: { errors } }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              variant={errors.vatNumber ? "error" : "default"}
                              errorMessage={errors.registrationNumber?.message}
                              placeholder="VAT-ID"
                              enabled={selectedField === "VAT"}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="TAX" id="option-one" />
                      <Label htmlFor="option-one" className="text-[#002652]">
                        TAX
                      </Label>
                    </div>
                    <FormField
                      control={form.control}
                      name="tax"
                      render={({ field, formState: { errors } }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              variant={errors.registrationNumber ? "error" : "default"}
                              placeholder="TAX"
                              errorMessage={errors.registrationNumber?.message}
                              enabled={selectedField === "TAX"}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="Address *"
                            placeholder="Address"
                            {...field}
                            variant={errors.address ? "error" : "default"}
                            errorMessage={errors.address?.message}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="City *"
                            placeholder="City"
                            {...field}
                            variant={errors.city ? "error" : "default"}
                            errorMessage={errors.city?.message}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="contactName"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="Contact name *"
                            placeholder="Contact name"
                            {...field}
                            variant={errors.contactName ? "error" : "default"}
                            errorMessage={errors.contactName?.message}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="E-mail *"
                            placeholder="@email.com"
                            {...field}
                            variant={errors.email ? "error" : "default"}
                            errorMessage={errors.email?.message}
                            disabled={true}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="phoneNumber"
                    render={({ field, formState: { errors } }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            label="Phone number *"
                            placeholder="Phone number"
                            {...field}
                            variant={errors.phoneNumber ? "error" : "default"}
                            errorMessage={errors.phoneNumber?.message}
                            disabled
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </form>
              </RadioGroup>
            </Form>
          </div>
          <div className="flex items-center justify-end gap-6 mt-12">
            <Button
              type="button"
              onClick={handleCancelButton}
              variant="outlined"
              color="dark-blue"
              size="medium"
              className="px-24"
            >
              Cancel
            </Button>
            <Button
              disabled={!isFormValid}
              onClick={() => onSubmit(form.getValues())}
              variant="filled"
              color="yellow"
              size="medium"
              className="px-24"
            >
              {form.formState.isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </>
      ) : (
        <div className="bg-white my-10 p-6 rounded-3xl">
          <section className="flex items-center justify-between pb-6">
            <h3 className="text-primary text-2xl font-bold mb-1">Company information</h3>
            <Button onClick={handleEditButton} variant="filled" color="dark-blue" size="medium" className="px-4 py-2">
              Edit
            </Button>
          </section>
          <div className="grid grid-cols-2 gap-3">
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">Company name</p>
              <p className="text-primary text-lg font-[400]">{company?.companyName}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">Registration number</p>
              <p className="text-primary text-lg font-[400]">{company?.registrationNumber}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">VAT-ID</p>
              <p className="text-primary text-lg font-[400]">{company?.vatNumber ?? "---"}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">Tax</p>
              <p className="text-primary text-lg font-[400]">{company?.tax ?? "---"}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">Address</p>
              <p className="text-primary text-lg font-[400]">{company?.address}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">City</p>
              <p className="text-primary text-lg font-[400]">{company?.city}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">ContactName</p>
              <p className="text-primary text-lg font-[400]">{company?.contactName}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">E-mail</p>
              <p className="text-primary text-lg font-[400]">{company?.email}</p>
            </div>
            <div className="flex flex-col items-start justify-start">
              <p className="text-on-surface-01">Phone number</p>
              <p className="text-primary text-lg font-[400]">{company?.phoneNumber}</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
