import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { Popover, PopoverClose, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useState } from "react";
import { cn } from "@/utils/cn";
import { useQuery } from "@tanstack/react-query";
import { getDiscountLinksUsage } from "@/lib/api/partner";
import { CustomDropdown } from "@/components/common/custom-dropdown";
import { Skeleton } from "@/components/ui/skeleton";

export function DiscountsAndLinks() {
  const [filter, setFilter] = useState<"discount" | "link">("discount");
  const years = Array.from({ length: 9 }, (_, i) => new Date().getFullYear() - 4 + i);
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());

  const { data: discountsAndLinks, isLoading: isDiscountsAndLinksLoading } = useQuery({
    queryKey: ["discounts-and-links", selectedYear],
    queryFn: () => getDiscountLinksUsage(selectedYear.toString()),
  });

  return (
    <div className="w-full py-5 px-4 gap-6 rounded-xl bg-white">
      <div className="flex items-center justify-between">
        <p className="text-2xl text-tonal-red-50 font-bold">Discount/Links used</p>
        <CustomDropdown
          selectedOption={selectedYear}
          options={years.map((year) => ({
            label: year.toString(),
            value: year.toString(),
          }))}
          handleSelect={setSelectedYear}
        />
      </div>
      <p className="text-tonal-dark-cream-60">TOP 5 Partners</p>
      <Popover>
        <PopoverTrigger className="flex items-center gap-2 mt-6 w-36">
          <div className={cn("h-4 w-4 rounded-full", filter === "discount" ? "bg-tonal-red-50" : "bg-tonal-pink-80")} />
          <p className="font-bold text-tonal-dark-cream-10">{filter === "discount" ? "Discount" : "Link"}</p>
          <KeyboardArrowDown className="fill-primary h-5 w-5" />
        </PopoverTrigger>
        <PopoverContent
          side="bottom"
          className="w-60 overflow-hidden shadow-elevation-04-1 border-none p-0 rounded-2xl"
        >
          <PopoverClose asChild>
            <Button
              variant="text"
              size="medium"
              color="dark-blue"
              className="w-full flex justify-start p-4 py-6 rounded-none"
              leadingIcon={<div className="h-4 w-4 rounded-full bg-tonal-red-50 mt-[2px]" />}
              onClick={() => setFilter("discount")}
            >
              Discount
            </Button>
          </PopoverClose>
          <PopoverClose asChild>
            <Button
              variant="text"
              size="medium"
              color="dark-blue"
              className="w-full flex justify-start p-4 py-6 rounded-none"
              leadingIcon={<div className="h-4 w-4 rounded-full bg-tonal-pink-80 mt-[2px]" />}
              onClick={() => setFilter("link")}
            >
              Link
            </Button>
          </PopoverClose>
        </PopoverContent>
      </Popover>
      {isDiscountsAndLinksLoading ? (
        <div className="grid grid-cols-5 gap-4 w-full h-[300px] mt-6 ml-4">
          {[...Array(5)].map((_, index) => (
            <Skeleton key={index} className="h-full w-10" />
          ))}
        </div>
      ) : (
        <div className="w-full mt-6 -ml-8">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={discountsAndLinks?.partners ?? []} barSize={40}>
              <CartesianGrid strokeDasharray="0" vertical={false} />
              <XAxis dataKey="partner_name" tick={{ fontSize: 12 }} />
              <YAxis domain={[0, 100]} tick={{ fontSize: 12 }} />
              <Tooltip />
              {filter === "discount" && <Bar dataKey="discount_uses" fill="#E64330" radius={[6, 6, 0, 0]} />}
              {filter === "link" && <Bar dataKey="link_uses" fill="#FAB5A2" radius={[6, 6, 0, 0]} />}
            </BarChart>
          </ResponsiveContainer>
        </div>
      )}
    </div>
  );
}
