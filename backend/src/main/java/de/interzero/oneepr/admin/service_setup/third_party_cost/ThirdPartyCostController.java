package de.interzero.oneepr.admin.service_setup.third_party_cost;

import de.interzero.oneepr.admin.service_setup.third_party_cost.dto.CreateOtherCostDto;
import de.interzero.oneepr.admin.service_setup.third_party_cost.dto.UpdateOtherCostDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing other costs.
 */
@RestController
@RequestMapping(Api.ADMIN_OTHER_COSTS)
@Tag(name = "OtherCost")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ThirdPartyCostController {

    private final ThirdPartyCostService thirdPartyCostService;

    /**
     * Creates a new other cost.
     *
     * @param data The DTO containing the data for the new other cost.
     * @return The newly created {@link ThirdPartyCost} entity.
     */
    @PostMapping
    @Operation(summary = "Create a new other cost")
    @ApiResponse(
            responseCode = "201",
            description = "Other cost created successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Bad Request"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public ResponseEntity<ThirdPartyCost> create(@RequestBody CreateOtherCostDto data) {
        ThirdPartyCost createdCost = thirdPartyCostService.create(data);
        return new ResponseEntity<>(createdCost, HttpStatus.CREATED);
    }

    /**
     * Retrieves all non-deleted other costs.
     *
     * @return A list of {@link ThirdPartyCost} objects.
     */
    @GetMapping
    @Operation(summary = "Get all other costs")
    @ApiResponse(
            responseCode = "200",
            description = "Other costs retrieved successfully"
    )
    public ResponseEntity<List<ThirdPartyCost>> findAll() {
        List<ThirdPartyCost> thirdPartyCosts = thirdPartyCostService.findAll();
        return ResponseEntity.ok(thirdPartyCosts);
    }

    /**
     * Retrieves a specific other cost by its ID.
     *
     * @param id The ID of the other cost to retrieve.
     * @return The requested {@link ThirdPartyCost} object.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get other cost by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Other cost retrieved successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Other cost not found"
    )
    public ResponseEntity<ThirdPartyCost> findOne(@PathVariable String id) {
        ThirdPartyCost thirdPartyCost = thirdPartyCostService.findOne(Integer.valueOf(id));
        return ResponseEntity.ok(thirdPartyCost);
    }

    /**
     * Updates an existing other cost.
     *
     * @param id   The ID of the other cost to update.
     * @param data DTO containing the fields to update.
     * @return The updated {@link ThirdPartyCost} object.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update other cost by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Other cost updated successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Other cost not found"
    )
    public ResponseEntity<ThirdPartyCost> update(@PathVariable String id,
                                                 @RequestBody UpdateOtherCostDto data) {
        ThirdPartyCost updatedCost = thirdPartyCostService.update(Integer.valueOf(id), data);
        return ResponseEntity.ok(updatedCost);
    }

    /**
     * Soft-deletes an other cost by its ID.
     *
     * @param id The ID of the other cost to delete.
     * @return The other cost object with its deletion date set.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete other cost by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Other cost deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Other cost not found"
    )
    public ResponseEntity<ThirdPartyCost> remove(@PathVariable String id) {
        ThirdPartyCost removedCost = thirdPartyCostService.remove(Integer.valueOf(id));
        return ResponseEntity.ok(removedCost);
    }
}
