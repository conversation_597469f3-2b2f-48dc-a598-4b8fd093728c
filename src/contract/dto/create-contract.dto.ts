import { ApiProperty } from "@nestjs/swagger";
import { ContractType } from "@prisma/client";

export class CreateContractDto {
  @ApiProperty({
    description: "The customer id of the contract",
    example: 1,
  })
  customer_id: number;

  @ApiProperty({
    description: "The type of the contract",
    enum: ContractType,
    example: ContractType.EU_LICENSE,
  })
  type: ContractType;

  @ApiProperty({
    description: "The title of the contract",
    example: "Contract Title",
  })
  title: string;

  @ApiProperty({
    description: "The start date of the contract",
    example: "2024-03-20T00:00:00.000Z",
  })
  start_date: Date;
}
