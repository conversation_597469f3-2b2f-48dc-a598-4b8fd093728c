import { VolumeReport } from "../volume-report/types";

export interface PackagingService {
  id: number;
  setup_packaging_service_id: number;
  license_id: number;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  deleted_at: any;
  report_set: {
    id: number;
    setup_report_set_id: number;
    license_packaging_service_id: number;
    created_at: string;
    updated_at: string;
    deleted_at: any;
  };
  report_set_frequency: {
    id: number;
    setup_report_set_frequency_id: number;
    license_packaging_service_id: number;
    rhythm: string;
    frequency: object;
    created_at: string;
    updated_at: string;
    deleted_at: any;
  };
  volume_reports: VolumeReport[];
}
