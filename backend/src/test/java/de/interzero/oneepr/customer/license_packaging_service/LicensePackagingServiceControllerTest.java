package de.interzero.oneepr.customer.license_packaging_service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.dto.*;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static de.interzero.oneepr.common.string.Api.CUSTOMER_PACKAGING_SERVICES;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;


@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class  LicensePackagingServiceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private LicensePackagingServiceRepository licensePackagingServiceRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    private WireMockServer wireMockServer;

    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;

    private static final Integer TEST_CONTRACT_ID = 1;

    private static final Integer TEST_LICENSE_ID = 1;

    private static final Integer TEST_SETUP_PACKAGING_SERVICE_ID = 1;

    private static final String TEST_USER_EMAIL = "<EMAIL>";

    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    private License testLicense;

    private Customer testCustomer;

    private LicensePackagingService testPackagingService;

    @BeforeEach
    void setUp() {
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Create test data in database
        createTestData();

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(
                String.valueOf(testCustomer.getUserId()),
                TEST_USER_ROLE,
                TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
    }


    private void createTestData() {
        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setFirstName("test first name");
        testCustomer.setLastName("test last name");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        Contract testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setTitle("test contract title");
        testContract.setEndDate(Instant.now());
        testContract.setStartDate(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create License
        testLicense = new License();
        testLicense.setId(TEST_LICENSE_ID);
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setYear(2024);
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense.setStartDate(Instant.now());
        testLicense.setCountryId(1);
        testLicense.setCountryName("Germany");
        testLicense.setRegistrationNumber("test registration number");
        testLicense.setCountryCode("Germany");
        testLicense.setCountryFlag("Germany");
        testLicense = licenseRepository.save(testLicense);

    }

    // ========== FIND ALL METHOD TESTS (GET /packaging-services) ==========

    /**
     * Test findAll method - Success case without filter
     * Tests: GET /packaging-services
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnList_WhenNoFilter() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(CUSTOMER_PACKAGING_SERVICES))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].id").value(testPackagingService.getId()))
                .andExpect(jsonPath("$[0].name").value("Test Packaging Service"))
                .andExpect(jsonPath("$[0].description").value("Test Description"));
    }

    /**
     * Test findAll method - Success case with license_id filter
     * Tests: GET /packaging-services?license_id=1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnFilteredList_WhenLicenseIdProvided() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(CUSTOMER_PACKAGING_SERVICES)
                                .param("license_id", testPackagingService.getLicense().getId().toString()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].id").value(testPackagingService.getId()));
    }

    /**
     * Test findAll method - Empty list when no services exist
     * Tests: GET /packaging-services when no services exist
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnEmptyList_WhenNoServicesExist() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(CUSTOMER_PACKAGING_SERVICES))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(0));
    }

    /**
     * Test findAll method - Invalid license_id parameter
     * Tests: GET /packaging-services?license_id=invalid
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnBadRequest_WhenInvalidLicenseId() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(CUSTOMER_PACKAGING_SERVICES).param("license_id", "invalid"))
                .andExpect(status().isBadRequest());
    }

    // ========== FIND ONE METHOD TESTS (GET /packaging-services/{id}) ==========

    /**
     * Test findOne method - Success case
     * Tests: GET /packaging-services/1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnService_WhenServiceExists() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()))
                .andExpect(jsonPath("$.name").value("Test Packaging Service"))
                .andExpect(jsonPath("$.description").value("Test Description"));
    }

    /**
     * Test findOne method - Service doesn't exist
     * Tests: GET /packaging-services/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnNotFound_WhenServiceNotExists() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", 999)).andExpect(status().isNotFound());
    }

    /**
     * Test findOne method - Customer role accessing other customer's data
     * Tests: Authorization check for findOne endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isForbidden());
    }

    /**
     * Test findOne method - Admin role should have access
     * Tests: Authorization check for ADMIN role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnService_WhenAdminRole() throws Exception {
        // Given - Mock admin user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.ADMIN, "<EMAIL>"));

        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()));
    }

    // ========== PERFORMANCE METHOD TESTS (GET /packaging-services/{id}/performance) ==========

    /**
     * Test performance method - Success case
     * Tests: GET /packaging-services/1/performance
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void performance_ShouldReturnPerformanceData_WhenValidId() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/performance",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(new GetLicensePackagingServicePerformanceDto())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setup_packaging_service_id").value(TEST_SETUP_PACKAGING_SERVICE_ID))
                .andExpect(jsonPath("$.customers_total").exists())
                .andExpect(jsonPath("$.revenue_total").exists())
                .andExpect(jsonPath("$.handling_total").exists())
                .andExpect(jsonPath("$.third_party_total").exists());
    }

    /**
     * Test performance method - With date filters
     * Tests: GET /packaging-services/1/performance?start_date=2024-01-01&end_date=2024-12-31
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void performance_ShouldReturnFilteredData_WhenDateFiltersProvided() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();
        Map<String, String> param = new HashMap<>();
        param.put("start_date", "2024-01-01");
        param.put("end_date", "2024-12-31");

        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/performance",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(param)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setup_packaging_service_id").value(TEST_SETUP_PACKAGING_SERVICE_ID));
    }

    /**
     * Test performance method - Invalid ID
     * Tests: GET /packaging-services/0/performance (invalid ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void performance_ShouldReturnBadRequest_WhenInvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}/performance", 0)).andExpect(status().isBadRequest());
    }

    // ========== TURNOVER METHOD TESTS (GET /packaging-services/{id}/turnover) ==========

    /**
     * Test turnover method - Success case with MONTH grouping
     * Tests: GET /packaging-services/1/turnover?group_by=MONTH
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void turnover_ShouldReturnTurnoverData_WhenValidIdGrouping() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        GetLicensePackagingServiceTurnoverDto month = new GetLicensePackagingServiceTurnoverDto();
        month.setGroupBy("MONTH");
        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/turnover",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(month)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(6)); // Last 6 months


        GetLicensePackagingServiceTurnoverDto quarter = new GetLicensePackagingServiceTurnoverDto();
        quarter.setGroupBy("QUARTER");
        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/turnover",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(quarter)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(4)); // Last 4 quarters


        GetLicensePackagingServiceTurnoverDto year = new GetLicensePackagingServiceTurnoverDto();
        year.setGroupBy("YEAR");
        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/turnover",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(year)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(2)); // Previous year and current year
    }


    /**
     * Test turnover method - Invalid group_by parameter
     * Tests: GET /packaging-services/1/turnover?group_by=INVALID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void turnover_ShouldReturnBadRequest_WhenInvalidGroupBy() throws Exception {
        // When & Then
        mockMvc.perform(get(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}/turnover",
                        TEST_SETUP_PACKAGING_SERVICE_ID).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString("INVALID")))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test turnover method - Invalid ID
     * Tests: GET /packaging-services/0/turnover?group_by=MONTH
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void turnover_ShouldReturnBadRequest_WhenInvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}/turnover", 0).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString("MONTH"))).andExpect(status().isBadRequest());
    }

    // ========== WEIGHT REPORTED METHOD TESTS (GET /packaging-services/{id}/weight-reported) ==========

    /**
     * Test weightReported method - Success case
     * Tests: GET /packaging-services/1/weight-reported
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void weightReported_ShouldReturnWeightData_WhenValidId() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}/weight-reported", TEST_SETUP_PACKAGING_SERVICE_ID))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    /**
     * Test weightReported method - Invalid ID
     * Tests: GET /packaging-services/0/weight-reported
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void weightReported_ShouldReturnBadRequest_WhenInvalidId() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}/weight-reported", 0))
                .andExpect(status().isBadRequest());
    }

    // ========== CREATE METHOD TESTS (POST /packaging-services) ==========

    /**
     * Test create method - Success case
     * Tests: POST /packaging-services with valid DTO
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnCreatedService_WhenValidDto() throws Exception {
        // Given
        CreateLicensePackagingServiceDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(CUSTOMER_PACKAGING_SERVICES)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name").value("Test Packaging Service"))
                .andExpect(jsonPath("$.description").value("Test Description"))
                .andExpect(jsonPath("$.setup_packaging_service_id").value(TEST_SETUP_PACKAGING_SERVICE_ID))
                .andExpect(jsonPath("$.created_at").exists())
                .andExpect(jsonPath("$.updated_at").exists());
    }

    /**
     * Test create method - Invalid JSON
     * Tests: POST /packaging-services with malformed JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(CUSTOMER_PACKAGING_SERVICES)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test create method - Missing content type
     * Tests: POST /packaging-services without Content-Type header
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicensePackagingServiceDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(CUSTOMER_PACKAGING_SERVICES)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    // ========== UPDATE METHOD TESTS (PUT /packaging-services/{id}) ==========

    /**
     * Test update method - Success case
     * Tests: PUT /packaging-services/1 with valid DTO
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnUpdatedService_WhenValidDto() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        UpdateLicensePackagingServiceDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}",
                        testPackagingService.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()))
                .andExpect(jsonPath("$.name").value("Updated Packaging Service"))
                .andExpect(jsonPath("$.description").value("Updated Description"));
    }

    /**
     * Test update method - Non-existent service
     * Tests: PUT /packaging-services/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnNotFound_WhenServiceNotExists() throws Exception {
        // Given
        UpdateLicensePackagingServiceDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(CUSTOMER_PACKAGING_SERVICES + "/{id}", 999).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isNotFound());
    }

    /**
     * Test update method - Invalid JSON
     * Tests: PUT /packaging-services/1 with malformed JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(put(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}",
                        testPackagingService.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\""))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test update method - Customer role accessing other customer's data
     * Tests: Authorization check for update endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicensePackagingServiceDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                CUSTOMER_PACKAGING_SERVICES + "/{id}",
                testPackagingService.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isForbidden());
    }

    // ========== REMOVE METHOD TESTS (DELETE /packaging-services/{id}) ==========

    /**
     * Test remove method - Success case (soft delete)
     * Tests: DELETE /packaging-services/1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnRemovedService_WhenValidId() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(delete(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()))
                .andExpect(jsonPath("$.deleted_at").exists());
    }

    /**
     * Test remove method - Non-existent service
     * Tests: DELETE /packaging-services/999 (non-existent ID)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnNotFound_WhenServiceNotExists() throws Exception {
        // When & Then
        mockMvc.perform(delete(CUSTOMER_PACKAGING_SERVICES + "/{id}", 999)).andExpect(status().isNotFound());
    }

    /**
     * Test remove method - Customer role accessing other customer's data
     * Tests: Authorization check for remove endpoint
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(delete(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isForbidden());
    }

    // ========== ROLE-BASED ACCESS TESTS ==========

    /**
     * Test with different user roles - SUPER_ADMIN
     * Tests: Authorization check for SUPER_ADMIN role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldWork_WithSuperAdminRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.SUPER_ADMIN, "<EMAIL>"));

        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()));
    }

    /**
     * Test with different user roles - CLERK
     * Tests: Authorization check for CLERK role
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldWork_WithClerkRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CLERK, "<EMAIL>"));

        // First create a test packaging service
        testPackagingService = createAndSaveTestPackagingService();

        UpdateLicensePackagingServiceDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                        CUSTOMER_PACKAGING_SERVICES + "/{id}",
                        testPackagingService.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testPackagingService.getId()));
    }

    // ========== EDGE CASE TESTS ==========

    /**
     * Test with negative ID values
     * Tests: GET /packaging-services/-1
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnNotFound_WithNegativeId() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}", -1)).andExpect(status().isNotFound());
    }

    /**
     * Test with zero ID values
     * Tests: PUT /packaging-services/0
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnNotFound_WithZeroId() throws Exception {
        // Given
        UpdateLicensePackagingServiceDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(CUSTOMER_PACKAGING_SERVICES + "/{id}", 0).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isNotFound());
    }

    /**
     * Test with large ID values
     * Tests: DELETE /packaging-services/********** (Integer.MAX_VALUE)
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnNotFound_WithLargeId() throws Exception {
        // When & Then
        mockMvc.perform(delete(CUSTOMER_PACKAGING_SERVICES + "/{id}", Integer.MAX_VALUE))
                .andExpect(status().isNotFound());
    }

    /**
     * Test performance method with invalid date format
     * Tests: GET /packaging-services/1/performance?start_date=invalid-date
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void performance_ShouldReturnBadRequest_WithInvalidDateFormat() throws Exception {
        // When & Then
        mockMvc.perform(get(CUSTOMER_PACKAGING_SERVICES + "/{id}/performance", TEST_SETUP_PACKAGING_SERVICE_ID).param("start_date",
                                                                                                                      "invalid-date"))
                .andExpect(status().isBadRequest());
    }

    // ========== HELPER METHODS ==========

    /**
     * Helper method to create test CreateLicensePackagingServiceDto
     */
    private CreateLicensePackagingServiceDto createTestCreateDto() {
        CreateLicensePackagingServiceDto dto = new CreateLicensePackagingServiceDto();
        dto.setLicenseId(TEST_LICENSE_ID);
        dto.setSetupPackagingServiceId(TEST_SETUP_PACKAGING_SERVICE_ID);
        dto.setName("Test Packaging Service");
        dto.setDescription("Test Description");
        return dto;
    }

    /**
     * Helper method to create test UpdateLicensePackagingServiceDto
     */
    private UpdateLicensePackagingServiceDto createTestUpdateDto() {
        UpdateLicensePackagingServiceDto dto = new UpdateLicensePackagingServiceDto();
        dto.setLicenseId(TEST_LICENSE_ID);
        dto.setSetupPackagingServiceId(TEST_SETUP_PACKAGING_SERVICE_ID);
        dto.setName("Updated Packaging Service");
        dto.setDescription("Updated Description");
        return dto;
    }

    /**
     * Helper method to create and save a test LicensePackagingService
     */
    private LicensePackagingService createAndSaveTestPackagingService() {
        LicensePackagingService service = new LicensePackagingService();
        service.setLicense(testLicense);
        service.setSetupPackagingServiceId(TEST_SETUP_PACKAGING_SERVICE_ID);
        service.setName("Test Packaging Service");
        service.setDescription("Test Description");
        service.setCreatedAt(Instant.now());
        service.setUpdatedAt(Instant.now());

        return licensePackagingServiceRepository.save(service);
    }
}
