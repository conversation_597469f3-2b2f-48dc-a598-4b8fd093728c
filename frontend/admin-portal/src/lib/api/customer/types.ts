export type ListCustomer = {
  id: number;
  first_name: string;
  last_name: string;
  salutation: string;
  email: string;
  user_id: number;
  is_active: boolean;
  id_stripe: number | null;
  updated_at: string;
  created_at: string;
  companies: Company[];
  phones: CustomerPhone[];
  contracts: Contract[];
};

export interface Company {
  id: number;
  name: string;
  description?: string;
  vat: string;
  tin: string;
  lucid?: string;
  customer_id?: number;
  starting?: string;
  website?: string;
  partner_id?: number;
  address_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
  address: CompanyAddress;
  owner_name?: string;
  industry_sector?: string;
  contacts?: {
    name: string;
    email: string;
    phone_mobile: string;
  };
}

export interface CompanyAddress {
  id: number;
  country_code: string;
  address_line: string;
  city: string;
  zip_code: string;
  street_and_number: string;
  additional_address: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface CustomerPhone {
  id: number;
  phone_number: string;
  customer_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  phone_type: string;
}

export interface Contract {
  id: number;
  customer_id: number;
  type: ContractType;
  status: ContractStatus;
  title: string;
  start_date: Date;
  end_date: Date;
  termination_id: number | null;
  termination_date: Date | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  licenses: License[];
  action_guides: ActionGuide[];
  files: UploadedFile[];
  termination: Termination | null;
}

export type ContractType = "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";

export type ContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export type ActionGuide = {
  id: number;
  contract_id: number;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  contract_status: ContractStatus;
  termination: Termination | null;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
};

export type Termination = {
  id: number;
  created_at: string;
};

export type License = {
  id: number;
  contract_id: number;
  registration_number: string;
  registration_status: LicenseRegistrationStatus;
  clerk_control_status: LicenseClerkControlStatus;
  contract_status: LicenseContractStatus;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  year: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  start_date: string;
  end_date: string | null;
  termination: Termination | null;
  files: UploadedFile[];
};

export type LicenseRegistrationStatus = "PENDING" | "IN_REVIEW" | "REGISTRATION" | "DONE";
export type LicenseClerkControlStatus = "PENDING" | "DONE";
export type LicenseContractStatus = "ACTIVE" | "TERMINATION_PROCESS" | "TERMINATED";

export interface UploadedFile {
  id: string;
  name: string;
  original_name: string;
  extension: string;
  size: string;
  type: FileType;
  created_at: string;
  updated_at: string;
  required_information_id: number;
  contract_id: number | null;
  certificate_id: number | null;
  license_id: number | null;
  termination_id: number | null;
  general_information_id: number | null;
}

export type FileType =
  | "GENERAL_INFORMATION"
  | "REQUIRED_INFORMATION"
  | "CONTRACT"
  | "CONTRACT_TERMINATION"
  | "LICENSE_CONTRACT"
  | "CERTIFICATE"
  | "INVOICE"
  | "PAYMENT"
  | "LICENSE_PROOF_OF_REGISTRATION";
