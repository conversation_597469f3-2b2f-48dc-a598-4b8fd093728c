interface NewCustomersSummary {
  total: number;
  eu_licensed: number;
  direct_licensed: number;
  action_guide: number;
  other_services: number;
  brokers: number;
}

interface NetRevenuesSummary {
  overral_products: number;
  eu_licensing: number;
  direct_licensing: number;
  action_guide: number;
  other_services: number;
  bulk_registration: number;
}

export interface TotalCustomers {
  total_licensed: number;
  net_revenues_summary: NetRevenuesSummary;
  existing_clients_licensed: number;
  new_customers_summary: NewCustomersSummary;
}
