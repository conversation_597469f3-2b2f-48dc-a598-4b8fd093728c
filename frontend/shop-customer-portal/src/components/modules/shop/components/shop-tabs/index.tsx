"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { CheckCircle, Add } from "@arthursenno/lizenzero-ui-react/Icon";
import { Link } from "@/i18n/navigation";

interface ShopCountryTabsProps {
  selectedCountry: string | null;
  onCountrySelect: (countryCode: string) => void;
  className?: string;
  obligationCompletionStatus?: Record<string, boolean>;
  countries?: Array<{
    country_code: string;
    country_name: string;
    country_flag: string;
    service_type?: string;
  }>;
}

export function ShopCountryTabs({
  selectedCountry,
  onCountrySelect,
  className,
  obligationCompletionStatus = {},
  countries: propCountries,
}: ShopCountryTabsProps) {
  const { shoppingCart } = useShoppingCart();

  // Use provided countries prop or fallback to license items from cart
  const licenseItems = shoppingCart.items.filter((item) => item.service_type === "EU_LICENSE");

  // Use countries in order of preference: prop > cart items
  const countries = propCountries || licenseItems;

  const handleValueChange = (value: string) => {
    // Skip the "__add__" case since it's handled by Link navigation
    if (value !== "__add__") {
      onCountrySelect(value);
    }
  };

  return (
    <div className={className}>
      <Tabs value={selectedCountry || ""} onValueChange={handleValueChange} className="mb-8">
        <div className="flex flex-wrap items-center gap-3">
          <TabsList className="flex-wrap gap-3">
            {countries.map((country) => {
              const isCompleted = obligationCompletionStatus[country.country_code] || false;
              const status = isCompleted ? "completed" : "pending";
              const isActive = selectedCountry === country.country_code;

              return (
                <TabsTrigger
                  key={country.country_code}
                  value={country.country_code}
                  status={status}
                  className="gap-3 text-base"
                >
                  <CountryIcon
                    country={{
                      flag_url: country.country_flag,
                      name: country.country_name,
                    }}
                    className="border border-white"
                  />
                  {country.country_name}
                  {isCompleted && <CheckCircle className={`ml-2 size-5 ${isActive ? "fill-white" : "fill-primary"}`} />}
                </TabsTrigger>
              );
            })}
          </TabsList>
          <Link
            href="/eu/long-journey/select-countries?step=sales"
            aria-label="Add country"
            className="inline-flex items-center justify-center whitespace-nowrap rounded-full px-5 py-1 font-bold text-primary gap-2 border-primary border-2 bg-transparent hover:opacity-50"
          >
            <Add className="size-5 fill-primary" />
            <span className="text-base">Add</span>
          </Link>
        </div>
      </Tabs>
    </div>
  );
}
