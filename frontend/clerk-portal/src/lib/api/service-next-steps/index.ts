import { ApiEndpoints } from "../endpoints";
import { api } from "..";

interface UpdateServiceNextStepParams {
  done_at: string | null;
}

export const updateServiceNextStep = async (id: number, data: UpdateServiceNextStepParams) => {
  try {
    await api.put(ApiEndpoints.serviceNextSteps.update(id), data);

    return { success: true } as const;
  } catch (error: any) {
    return { success: false, error: error.message || "Failed to toggle service step" } as const;
  }
};
