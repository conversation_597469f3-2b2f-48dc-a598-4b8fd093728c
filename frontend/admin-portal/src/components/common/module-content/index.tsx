import { cn } from "@/utils/cn";

interface ModuleContentProps {
  containerClassName?: string;
  contentClassName?: string;
  children: React.ReactNode;
}

export function ModuleContent({ containerClassName, contentClassName, children }: ModuleContentProps) {
  return (
    <div className={cn("w-full px-4 py-6 lg:py-14", containerClassName)}>
      <div className={cn("w-full lg:max-w-[912px] mx-auto", contentClassName)}>{children}</div>
    </div>
  );
}
