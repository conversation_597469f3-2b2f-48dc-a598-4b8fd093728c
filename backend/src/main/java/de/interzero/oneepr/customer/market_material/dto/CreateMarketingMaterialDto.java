package de.interzero.oneepr.customer.market_material.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.market_material.MarketingMaterial;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "Create marketing material request")
@Data
public class CreateMarketingMaterialDto {

    @Schema(
            description = "The name of the marketing material",
            example = "Marketing Material 1"
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "The start date of the marketing material in ISO",
            example = "2011-10-05T14:48:00.000Z"
    )
    @JsonProperty("start_date")
    private LocalDate startDate;

    @Schema(
            description = "The end date of the marketing material in ISO",
            example = "2011-10-05T14:48:00.000Z"
    )
    @JsonProperty("end_date")
    private LocalDate endDate;

    @Schema(
            description = "The category of the marketing material",
            example = "STANDARD or SPECIFIC_MATERIAL"
    )
    @JsonProperty("category")
    private MarketingMaterial.Category category;

    @Schema(
            description = "The restriction of the marketing material",
            example = "ALL or CLUSTER or SPECIFIC"
    )
    @JsonProperty("partner_restriction")
    private MarketingMaterial.PartnerRestriction partnerRestriction;

    @Schema(
            description = "Comma-separated partner IDs",
            example = "1,2,3"
    )
    @JsonProperty("partners")
    private String partners;
}
