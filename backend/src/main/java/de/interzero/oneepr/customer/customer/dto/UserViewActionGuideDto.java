package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * Data Transfer Object representing a detailed view of an Action Guide for the user profile.
 * <p>
 * This DTO is a direct translation of the {@code ActionGuide} model, included with
 * a {@code where: { deleted_at: null }} filter within the {@code contracts} relation
 * of the {@code customer.service.ts#findByUserId} method.
 */
@Data
@NoArgsConstructor
public class UserViewActionGuideDto {

    @Schema(description = "Unique identifier of the action guide.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The unique identifier of the country.")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The ISO code of the country.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "The name of the country.")
    @JsonProperty("country_name")
    private String countryName;

    @Schema(description = "The flag icon or URL for the country.")
    @JsonProperty("country_flag")
    private String countryFlag;

    @Schema(description = "The status of the associated contract.")
    @JsonProperty("contract_status")
    private Contract.Status contractStatus;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The associated termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;
}