import { adminApi } from "@/lib/api";
import { ApiEndpoints } from "@/lib/api/endpoints";
import { CreateCriteria, Criteria, UpdateCriteria } from "@/types/service-setup/criteria";

export async function getCriterias(countryId: number) {
  const response = await adminApi.get<Criteria[]>(ApiEndpoints.criterias.findAll, {
    params: {
      countryId,
    },
  });

  return response.data;
}

export async function createCriteria(reportSet: CreateCriteria) {
  const response = await adminApi.post<Criteria>(ApiEndpoints.criterias.create, reportSet);

  return response.data;
}

export async function updateCriteria(reportSetId: number, reportSet: UpdateCriteria) {
  const response = await adminApi.put<Criteria>(ApiEndpoints.criterias.update(reportSetId), reportSet);

  return response.data;
}

export async function deleteCriteria(reportSetId: number) {
  await adminApi.delete(ApiEndpoints.criterias.delete(reportSetId));
}

export async function getCriteriasBySectionId(sectionId: number) {
  const response = await adminApi.get<Criteria[]>(ApiEndpoints.criterias.findBySectionId(sectionId));
  return response.data;
}
