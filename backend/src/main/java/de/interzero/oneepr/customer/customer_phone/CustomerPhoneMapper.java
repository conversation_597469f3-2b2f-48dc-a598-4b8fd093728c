package de.interzero.oneepr.customer.customer_phone;

import de.interzero.oneepr.customer.customer_phone.dto.CreateCustomerPhoneDto;
import de.interzero.oneepr.customer.customer_phone.dto.UpdateCustomerPhoneDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * Mapper for {@link CustomerPhone}
 */
@Mapper(componentModel = "spring")
public interface CustomerPhoneMapper {

    /**
     * Maps a {@link CreateCustomerPhoneDto} to a {@link CustomerPhone} entity.
     * <p>
     * This method is typically used when creating a new phone record.
     * MapStruct will generate an implementation that copies properties
     * from the source DTO to a new Phone entity instance.
     *
     * @param createPhoneDto the DTO containing the data for the new phone
     * @return the newly created and mapped {@link CustomerPhone} entity
     */
    @Mapping(
            source = "phoneNumber",
            target = "phoneNumber"
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    CustomerPhone toEntity(CreateCustomerPhoneDto createPhoneDto);

    /**
     * Updates an existing {@link CustomerPhone} entity with values from an {@link UpdateCustomerPhoneDto}.
     * <p>
     * This method modifies the {@code target} Phone entity in place.
     * MapStruct will generate an implementation that copies properties
     * from the {@code source} DTO to the {@code target} entity.
     * Fields that are null in the source DTO might be ignored by default,
     * depending on MapStruct's configuration or specific mapping rules defined.
     *
     * @param source the DTO containing the updated values for the phone
     * @param target the existing {@link CustomerPhone} entity to be updated (annotated with {@link MappingTarget})
     */
    @Mapping(
            source = "phoneNumber",
            target = "phoneNumber"
    )
    @Mapping(
            target = "customer",
            ignore = true
    )
    void updateEntity(UpdateCustomerPhoneDto source,
                      @MappingTarget CustomerPhone target);
}