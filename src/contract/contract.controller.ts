import { Body, Controller, Delete, Get, Param, Post, Put, Query } from "@nestjs/common";
import { CreateContractDto } from "./dto/create-contract.dto";
import { UpdateContractDto } from "./dto/update-contract.dto";
import { ContractService } from "./contract.service";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { FindAllContractDto } from "./dto/find-all-contract.dto";
import { Roles } from "src/shared/auth/role.decorator";
import { Role } from "src/shared/auth/role.enum";
import { AuthenticatedUser, User } from "@/shared/auth/user.decorator";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.CUSTOMER)
@Controller("contracts")
@ApiTags("contracts")
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @Get()
  @ApiOperation({ summary: "Get all contracts" })
  findAll(@Query() query: FindAllContractDto) {
    return this.contractService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a contract by id" })
  findOne(@Param("id") id: number) {
    return this.contractService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: "Create a contract" })
  create(@Body() data: CreateContractDto) {
    return this.contractService.create(data);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a contract" })
  update(@Param("id") id: number, @Body() data: UpdateContractDto, @User() user: AuthenticatedUser) {
    return this.contractService.update(id, data, user);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a contract" })
  remove(@Param("id") id: number, @User() user: AuthenticatedUser) {
    return this.contractService.remove(id, user);
  }
}
