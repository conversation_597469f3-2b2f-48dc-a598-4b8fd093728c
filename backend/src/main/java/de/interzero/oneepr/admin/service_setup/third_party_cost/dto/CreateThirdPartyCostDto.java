package de.interzero.oneepr.admin.service_setup.third_party_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new third party cost.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateThirdPartyCostDto extends BaseDto {

    @JsonProperty("name")
    @Schema(
            description = "Name of the third party cost",
            example = "Registration Fee",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotBlank(message = "Name is required")
    private String name;

    @JsonProperty("price")
    @Schema(
            description = "Price of the third party cost in cents",
            example = "5000",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull(message = "Price is required")
    @Positive(message = "Price must be positive")
    private Integer price;
}
