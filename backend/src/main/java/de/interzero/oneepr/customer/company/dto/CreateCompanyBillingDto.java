package de.interzero.oneepr.customer.company.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object for creating company billing information.
 * Converted from TypeScript CreateCompanyDto billing structure with exact same field names.
 */
@Schema(description = "Company billing information")
@Data
public class CreateCompanyBillingDto {

    @JsonProperty("full_name")
    @Schema(
            description = "Full name for billing",
            example = "John Doe"
    )
    private String fullName;

    @JsonProperty("country_code")
    @Schema(
            description = "Country code",
            example = "DE"
    )
    private String countryCode;

    @JsonProperty("country_name")
    @Schema(
            description = "Country name",
            example = "Germany"
    )
    private String countryName;

    @JsonProperty("company_name")
    @Schema(
            description = "Company name for billing",
            example = "Acme Corp"
    )
    private String companyName;

    @JsonProperty("street_and_number")
    @Schema(
            description = "Street and number",
            example = "Main Street 123"
    )
    private String streetAndNumber;

    @JsonProperty("city")
    @Schema(
            description = "City",
            example = "Berlin"
    )
    private String city;

    @JsonProperty("zip_code")
    @Schema(
            description = "ZIP code",
            example = "10115"
    )
    private String zipCode;
}
