package de.interzero.oneepr.admin.service_setup.required_information.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.dto.FilesDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CriteriaDto;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object for the response of the findServiceSetupRequiredInformations method.
 * It represents a RequiredInformation entity, augmented with its associated file,
 * a list of filtered commitment criteria, and a flag indicating if any such criteria exist.
 * This structure mirrors the transformation performed in the original NestJS service.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RequiredInformationResponseDto {

    @Schema(
            description = "The unique identifier of the required information.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The type of information required.",
            example = "DOCUMENT"
    )
    @JsonProperty("type")
    private RequiredInformation.Type type;

    @Schema(
            description = "The name of the required information.",
            example = "Company Registration"
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "The description of the required information.",
            example = "Please upload your company registration document."
    )
    @JsonProperty("description")
    private String description;

    @Schema(
            description = "A specific question related to the information.",
            example = "What is your registration number?"
    )
    @JsonProperty("question")
    private String question;

    @Schema(
            description = "The kind of information.",
            example = "COUNTRY_INFORMATION"
    )
    @JsonProperty("kind")
    private RequiredInformation.Kind kind;

    @Schema(description = "The associated file, if any.")
    @JsonProperty("file")
    private FilesDto file;

    @Schema(description = "List of associated criteria filtered for 'REQUIRED_INFORMATION' and 'COMMITMENT'.")
    @JsonProperty("criterias")
    private List<CriteriaDto> criterias;

    @Schema(
            description = "Flag indicating if this information has commitment criteria.",
            example = "true"
    )
    @JsonProperty("has_criteria")
    private boolean hasCriteria;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(
            description = "The ID of the associated country.",
            example = "49"
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("packaging_service_ids")
    private List<Integer> packagingServiceIds;
}