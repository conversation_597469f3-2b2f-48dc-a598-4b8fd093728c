import { ApiProperty } from "@nestjs/swagger";

export class FindAllCustomersDto {
  @ApiProperty({
    required: false,
    description: "Page number",
  })
  page?: number;

  @ApiProperty({
    required: false,
    description: "Page size",
  })
  limit?: number;

  @ApiProperty({
    required: false,
    description: "Order by",
    enum: ["ASC", "DESC"],
  })
  order?: "ASC" | "DESC" | "LAST_MODIFIED" | "FIRST_MODIFIED";

  @ApiProperty({
    required: false,
    description: "Action performed",
  })
  search?: string;

  @ApiProperty({
    required: false,
    description: "Customer service type",
    enum: ["EU_LICENSE", "DIRECT_LICENSE", "ACTION_GUIDE", "WORKSHOP"],
  })
  service_type?: "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";

  @ApiProperty({
    required: false,
    description: "Customer status",
    enum: ["ACTIVE", "TERMINATED"],
  })
  status?: "ACTIVE" | "TERMINATED";

  @ApiProperty({
    required: false,
    description: "Customer country code",
    example: "FR",
  })
  country_code?: string;
}
