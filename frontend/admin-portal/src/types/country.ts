export interface Country {
  id: number;
  name: string;
  code: string;
  flag_url: string;
  other_costs_obligated: boolean;
  is_published: boolean;
  has_other_cost_criteria?: boolean;
  has_representative_tier_criteria?: boolean;
  created_at: string;
  updated_at: string | null;
}

export type CreateCountry = Omit<Country, "id" | "created_at" | "updated_at">;

export type UpdateCountry = Partial<{
  other_costs_obligated: boolean;
}>;
