package de.interzero.oneepr.auth.auth;

import de.interzero.oneepr.auth.auth.dto.LoginResponse;
import de.interzero.oneepr.auth.user.User;
import de.interzero.oneepr.auth.user.UserRepository;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.jwt.*;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TokenService {

    private final JwtEncoder encoder;

    private final JwtDecoder decoder;

    private final UserDetailsService userDetailsService;

    private final UserRepository userRepo;

    private final long accessTokenValidity;

    private final long refreshTokenValidity;

    public static final String CLAIM_ROLES = "roles";

    public static final String CLAIM_ROLE_NAME = "roleName";

    public static final String CLAIM_ACCOUNT_MAIL = "accountMail";

    public static final String ROLE_STRING_DELIMITER = " ";

    public static final String TOKEN_ISSUER = "https://interzero.de/oneepr";

    private final Map<String, UserTokenInfo> refreshTokenWhitelist = new ConcurrentHashMap<>();

    public TokenService(JwtEncoder encoder,
                        JwtDecoder decoder,
                        UserDetailsService userDetailsService,
                        UserRepository userRepo,
                        @Value("${token.validity.access:********}") long accessTokenValidity,
                        @Value("${token.validity.refresh:********}") long refreshTokenValidity) {
        this.encoder = encoder;
        this.decoder = decoder;
        this.userDetailsService = userDetailsService;
        this.userRepo = userRepo;
        this.accessTokenValidity = accessTokenValidity; // 8 hours by default, overridden by properties file if present
        this.refreshTokenValidity = refreshTokenValidity; // 24 hours by default, overridden by properties file if present
    }


    /**
     * Generate a JWT access and refresh token for the given user, thus logging them in.
     *
     * @param authentication the user
     * @return token map with token and additional details of user
     */
    public LoginResponse login(@NonNull UserDetails authentication,
                               boolean includeRefreshToken) {
        // user details
        final User user = userRepo.findByEmailIgnoreCase(authentication.getUsername())
                .orElseThrow(InvalidLoginException::new);

        // create tokens
        Instant now = Instant.now();
        final String token = generateAccessToken(user, now.plusMillis(accessTokenValidity));
        final String refreshToken;
        if (includeRefreshToken) {
            refreshToken = generateRefreshToken(authentication, now.plusMillis(refreshTokenValidity));
            refreshTokenWhitelist.put(
                    refreshToken,
                    new UserTokenInfo(user.getUsername(), now.plusMillis(refreshTokenValidity)));
        } else {
            refreshToken = null; // no refresh token generated
        }

        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo(
                user.getId(),
                user.getEmail(),
                user.getName(),
                user.getIsActive(),
                user.getStatus(),
                user.getRoleId(),
                user.getRole().getName(),
                user.getPassword() != null);
        long expiresInSeconds = accessTokenValidity / 1000;
        return new LoginResponse(token, expiresInSeconds, refreshToken, userInfo);
    }

    /**
     * Generate an access token for the given user
     *
     * @param user the user
     * @return the access token
     */
    public String generateAccessToken(@NonNull User user,
                                      @NonNull Instant expiresAt) {
        String scope = user.getAuthorities()
                .stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(ROLE_STRING_DELIMITER));
        log.debug("scope: {}", scope);

        // Define the claims for the access token
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .issuer(TOKEN_ISSUER)
                .issuedAt(Instant.now())
                .expiresAt(expiresAt)
                .subject(user.getUsername())
                .claim(CLAIM_ROLES, scope)
                .claim(CLAIM_ACCOUNT_MAIL, user.getEmail())
                .claim(CLAIM_ROLE_NAME, user.getRole().getName())
                .build();
        return this.encoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();
    }

    /**
     * Generate a refresh token for the given user
     *
     * @param authentication the user
     * @return the refresh token
     */
    public String generateRefreshToken(@NonNull UserDetails authentication,
                                       @NonNull Instant expiresAt) {
        // Define the claims for the refresh token
        JwtClaimsSet claimsSet = JwtClaimsSet.builder()
                .subject(authentication.getUsername())
                .expiresAt(expiresAt)
                .build();

        return this.encoder.encode(JwtEncoderParameters.from(claimsSet)).getTokenValue();
    }

    /**
     * Validate the refresh token and generate a new access token, thus allowing the user to continue their session.
     *
     * @param refreshToken the refresh token
     * @return token map with token and additional details of user
     */
    public LoginResponse validateRefreshTokenAndGenerateAccessToken(String refreshToken) {
        try {
            // check if the refresh token is null or empty
            if (refreshToken == null || refreshToken.isBlank()) {
                throw new InvalidLoginException();
            }

            // check if the refresh token is whitelisted
            if (!refreshTokenWhitelist.containsKey(refreshToken)) {
                throw new InvalidLoginException();
            }

            // Decode and verify the refresh token
            Jwt decodedToken = decoder.decode(refreshToken);

            // Check if the refresh token is expired (redundant check, but good for clarity)
            Instant expiry = decodedToken.getExpiresAt();
            if (expiry == null || expiry.isBefore(Instant.now())) {
                throw new InvalidLoginException();
            }

            // get name from jwt token
            String username = decodedToken.getSubject();
            final UserDetails userDetails = userDetailsService.loadUserByUsername(username);
            return login(userDetails, false);

        } catch (Exception e) {
            throw new InvalidLoginException();
        }
    }

    /**
     * Logout the user by invalidating all refresh tokens associated with their user account.
     *
     * @param accessToken the access token of the user to be logged out
     */
    public void logout(@NonNull String accessToken) {
        // find the user associated with the access token
        String token = getSanitizedToken(accessToken);
        try {
            // Decode the access token to get the username
            Jwt decodedToken = decoder.decode(token);
            String username = decodedToken.getSubject();
            if (username == null || username.isBlank()) {
                throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid token: missing subject");
            }

            // Remove all refresh tokens belonging to this user
            refreshTokenWhitelist.entrySet().removeIf(entry -> entry.getValue().username().equals(username));
            log.info("User '{}' logged out successfully", username);
        } catch (JwtException e) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "General token error");
        }
    }

    /**
     * Sanitize the access token from the request header.
     *
     * @param accessToken the access token from the request header
     * @return the sanitized token
     */
    private static String getSanitizedToken(String accessToken) {
        if (accessToken == null || accessToken.isBlank()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Authorization header not found");
        }
        String[] parts = accessToken.split(" ");
        if (parts.length != 2 || !"Bearer".equals(parts[0])) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid token type");
        }
        String token = parts[1];
        if (token.isBlank()) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Token not found");
        }
        return token;
    }

    /**
     * Check the status of the token
     *
     * @param authorizationToken the authorization token from the request header
     * @return true if the token is valid, false otherwise
     */
    public boolean status(String authorizationToken) {
        String token = getSanitizedToken(authorizationToken);

        try {
            // Use the JwtDecoder to decode and validate the token
            Jwt decodedToken = decoder.decode(token);

            // Validate expiration time
            Instant expiry = decodedToken.getExpiresAt();
            if (expiry == null || expiry.isBefore(Instant.now())) {
                throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Token has expired");
            }

            // Validate subject (username) exists in the system
            String username = decodedToken.getSubject();
            if (username == null || username.isBlank()) {
                throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid token: missing subject");
            }
            return true;
        } catch (JwtException e) {
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "General token error");
        }
    }

    /**
     * Periodically remove expired refresh tokens from the whitelist.
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // every hour
    public void cleanupExpiredRefreshTokens() {
        Instant now = Instant.now();
        refreshTokenWhitelist.entrySet().removeIf(entry -> entry.getValue().expiry().isBefore(now));
        log.debug("Expired refresh tokens cleaned up from whitelist");
    }

    /**
     * Record to hold user token information for the whitelist.
     *
     * @param username the username of the user
     * @param expiry   the expiration time of the token
     */
    private record UserTokenInfo(String username, Instant expiry) {

    }
}