import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from "class-validator";
import { CreatePartnerBankingDto } from "./create-partner-banking.dto";
import { CreatePartnerCompanyDto } from "./create-partner-company.dto";

export class UpdatePartnerDto {
  @ApiPropertyOptional({ description: "First name of the partner" })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  partner_firstname?: string;

  @ApiPropertyOptional({ description: "Last name of the partner" })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  partner_lastname?: string;

  @ApiPropertyOptional({ description: "Email address of the partner" })
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  partner_email?: string;

  @ApiPropertyOptional()
  banking?: CreatePartnerBankingDto;

  @ApiPropertyOptional()
  @IsOptional()
  company?: CreatePartnerCompanyDto;

  @ApiPropertyOptional()
  @IsOptional()
  no_provision_negotiated?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  payout_cycle?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  commission_mode?: string;

  @ApiPropertyOptional()
  @IsArray()
  @IsOptional()
  coupons?: number[];

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  marketing_material_id?: number;

  @ApiPropertyOptional({ description: "Marketing material name" })
  @IsString()
  @IsOptional()
  new_marketing_material_name?: string;

  @ApiPropertyOptional({
    type: "string",
    format: "binary",
    description: "Contract file",
  })
  @IsOptional()
  contract_file?: Express.Multer.File;

  @ApiPropertyOptional({
    type: "array",
    items: {
      type: "string",
      format: "binary",
    },
    description: "Marketing material files",
  })
  @IsOptional()
  new_marketing_material_files?: Express.Multer.File[];
}
