import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { OtherCost, OtherCostDto } from "./types";

export async function getOtherCosts({ countryCode }: { countryCode: string }) {
  try {
    const response = await api.get<OtherCost[]>(ApiEndpoints.otherCosts.get(countryCode));

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function createOtherCosts(data: OtherCostDto) {
  try {
    const response = await api.post<OtherCost[]>(ApiEndpoints.otherCosts.post, data);

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function deleteOtherCosts(otherCostsId: string | number) {
  try {
    const response = await api.delete(ApiEndpoints.otherCosts.delete(otherCostsId));

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}
