import AsyncPaginatedTable from "@/components/common/async-paginated-table";
import { DatatableSearch } from "@/components/common/datatable/datatable-search";
import { CountryIcon } from "@/components/ui/country-icon";
import { Dropdown, DropdownItem } from "@/components/ui/dropdown";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getCustomers } from "@/lib/api/customer";
import { ContractType, ListCustomer } from "@/lib/api/customer/types";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Elipse, KeyboardArrowDown, KeyboardArrowRight, FilterAlt } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { createColumnHelper } from "@tanstack/react-table";
import Link from "next/link";
import { useMemo } from "react";

function getStatusLabel(status: "ACTIVE" | "TERMINATED" | "TERMINATION_PROCESS") {
  if (status === "ACTIVE") return "Active";
  if (status === "TERMINATED") return "Terminated";
  if (status === "TERMINATION_PROCESS") return "Termination in process";
  return "";
}

const columnHelper = createColumnHelper<ListCustomer>();

const columns = [
  columnHelper.display({
    id: "company",
    cell: (info) => {
      const company = info.row.original.companies[0];
      return (
        <div className="flex flex-col gap-1">
          <Link
            href={`/accounts/${info.row.original.id}`}
            className="text-sm text-primary hover:underline underline-offset-2"
          >
            {company?.name ?? " "}
          </Link>
          <div className="text-xs font-medium text-tonal-dark-cream-50">
            #{formatCustomerNumber(info.row.original.id)}
          </div>
        </div>
      );
    },
    header: "Company",
  }),
  columnHelper.display({
    id: "country",
    cell: (info) => {
      const countries = info.row.original.contracts.reduce(
        (acc, contract) => {
          contract.licenses.forEach((license) => {
            if (acc.find((country) => country.country_code === license.country_code)) return;
            acc.push({
              country_code: license.country_code,
              country_name: license.country_name,
              country_flag: license.country_flag,
            });
          });

          contract.action_guides.forEach((actionGuide) => {
            if (acc.find((country) => country.country_code === actionGuide.country_code)) return;

            acc.push({
              country_code: actionGuide.country_code,
              country_name: actionGuide.country_name,
              country_flag: actionGuide.country_flag,
            });
          });

          return acc;
        },
        [] as { country_code: string; country_name: string; country_flag: string }[]
      );

      return (
        <div className="flex gap-2">
          {countries?.slice(0, 2).map((country) => (
            <div key={country.country_code}>
              <CountryIcon country={{ name: country.country_name, flag_url: country.country_flag }} />
            </div>
          ))}
          {countries && countries?.length > 2 && (
            <div className="size-6 flex items-center justify-center rounded-full bg-secondary text-primary text-xs font-medium border-[1.5px] border-primary">
              +{countries.length - 2}
            </div>
          )}
        </div>
      );
    },
    header: "Country",
  }),
  columnHelper.display({
    id: "licenseYear",
    header: "License Year",

    cell: (info) => {
      const contracts = info.row.original.contracts;
      const licenseYearsLabel = Array.from(
        new Set(contracts.map((contract) => contract.licenses.map((license) => license.year)).flat())
      ).join(", ");

      return <span className="text-primary text-sm">{licenseYearsLabel || "N/A"}</span>;
    },
  }),
  columnHelper.display({
    id: "termination",
    cell: (info) => {
      const contracts = info.row.original.contracts;

      const termination = (() => {
        for (const contract of contracts) {
          if (contract.termination) return contract.termination;

          if (contract.licenses) {
            for (const license of contract.licenses) {
              if (license.termination) return license.termination;
            }
          }

          if (contract.action_guides) {
            for (const actionGuide of contract.action_guides) {
              if (actionGuide.termination) return actionGuide.termination;
            }
          }
        }

        return null;
      })();

      const terminationLabel = termination
        ? new Date(termination.created_at).toLocaleDateString()
        : "Automatic renewal";

      return <span className="text-primary text-sm">{terminationLabel}</span>;
    },
    header: "Termination Date",
  }),
  columnHelper.display({
    id: "customerStatus",
    cell: (info) => {
      const contracts = info.row.original.contracts;

      const status = (() => {
        for (const contract of contracts) {
          if (contract.status !== "ACTIVE") return contract.status;

          if (contract.licenses) {
            for (const license of contract.licenses) {
              if (license.contract_status !== "ACTIVE") return license.contract_status;
            }
          }

          if (contract.action_guides) {
            for (const actionGuide of contract.action_guides) {
              if (actionGuide.contract_status !== "ACTIVE") return actionGuide.contract_status;
            }
          }
        }

        return "ACTIVE";
      })();

      const statusLabel = getStatusLabel(status);

      return (
        <div
          data-status={status}
          className="text-sm group flex items-center gap-1 text-tonal-dark-cream-30 data-[status=ACTIVE]:text-success data-[status=TERMINATED]:text-tonal-red-50 data-[status=TERMINATION_PROCESS]:text-tonal-dark-cream-30"
        >
          <Elipse className="flex-none fill-tonal-dark-cream-30 group-data-[status=ACTIVE]:fill-success group-data-[status=TERMINATED]:fill-tonal-red-50 group-data-[status=TERMINATION_PROCESS]:fill-tonal-dark-cream-30 size-2" />
          <p className="mt-0.5 font-bold text-tonal-dark-cream-30 group-data-[status=ACTIVE]:text-success group-data-[status=TERMINATED]:text-tonal-red-50 group-data-[status=TERMINATION_PROCESS]:text-tonal-dark-cream-30">
            {statusLabel}
          </p>
        </div>
      );
    },
    header: "Customer Status",
  }),
  columnHelper.display({
    id: "actions",
    header: " ",
    cell: (info) => (
      <Link href={`/accounts/${info.row.original.id}`}>
        <Button variant="text" color="dark-blue" size="iconSmall" trailingIcon={<KeyboardArrowRight />} />
      </Link>
    ),
  }),
];

const SERVICE_TYPE_FILTERS = [
  { label: "All Services", value: "ALL" },
  { label: "Eu License", value: "EU_LICENSE" },
  { label: "Direct License", value: "DIRECT_LICENSE" },
  { label: "Action Guide", value: "ACTION_GUIDE" },
];

const STATUS_FILTERS = [
  { label: "All status", value: "ALL", color: null },
  { label: "Active", value: "ACTIVE", color: "fill-success" },
  { label: "Terminated", value: "TERMINATED", color: "fill-tonal-red-50" },
  { label: "Termination in process", value: "TERMINATION_PROCESS", color: "fill-tonal-dark-cream-30" },
];

export function AccountsTable() {
  const { paramValues, changeParam, changeParams } = useQueryFilter(["status", "service-type", "search", "page"]);

  const status = paramValues.status || STATUS_FILTERS[0]?.value;
  const serviceType = paramValues["service-type"] || SERVICE_TYPE_FILTERS[0]?.value;
  const search = paramValues.search || undefined;
  const page = paramValues.page ? Number(paramValues.page) : 1;

  const { data: customers, isLoading } = useQuery({
    queryKey: ["customers", { page, limit: 10, search, status, serviceType }],
    queryFn: async () => {
      const response = await getCustomers({
        page,
        limit: 10,
        search,
        status: status === "ALL" ? undefined : (status as "ACTIVE" | "TERMINATED" | "TERMINATION_PROCESS"),
        service_type: serviceType === "ALL" ? undefined : (serviceType as ContractType),
      });

      return response;
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const selectedServiceType = useMemo(() => {
    return SERVICE_TYPE_FILTERS.find((filter) => filter.value === serviceType) || SERVICE_TYPE_FILTERS[0];
  }, [serviceType]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  function handleChangeServiceType(newOrder: (typeof SERVICE_TYPE_FILTERS)[number]) {
    changeParams({ "service-type": newOrder.value, page: "1" });
  }

  const selectedStatus = useMemo(() => {
    return STATUS_FILTERS.find((filter) => filter.value === status) || STATUS_FILTERS[0];
  }, [status]);

  function handleChangeStatus(updatedStatus: (typeof STATUS_FILTERS)[number]) {
    changeParams({ status: updatedStatus.value, page: "1" });
  }

  function handleSearch(value: string) {
    changeParams({ search: value, page: "1" });
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-12">
        <DatatableSearch onSearch={handleSearch} placeholder="Search by user ID or email" />
        <div className="flex items-center gap-4">
          {/* Service Type Filter - Hidden for now */}
          {/* <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <Sort width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedServiceType.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {SERVICE_TYPE_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeServiceType(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                {filter.label}
              </DropdownItem>
            ))}
          </Dropdown> */}
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold text-base">
                <FilterAlt width={20} height={20} className="fill-support-blue" />
                <span className="ml-1 mr-2 mt-1 text-left">{selectedStatus.label}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
              </button>
            }
          >
            {STATUS_FILTERS.map((filter, idx) => (
              <DropdownItem
                key={idx}
                onClick={() => handleChangeStatus(filter)}
                className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
              >
                <div className="flex items-center gap-3">
                  {filter.color && <Elipse className={`flex-none size-3 ${filter.color}`} />}
                  {filter.label}
                </div>
              </DropdownItem>
            ))}
          </Dropdown>
        </div>
      </div>
      <AsyncPaginatedTable
        columns={columns}
        currentPage={page}
        isLoading={isLoading}
        data={customers?.customers || []}
        pages={customers?.pages || 0}
        onPageChange={(page) => {
          changeParam("page", page.toString());
        }}
        noResultsMessage={search ? `No results for "${search}" in Customer List` : "No customers found"}
      />
    </div>
  );
}
