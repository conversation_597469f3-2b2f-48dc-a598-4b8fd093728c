package de.interzero.oneepr.admin.settings.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating or updating a Setting.
 */
@Getter
@Setter
public class UpsertSettingDto extends BaseDto {

    @NotNull
    @Schema(
            description = "The value of the setting",
            example = "{\"key\": \"value\"}"
    )
    @JsonProperty("value")
    private String value;

    @NotNull
    @Schema(
            description = "The file id of the setting",
            example = "123"
    )
    @JsonProperty("term_or_condition_file_id")
    private String termOrConditionFileId;
}
