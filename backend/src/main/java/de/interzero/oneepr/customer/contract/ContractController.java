package de.interzero.oneepr.customer.contract;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.contract.dto.ContractResponseDto;
import de.interzero.oneepr.customer.contract.dto.CreateContractDto;
import de.interzero.oneepr.customer.contract.dto.FindAllContractDto;
import de.interzero.oneepr.customer.contract.dto.UpdateContractDto;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.CONTRACTS)
@Tag(
        name = "Contracts",
        description = "Endpoints for managing contracts"
)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER}) // Matches roles from the NestJS controller
@RequiredArgsConstructor
public class ContractController {

    private final ContractService contractService;

    private final ContractMapper contractMapper;

    /**
     * Finds all contracts based on optional filter criteria.
     * The response is a list of detailed contract DTOs.
     *
     * @param query DTO containing optional filters like customerId, status, and type.
     * @return A list of contracts matching the filters.
     */
    @GetMapping
    @Operation(summary = "Get all contracts")
    public List<ContractResponseDto> findAll(FindAllContractDto query) {
        return contractService.findAll(query);
    }

    /**
     * Finds a single contract by its ID, returning a fully detailed structure.
     *
     * @param id The ID of the contract.
     * @return A detailed DTO of the contract.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get a contract by id")
    public ContractResponseDto findOne(@PathVariable Integer id) {
        return contractService.findOne(id);
    }

    /**
     * Creates a new contract.
     *
     * @param data The DTO containing data for the new contract.
     * @return A detailed DTO of the newly created contract.
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Create a contract")
    public ContractResponseDto create(@RequestBody CreateContractDto data) {
        Contract createdContract = contractService.create(data);
        return contractMapper.toDto(createdContract);
    }

    /**
     * Updates an existing contract.
     *
     * @param id   The ID of the contract to update.
     * @param data The DTO with fields to update.
     * @return A detailed DTO of the updated contract.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update a contract")
    public ContractResponseDto update(@PathVariable String id,
                                      @RequestBody UpdateContractDto data) {
        try {
            Integer contractId = Integer.valueOf(id);
            AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
            Contract updatedContract = contractService.update(contractId, data, user);
            return contractMapper.toDto(updatedContract);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "ID must be a valid number");
        }
    }

    /**
     * Soft-deletes a contract by setting its deleted_at timestamp.
     *
     * @param id The ID of the contract to delete.
     * @return A success response entity.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a contract")
    public ResponseEntity<Void> remove(@PathVariable Integer id) {

            AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        contractService.remove(id, user);
        return ResponseEntity.noContent().build();
    }
}