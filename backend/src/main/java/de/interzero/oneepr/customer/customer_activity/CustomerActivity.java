package de.interzero.oneepr.customer.customer_activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;

@Entity
@Table(
        name = "customer_activities",
        schema = "public"
)
@Getter
@Setter
public class CustomerActivity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Integer id;


    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(
            name = "metadata",
            columnDefinition = "jsonb"
    )
    @JsonProperty("metadata")
    private Map<String, Object> metadata;


    @CreationTimestamp
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @UpdateTimestamp
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "customer_id",
            referencedColumnName = "id",
            nullable = false
    )
    @JsonProperty("customer")
    @JsonIgnore
    private Customer customer;

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return (this.customer != null) ? this.customer.getId() : null;
    }

    public enum Type {
        ACCOUNT_LOGIN,
        ACCOUNT_UPDATE_PASSWORD,
        ACCOUNT_UPDATE_EMAIL,
        ACCOUNT_UPDATE_ADDRESS,
        ACCOUNT_UPDATE_PAYMENT,
        CONTRACT_ADD_SERVICE,
        CONTRACT_UPDATE_SERVICE,
        CONTRACT_TERMINATION,
        CONTRACT_SERVICE_TERMINATION,
        REPORT_ADD,
        REPORT_UPDATE,
        DOCUMENT_UPLOAD,
        DOCUMENT_ANSWER,
        DOCUMENT_DOWNLOAD
    }
}