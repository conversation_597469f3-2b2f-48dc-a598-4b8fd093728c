package de.interzero.oneepr.admin.country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new country.
 */
@Getter
@Setter
public class CreateCountryDto extends BaseDto {

    @Schema(
            description = "Name of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "ISO code of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "Flag URL of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("flag_url")
    private String flagUrl;
}