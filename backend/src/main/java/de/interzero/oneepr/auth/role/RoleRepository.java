package de.interzero.oneepr.auth.role;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for managing {@link Role} entities.
 * Provides methods for querying roles by name and active status.
 */
public interface RoleRepository extends JpaRepository<Role, Integer> {

    /**
     * Finds a role by its name.
     *
     * @param name the name of the role
     * @return an {@link Optional} containing the {@link Role} if found, or empty if not
     */
    Optional<Role> findByName(String name);

    /**
     * Finds all roles that are active and not marked as deleted.
     *
     * @return a list of active {@link Role} entities
     */
    List<Role> findByIsActiveTrueAndDeletedAtIsNull();
}
