package de.interzero.oneepr.customer.license_packaging_service;

import de.interzero.oneepr.customer.license_packaging_service.dto.*;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.common.AuthUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

import static de.interzero.oneepr.common.string.Api.CUSTOMER_PACKAGING_SERVICES;

/**
 * License Packaging Service Controller for managing packaging service operations.
 * Converted from TypeScript license-packaging-service.controller.ts
 * Maintains exact same structure, variable names, and function names.
 */
@RestController
@RequestMapping(CUSTOMER_PACKAGING_SERVICES)
@RequiredArgsConstructor
@Tag(
        name = "packaging-services",
        description = "Packaging services management operations"
)
public class LicensePackagingServiceController {

    private final LicensePackagingServiceService licensePackagingService;

    /**
     * Get all packaging services with optional license_id filter
     * Equivalent to TypeScript: @Get() findAll(@Query("license_id") license_id?: string)
     *
     * @param licenseId Optional license ID to filter by (maintaining exact same parameter name)
     * @return List of LicensePackagingService entities
     */
    @GetMapping
    @Operation(summary = "Get all packaging services")
    public List<LicensePackagingService> findAll(@RequestParam(
            value = "license_id",
            required = false
    ) Integer licenseId) {
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.findAll(licenseId);
    }

    /**
     * Get a packaging service by id
     * Equivalent to TypeScript: @Get(":id") findOne(@Param("id") id: number, @User() user: AuthenticatedUser)
     *
     * @param id The ID of the packaging service (maintaining exact same parameter name)
     * @return The found LicensePackagingService
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get a packaging service by id")
    public LicensePackagingService findOne(@PathVariable("id") Integer id) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.findOne(id, user);
    }

    /**
     * Get a packaging service performance by id
     * Equivalent to TypeScript: @Get(":id/performance") performance(@Param("id") id: number, @Query() query: GetLicensePackagingServicePerformanceDto)
     *
     * @param id    The ID of the packaging service (maintaining exact same parameter name)
     * @param query The performance query parameters (maintaining exact same parameter name)
     * @return Performance data map
     */
    @GetMapping("/{id}/performance")
    @Operation(summary = "Get a packaging service performance by id")
    public PackagingServicePerformanceDto performance(@PathVariable("id") Integer id,
                                                      @RequestBody GetLicensePackagingServicePerformanceDto query) {
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.getPerformance(id, query);
    }

    /**
     * Get a packaging service turnover by id
     * Equivalent to TypeScript: @Get(":id/turnover") turnover(@Param("id") id: number, @Query() query: GetLicensePackagingServiceTurnoverDto)
     *
     * @param id    The ID of the packaging service (maintaining exact same parameter name)
     * @param query The turnover query parameters (maintaining exact same parameter name)
     * @return Turnover data list
     */
    @GetMapping("/{id}/turnover")
    @Operation(summary = "Get a packaging service turnover by id")
    public List<PackagingServiceTurnoverDto> turnover(@PathVariable("id") Integer id,
                                                      @Valid @RequestBody GetLicensePackagingServiceTurnoverDto query) {
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.getTurnover(id, query);
    }

    /**
     * Get a packaging service weight reported by id
     * Equivalent to TypeScript: @Get(":id/weight-reported") weightReported(@Param("id") id: number)
     *
     * @param id The ID of the packaging service (maintaining exact same parameter name)
     * @return Weight reported data list
     */
    @GetMapping("/{id}/weight-reported")
    @Operation(summary = "Get a packaging service weight reported by id")
    public List<PackagingServiceWeightReportedDto> weightReported(@PathVariable("id") Integer id) {
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.getWeightReported(id);
    }

    /**
     * Create a packaging service
     * Equivalent to TypeScript: @Post() create(@Body() data: CreateLicensePackagingServiceDto)
     *
     * @param data The DTO containing the data to create the service (maintaining exact same parameter name)
     * @return The created LicensePackagingService
     */
    @PostMapping
    @Operation(summary = "Create a packaging service")
    public LicensePackagingService create(@RequestBody @Valid CreateLicensePackagingServiceDto data) {
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.create(data);
    }

    /**
     * Update a packaging service
     * Equivalent to TypeScript: @Put(":id") update(@Param("id") id: number, @Body() data: UpdateLicensePackagingServiceDto, @User() user: AuthenticatedUser)
     *
     * @param id   The ID of the packaging service to update (maintaining exact same parameter name)
     * @param data The DTO containing the update data (maintaining exact same parameter name)
     * @return The updated LicensePackagingService
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update a packaging service")
    public LicensePackagingService update(@PathVariable("id") Integer id,
                                          @RequestBody UpdateLicensePackagingServiceDto data) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.update(id, data, user);
    }

    /**
     * Delete a packaging service
     * Equivalent to TypeScript: @Delete(":id") remove(@Param("id") id: number, @User() user: AuthenticatedUser)
     *
     * @param id The ID of the packaging service to remove (maintaining exact same parameter name)
     * @return The soft-deleted LicensePackagingService
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a packaging service")
    public LicensePackagingService remove(@PathVariable("id") Integer id) {
        // Get authenticated user - maintaining exact same logic as TypeScript @User() decorator
        AuthenticatedUser user = AuthUtil.getRelevantUserDetails();
        // Maintaining exact same logic as TypeScript
        return licensePackagingService.remove(id, user);
    }
}
