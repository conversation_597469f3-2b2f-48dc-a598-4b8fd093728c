"use client";

import { ModuleTitle } from "@/components/common/module-title";
import { Add, Dashboard, KeyboardArrowDown } from "@interzero/oneepr-react-ui/Icon";
import { DiscountCodesTable } from "./discount-codes-table";
import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useRouter } from "next/navigation";
import { MonthDatePickerWithRange } from "@/components/ui/month-date-picker";

export function DiscountCodesModule() {
  const router = useRouter();

  return (
    <ModuleContent>
      <ModuleTitle
        icon={Dashboard}
        title="Discount Code Manager"
        description="Manage and monitor all discount codes across the platform"
      />
      <div className="flex justify-between items-center mb-6">
        <Button
          variant="filled"
          color="yellow"
          size="iconSmall"
          leadingIcon={<Add />}
          onClick={() => router.push("/discount-codes/create")}
        >
          Add new discount
        </Button>
        <MonthDatePickerWithRange />
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6 mb-16">
        <div className="flex flex-col gap-6 p-8 rounded-3xl bg-tonal-blue-90">
          <div>
            <h3 className="text-xl font-bold text-primary">Revenue</h3>
          </div>
          <div className="space-y-2">
            <h4 className="text-6xl font-bold text-tonal-blue-40">€305.00</h4>
            <div className="flex items-center gap-2">
              <p className="text-sm text-primary">Via discount codes</p>
            </div>
            <div className="flex items-center gap-2">
              <KeyboardArrowDown width={20} className="fill-tonal-blue-40" />
              <span className="text-tonal-blue-40">0.5% more than august</span>
            </div>
          </div>
          <div className="pt-4 border-t border-gray-200 space-y-2">
            <h4 className="text-5xl font-bold text-tonal-red-40">€ 15.00</h4>
            <p className="text-sm text-primary">Reduction of revenue</p>
            <div className="flex items-center gap-2">
              <KeyboardArrowDown width={20} className="fill-tonal-red-40" />
              <span className="text-tonal-red-40">0.2% less than august</span>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-6 p-8 rounded-3xl bg-tonal-green-90">
          <div>
            <h3 className="text-xl font-bold text-primary">Leads generated</h3>
          </div>
          <div className="space-y-2">
            <h4 className="text-6xl font-bold text-tonal-green-40">250</h4>
            <p className="text-sm text-primary">Generate by coupons</p>
            <div className="flex items-center gap-2">
              <KeyboardArrowDown width={20} className="fill-tonal-green-40" />
              <span className="text-tonal-green-40">0.5% more than august</span>
            </div>
          </div>
          <div className="pt-4 border-t border-gray-200 space-y-2">
            <h4 className="text-5xl font-bold text-tonal-green-40">48</h4>
            <p className="text-sm text-primary">Generate by links</p>
            <div className="flex items-center gap-2">
              <KeyboardArrowDown width={20} className="fill-tonal-green-40" />
              <span className="text-tonal-green-40">0.5% more than august</span>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-6 p-8 rounded-3xl bg-tonal-red-90">
          <div>
            <h3 className="text-xl font-bold text-primary">Drop purchase</h3>
          </div>
          <div className="space-y-2">
            <h4 className="text-6xl font-bold text-tonal-red-40">25</h4>
            <p className="text-sm text-primary">Customers didn&apos;t finish the purchase</p>
            <div className="flex items-center gap-2">
              <KeyboardArrowDown width={20} className="fill-tonal-red-40" />
              <span className="text-tonal-red-40">0.2% less than august</span>
            </div>
          </div>
        </div>
      </div>
      <DiscountCodesTable />
    </ModuleContent>
  );
}
